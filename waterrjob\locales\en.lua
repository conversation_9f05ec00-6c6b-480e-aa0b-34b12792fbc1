Locales['en'] = {
  -- Global menus
  ['cloakroom']                 = 'غرفة تبديل الملابس',
  ['cloak_change']              = '<FONT FACE="A9eelsh">ﻞﻳﺪﺒﺘﻟﺍ ﺔﻓﺮﻏ ﻝﻮﺧﺪﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['citizen_wear']              = '<span style="color:FF0E0E">تسجيل خروج 👦🏻</span>',
  ['job_wear']                  = '<span style="color:00EE4F">تسجيل دخول 👷🏽</span>',
  ['bank_deposit_returned']     = '<center><b style="color:#0BAF5F;font-size:26px;"> تأمين المركبة </b></center> <br /><br /><div align="right"> <b style="color:White;font-size:20px">  تم إرجاع مبلغ التأمين المخصوم منك سابقا ',
  ['bank_deposit_taken']        = '<center><b style="color:#0BAF5F;font-size:26px;"> تأمين المركبة </b></center> <br /><br /><div align="right"> <b style="color:White;font-size:20px">  تم خصم مبلغ تأمين المركبة منك ',
  ['foot_work']                 = 'يجب ان تكون واقف على قدمك حتى تبدأ العمل',
  ['next_point']                = 'اذهب إلى مكان العمل التالي بعد الانتهاء من هذا المكان',
  ['security_deposit']          = '%s',
  ['not_your_vehicle']          = '<FONT FACE="A9eelsh">ﻖﺋﺎﺴﻟﺍ ﻥﺎﻜﻣ ﻲﻓ ﺖﺴﻟ ﻭﺍ ﻚﻟ ﺖﺴﻴﻟ ﺔﺒﻛﺮﻤﻟﺍ',
  ['in_vehicle']                = '<FONT FACE="A9eelsh">ﺔﺒﻛﺮﻣ ﻞﺧﺍﺩ ﻥﻮﻜﺗ ﻥﺍ ﺐﺠﻳ',
  ['wrong_point']               = '<FONT FACE="A9eelsh">ﺊﻃﺎﺧ ﻞﻴﺻﻮﺗ ﻥﺎﻜﻣ ﻲﻓ ﺖﻧﺍ',
  ['max_limit']                 = 'لايمكنك حمل اكثر من <font color=gold>%s <font color=white>في الحقيبة',
  ['not_enough']                = 'ليس لديك عدد كافي من العدد الاكمال المهمة',
  ['spawn_veh']                 = 'spawn vehicle',
  ['spawn_veh_button']          = '<FONT FACE="A9eelsh">ﻞﻤﻋ ﺔﺒﻛﺮﻣ ﺬﺧﻷ ~y~E~w~ ﻂﻐﺿﺍ',
  ['spawn_truck_button']        = '<FONT FACE="A9eelsh">ﻞﻤﻋ ﺔﺒﻛﺮﻣ ﺬﺧﻷ ~y~E~w~ ﻂﻐﺿﺍ',
  ['spawn_blocked']             = '<center><b style="color:#ea1f1f;font-size:18px;"> توجد مركبة في المنطقة المخصصة تعيق إستلام المركبة ',
  ['service_vh']                = 'service vehicle',
  ['return_vh']                 = 'vehicle return',
  ['return_vh_button']          = '<FONT FACE="A9eelsh">ﻞﻤﻌﻟﺍ ﺔﺒﻛﺮﻣ ﻉﺎﺟﺭﻹ ~y~E~w~ ﻂﻐﺿﺍ',
  ['delivery_point']            = 'delivery point',
  ['delivery']                  = 'التصدير',
  ['public_enter']              = 'ﻰﻨﺒﻤﻟﺍ ﻝﻮﺧﺪﻟ E ﻂﻐﺿﺍ',
  ['public_leave']              = 'ﻰﻨﺒﻤﻟﺍ ﻦﻣ ﺝﻭﺮﺨﻠﻟ E ﻂﻐﺿﺍ',

  -- Lumber Jack job
  ['lj_locker_room']            = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻤﻟﺍ ﺔﻓﺮﻏ ﺍ ﺏﺎﺸﺧﻷﺍ",
  ['lj_mapblip']                = "<FONT FACE='A9eelsh'>ﺐﺸﺨﻟﺍ ﻊﻤﺟ ﺍ ﺏﺎﺸﺧﻷﺍ",
  ['lj_wood']                   = 'خشب',
  ['lj_pickup']                 = '<FONT FACE="A9eelsh">ﺐﺸﺨﻟﺍ ﻊﻤﺠﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['lj_cutwood2']                = 'خشب مقطع',
  ['lj_cutwood_button']         = '<FONT FACE="A9eelsh">ﺐﺸﺨﻟﺍ ﻊﻄﻘﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['lj_cutwood']                  = "<FONT FACE='A9eelsh'>ﺐﺸﺨﻟﺍ ﻊﻄﻗ ﺍ ﺏﺎﺸﺧﻷﺍ",
  ['lj_planks']                 = 'حزمة خشب',
  ['lj_board']                = "<FONT FACE='A9eelsh'>ﺐﺸﺨﻟﺍ ﻂﺑﺭ ﺍ ﺏﺎﺸﺧﻷﺍ",
  ['lj_pick_boards']            = '<FONT FACE="A9eelsh">ﺐﺸﺨﻟﺍ ﺡﺍﻮﻟﺃ ﻂﺑﺮﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['lj_deliver_button']         = '<FONT FACE="A9eelsh">ﺐﺸﺨﻟﺍ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['lj_delivery_point']         = "<FONT FACE='A9eelsh'>ﺐﺸﺨﻟﺍ ﻊﻴﺑ ﺍ ﺏﺎﺸﺧﻷﺍ",
  ['fueler_kit']                    = 'عدة عمل أخشاب',

  -- Fisherman
  ['fm_fish_locker']            = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻤﻟﺍ ﺔﻓﺮﻏ ﺍ ﻙﺎﻤﺳﻷﺍ",
  ['fm_fish']                   = 'سمك',
  ['fm_shark']                  = 'قرش', 
  ['fm_turtle']                 = 'سلحفاة',    
  ['fm_fish_kit']               = 'عدة عمل الأسماك',
  ['fm_fish_area']              = '~t~(~o~3~t~) ~w~fishing area',
  ['fm_fish_button']            = 'press ~INPUT_PICKUP~ to fish.',
  ['fm_spawnboat_title']        = '~t~(~o~2~t~) ~w~spawn boat',
  ['fm_spawnboat']              = 'press ~INPUT_PICKUP~ to call the boat.',
  ['fm_boat_title']             = 'boat',
  ['fm_boat_return_title']      = 'boat return',
  ['fm_boat_return_button']     = 'Press ~INPUT_PICKUP~ to return the boat.',
  ['fm_deliver_fish']           = '<FONT FACE="A9eelsh">ﻚﻤﺴﻟﺍ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['fm_delivery_point']         = "<FONT FACE='A9eelsh'>ﻚﻤﺴﻟﺍ ﻊﻴﺑ ﺍ ﻙﺎﻤﺳﻷﺍ",
  ['fm_deliver_turtle']         = '<FONT FACE="A9eelsh">ﻒﺣﻼﺴﻟﺍ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['fm_delivery_point_turtle']  = "<FONT FACE='A9eelsh'>ﻒﺣﻼﺴﻟﺍ ﻊﻴﺑ ﺍ ﻙﺎﻤﺳﻷﺍ",  
  ['fm_deliver_shark']         = '<FONT FACE="A9eelsh">ﺵﺮﻘﻟﺍ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['fm_delivery_point_shark']  = "<FONT FACE='A9eelsh'>ﺵﺮﻘﻟﺍ ﻊﻴﺑ ﺍ ﻙﺎﻤﺳﻷﺍ",    

  -- Fuel
  ['f_oil_refiner']             = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻤﻟﺍ ﺔﻓﺮﻏ ﺍ ﻂﻔﻨﻟﺍ ",
  ['f_drill_oil']               = "<FONT FACE='A9eelsh'>ﻂﻔﻨﻟﺍ ﺝﺍﺮﺨﺘﺳﺇ ﺍ ﻂﻔﻨﻟﺍ 1",
  ['f_drill_oil2']              = "<FONT FACE='A9eelsh'>ﺯﺎﻐﻟﺍ ﺝﺍﺮﺨﺘﺳﺇ ﺍ ﺯﺎﻐﻟﺍ 1",

  ['f_petrol']                  = 'نفط خام',
  ['f_petrol_raffin']           = 'نفط مكرر',
  ['f_essence']                 = 'وقود',
  
  ['f_gas']                   	= 'غاز غير معالج',
  ['f_gas_refine']          	= 'غاز معالج',
  ['f_gazbottle']          		= 'اسطوانة غاز',
  

  ['fueler_kit']                    = 'عدة عمل النفط والغاز',
  ['f_drillbutton']             = '<FONT FACE="A9eelsh">ﺝﺍﺮﺨﺘﺳﻺﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['f_fuel_refine']             = "<FONT FACE='A9eelsh'>ﻂﻔﻨﻟﺍ ﺮﻳﺮﻜﺗ ﺍ ﻂﻔﻨﻟﺍ 2",
  ['f_fuel_refine2']             = "<FONT FACE='A9eelsh'>ﺯﺎﻐﻟﺍ ﺔﺠﻟﺎﻌﻣ ﺍ ﺯﺎﻐﻟﺍ 2",
  ['f_refine_fuel_button']      = '<FONT FACE="A9eelsh">ﺮﻳﺮﻜﺘﻠﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['f_fuel_mixture']            = "<FONT FACE='A9eelsh'>ﻂﻔﻨﻟﺍ ﻞﻳﻮﺤﺗ ﺍ ﻂﻔﻨﻟﺍ 3",
  ['f_fuel_mixture2']            = "<FONT FACE='A9eelsh'>ﺯﺎﻐﻟﺍ ﻞﻳﻮﺤﺗ ﺍ ﺯﺎﻐﻟﺍ 3",
  
  ['f_fuel_mixture_button']     = '<FONT FACE="A9eelsh">ﻞﻳﻮﺤﺘﻠﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['f_deliver_gas']             = "<FONT FACE='A9eelsh'>ﺩﻮﻗﻮﻟﺍ ﻊﻴﺑ ﺍ ﻂﻔﻨﻟﺍ",
  ['f_deliver_gas2']             = "<FONT FACE='A9eelsh'>ﺯﺎﻐﻟﺍ ﻊﻴﺑ ﺍ ﺯﺎﻐﻟﺍ",
  ['f_deliver_gas_button']      = '<FONT FACE="A9eelsh">ﻊﻴﺒﻠﻟ ~y~E~w~ ﻂﻐﺿﺍ',

  -- Miner
  ['m_miner_locker']            = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻤﻟﺍ ﺔﻓﺮﻏ ﺍ ﻥﺩﺎﻌﻤﻟﺍ",
  ['m_rock']                    = "<FONT FACE='A9eelsh'>ﺮﺠﺤﻟﺍ ﺐﻴﻘﻨﺗ ﺍ ﻥﺩﺎﻌﻤﻟﺍ",
  ['m_rock2']                    = 'حجر',
  ['m_pickrocks']               = '<FONT FACE="A9eelsh">ﺮﺠﺤﻟﺍ ﻦﻋ ﺐﻴﻘﻨﺘﻠﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['m_washrock']                = "<FONT FACE='A9eelsh'>ﺮﺠﺤﻟﺍ ﻒﻴﻈﻨﺗ ﺍ ﻥﺩﺎﻌﻤﻟﺍ",
  ['m_washrock2']                = 'حجر نظيف',
  ['m_rock_button']             = '<FONT FACE="A9eelsh">ﺮﺠﺤﻟﺍ ﻒﻴﻈﻨﺘﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['m_rock_smelting']           = "<FONT FACE='A9eelsh'>ﺮﺠﺤﻟﺍ ﺮﻬﺻ ﺍ ﻥﺩﺎﻌﻤﻟﺍ",
  ['m_copper']                  = 'نحاس',
  ['m_sell_copper']             = "<FONT FACE='A9eelsh'>ﺱﺎﺤﻨﻟﺍ ﻊﻴﺑ ﺍ ﻥﺩﺎﻌﻤﻟﺍ",
  ['m_deliver_copper']          = '<FONT FACE="A9eelsh">ﺱﺎﺤﻨﻟﺍ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['m_iron']                    = 'حديد',
  ['m_sell_iron']               = "<FONT FACE='A9eelsh'>ﺪﻳﺪﺤﻟﺍ ﻊﻴﺑ ﺍ ﻥﺩﺎﻌﻤﻟﺍ",
  ['m_deliver_iron']            = '<FONT FACE="A9eelsh">ﺪﻳﺪﺤﻟﺍ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['m_gold']                    = 'ذهب',
  ['m_sell_gold']               = "<FONT FACE='A9eelsh'>ﺐﻫﺬﻟﺍ ﻊﻴﺑ ﺍ ﻥﺩﺎﻌﻤﻟﺍ",
  ['m_deliver_gold']            = '<FONT FACE="A9eelsh">ﺐﻫﺬﻟﺍ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['m_diamond']                 = 'الماس',
  ['m_sell_diamond']            = "<FONT FACE='A9eelsh'>ﺱﺎﻤﻟﺍ ﻊﻴﺑ ﺍ ﻥﺩﺎﻌﻤﻟﺍ",
  ['m_deliver_diamond']         = '<FONT FACE="A9eelsh">ﺱﺎﻤﻟﺍ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['m_melt_button']             = '<FONT FACE="A9eelsh">ﺮﺠﺤﻟﺍ ﺮﻬﺼﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['miner_kit']             	= 'عدة عمل المعادن',

  -- Slaughterer
  ['s_slaughter_locker']        = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻤﻟﺍ ﺔﻓﺮﻏ ﺍ ﻦﺟﺍﻭﺪﻟﺍ",
  ['s_hen']                     = "<FONT FACE='A9eelsh'>ﺝﺎﺟﺪﻟﺍ ﺪﻴﺻ ﺍ ﻦﺟﺍﻭﺪﻟﺍ",
  ['s_alive_chicken']           = 'دجاج حي',
  ['s_catch_hen']               = '<FONT FACE="A9eelsh">ﺝﺎﺟﺪﻟﺍ ﺪﻴﺼﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['s_slaughtered_chicken']     = 'دجاج ذبح حلال',
  ['s_chop_animal']             = '<FONT FACE="A9eelsh">ﺝﺎﺟﺪﻟﺍ ﺢﺑﺬﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['s_slaughtered']             = "<FONT FACE='A9eelsh'>ﺝﺎﺟﺪﻟﺍ ﺢﺑﺫ ﺍ ﻦﺟﺍﻭﺪﻟﺍ",
  ['s_package']                 = "<FONT FACE='A9eelsh'>ﻒﻴﻠﻐﺘﻟﺍ ﺍ ﻦﺟﺍﻭﺪﻟﺍ",
  ['s_packagechicken']          = 'دجاج مغلف',
  ['s_unpackaged']              = 'دجاج ذبح حلال',
  ['s_unpackaged_button']       = '<FONT FACE="A9eelsh">ﺝﺎﺟﺪﻟﺍ ﻒﻴﻠﻐﺘﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['s_deliver']                 = '<FONT FACE="A9eelsh">ﺝﺎﺟﺪﻟﺍ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['s_delivery_point']          = "<FONT FACE='A9eelsh'>ﺝﺎﺟﺪﻟﺍ ﻊﻴﺑ ﺍ ﻦﺟﺍﻭﺪﻟﺍ",
  ['slaughterer_kit']          = 'عدة عمل الدواجن',

  -- semant
  ['s_semant_locker']        = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻤﻟﺍ ﺔﻓﺮﻏ  ﺖﻨﻤﺳﻷﺍ",
  ['s_trbhghermalg']         = "<FONT FACE='A9eelsh'>ﺏﺍﺮﺘﻟﺍ ﻊﻨﺼﻣ ﺖﻨﻤﺳﻷﺍ ",
  ['s_trbh_ghermalg']        = 'تربة غير معالجة',
  ['s_semant_take']          = '<FONT FACE="A9eelsh">ﺔﺑﺮﺘﻟﺍ ﻊﻨﺼﻟ  ~y~E~w~ ﻂﻐﺿﺍ',
  ['s_trbhmalg1']            = 'تربة معالجة',
  ['s_trbhmalg2']            = '<FONT FACE="A9eelsh">ﺖﻨﻤﺳﻷﺍ ﻊﻨﺼﻟ  ~y~E~w~ ﻂﻐﺿﺍ',
  ['s_trbhmalg']             = "<FONT FACE='A9eelsh'>ﺖﻨﻤﺳﻷﺍ ﻊﻨﺼﻟ ﺖﻨﻤﺳﻷﺍ  ",
  ['s_semantfinsh']          = "<FONT FACE='A9eelsh'>ﺖﻨﻤﺳﻷﺍ ﻒﻴﻠﻐﺘﻟ ﺖﻨﻤﺳﻷﺍ ",
  ['s_semant']              = 'اسمنت  روشن',
  ['s_semantfinsh1']         = 'تربة معالجة',
  ['s_semantfinsh_button']   = '<FONT FACE="A9eelsh">ﺖﻨﻤﺳﻷﺎ ﻔﻴﻠﻐﺘﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['C_deliver']              = '<FONT FACE="A9eelsh">ﺖﻨﻤﺳﻷﺍ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['C_delivery_point']       = "<FONT FACE='A9eelsh'>ﺖﻨﻤﺳﻷﺍ ﻊﻴﺑ ﺍ ﺖﻨﻤﺳﻷﺍ",
  ['semant_kit']             = 'عدة عمل الأسمنت', 


  -- alaalaf
  ['al_alaalaf_locker']       = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻤﻟﺍ ﺔﻓﺮﻏ  ﻑﻼﻋﻷﺍ",
  ['alalaaf1']                = "<FONT FACE='A9eelsh'>ﺮﻴﻌﺸﻟﺍ ﺝﺎﺘﻧﺇ ﻑﻼﻋﻷﺍ",
  ['al_shaer']               = 'شعير خام',
  ['al_aalaf']               = '<FONT FACE="A9eelsh">ﻡﺎﺨﻟﺍ ﺮﻴﻌﺸﻟﺍ ﺝﺎﺘﻧﻹ  ~y~E~w~ ﻂﻐﺿﺍ',
  ['al_alaalafed']           = 'شعير مخلوط',
  ['al_shaerkham1']           = '<FONT FACE="A9eelsh"> ﻁﻮﻠﺨﻤﻟﺍ ﺮﻴﻌﺸﻟﺍ ﺝﺎﺘﻧﻷ ~y~E~w~ ﻂﻐﺿﺍ',
  ['al_alaalafedd']            = "<FONT FACE='A9eelsh'>ﻑﻼﻋﻷﺍ ﺝﺎﺘﻧﻹ ﻑﻼﻋﻷﺍ ",
  ['al_mkaabatshaer']         = "<FONT FACE='A9eelsh'>ﺮﻴﻌﺸﻟﺍ ﺕﺎﺒﻌﻜﻣ ﺝﺎﺘﻧﻹ ",
  ['al_mkaabatshaer2']        = 'مكعبات شعير ',
  ['al_mkaabatshaer3']        = 'شعير مخلوط',
  ['al_mkaabatshaer_button']  = '<FONT FACE="A9eelsh">ﺮﻴﻌﺸﻟﺍ ﺕﺎﺒﻌﻜﻣ ﺝﺎﺘﻧﻹ  ~y~E~w~ ﻂﻐﺿﺍ',
  ['al_deliver']              = '<FONT FACE="A9eelsh">ﺮﻴﻌﺸﻟﺍ ﺕﺎﺒﻌﻜﻣ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['al_delivery_point']       = "<FONT FACE='A9eelsh'>ﻑﻼﻋﻷﺍ ﻊﻴﺑ ﺍ  ﻑﻼﻋﻷﺍ",
  ['alaalaf_kit']             = 'عدة عمل الأعلاف',  
  
  
  -- waterr
  ['wa_waterr_locker']        = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻤﻟﺍ ﺔﻓﺮﻏ  ﺓﺎﻴﻤﻟﺍ",
  ['wa_mloth1']               = "<FONT FACE='A9eelsh'>ﺓﺎﻴﻤﻟﺍ ﺔﺌﺒﻌﺘﻟ ﺓﺎﻴﻤﻟﺍ ",
  ['wa_mloth3']               = 'مياة ملوثة',
  ['wa_waterrmloth']          = '<FONT FACE="A9eelsh">ﺔﺛﻮﻠﻣ ﺓﺎﻴﻣ ﺔﺌﺒﻌﺘﻟ  ~y~E~w~ ﻂﻐﺿﺍ',
  ['wa_waterrghermohlaa1']    = ' مياة غير معالجة',
  ['wa_waterrghermohlaa4']    = '<FONT FACE="A9eelsh">ﺓﺎﻴﻤﻟﺍ ﺔﺠﻟﺎﻌﻤﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['wa_waterrghermohlaa']     = "<FONT FACE='A9eelsh'>ﺓﺎﻴﻤﻟﺍ ﺔﺠﻟﺎﻌﻤﻟ ﺓﺎﻴﻤﻟﺍ ",
  ['wa_mohlaa2']              = "<FONT FACE='A9eelsh'>ﺓﺎﻴﻤﻟﺍ ﺔﻴﻠﺤﺗ ",
  ['wa_mohlaa1']              = 'مياة محلاة',
  ['wa_mohlaa4']              = 'مياة غير محلاة',
  ['wa_wa_mohlaa_button']     = '<FONT FACE="A9eelsh">ﺓﺎﻴﻤﻟﺍ ﺔﻴﻠﺤﺘﻟ  ~y~E~w~ ﻂﻐﺿﺍ',
  ['wa_deliver']              = '<FONT FACE="A9eelsh">ﺓﺎﻴﻤﻟﺍ ﻊﻴﺒﻟ  ~y~E~w~ ﻂﻐﺿﺍ',
  ['wa_delivery_point']       = "<FONT FACE='A9eelsh'>ﺓﺎﻴﻤﻟﺍ ﻊﻴﺑ ﺍ ﺓﺎﻴﻤﻟﺍ",
  ['waterr_kit']              = 'عدة عمل المياة',    


  -- tmraa
  ['t_tmraa_locker']    = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻤﻟﺍ ﺔﻓﺮﻏ ﺭﻮﻤﺘﻟﺍ",
  ['tm_gdf']    = "<FONT FACE='A9eelsh'>ﺭﻮﻤﺘﻟﺍ ﻒﻄﻗ ﺭﻮﻤﺘﻟﺍ",
  ['tm_tmrfasd']   = 'عجوة غير ناضجة',
  ['t_take_tmrfasd']   = '<FONT FACE="A9eelsh">ﺭﻮﻤﺘﻟﺍ ﻒﻄﻘﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['t_tmraahmalag'] = 'عجوة ناضجة',
  ['t_tmrah_moalighh']  = '<FONT FACE="A9eelsh">ﺮﻤﺘﻟﺍ ﺔﺠﻟﺎﻌﻤﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['tm_tmraaahmaolgah']  = "<FONT FACE='A9eelsh'>ﺭﻮﻤﺘﻟﺍ ﺔﺠﻟﺎﻌﻣ",
  ['t_package']  = "<FONT FACE='A9eelsh'>ﺭﻮﻤﺘﻟﺍ ﺔﺌﺒﻌﺘﻟ",
  ['t_packagetmor']    = 'عجوة مغلفة',
  ['t_unpackaged']   = 'عجوة ناضجة',
  ['t_unpackaged_button'] = '<FONT FACE="A9eelsh">ﺭﻮﻤﺘﻟﺍ ﺔﺌﺒﻌﺘﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['t_deliver']     = '<FONT FACE="A9eelsh">ﺭﻮﻤﺘﻟﺍ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['t_delivery_point']  = "<FONT FACE='A9eelsh'>ﺭﻮﻤﺘﻟﺍ ﻊﻴﺑ ﺍ ﺭﻮﻤﺘﻟﺍ",
  ['tmraa_kit']          = 'عدة عمل تمور',  


  -- ekctrec
  ['e_ekctrec_locker']       = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻤﻟﺍ ﺔﻓﺮﻏ ﺀﺎﺑﺮﻬﻜﻟﺍ",
  ['ek_mosl']                = "<FONT FACE='A9eelsh'>ﺱﺎﺤﻨﻟﺍ ﻊﻤﺟ ﺱﺎﺤﻨﻟﺍ",
  ['ek_mosl1']               = 'موصل نحاسي',
  ['t_take_ekrfasd']         = '<FONT FACE="A9eelsh">ﺱﺎﺤﻨﻟﺍ ﻊﻤﺠﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['ek_wslaakhrb1']          = 'وصلة كهربائية',
  ['ekwslaakhrbb']           = '<FONT FACE="A9eelsh">ﺱﺎﺤﻨﻟﺍ ﻞﻳﻮﺤﺗ  ~y~E~w~ ﻂﻐﺿﺍ',
  ['ek_wslaakhrb']           = "<FONT FACE='A9eelsh'>ﺱﺎﺤﻨﻟﺍ ﻞﻳﻮﺤﺗ ",
  ['e_electrek']             = "<FONT FACE='A9eelsh'>ﺡﺎﺒﺼﻤﻟﺍ ﻊﻨﺻ ",
  ['e_electrek1']            = 'مصباح كهربائي',
  ['el_wslaakhrba']          = 'وصلة كهربائية',
  ['e_elketreck1_button']    = '<FONT FACE="A9eelsh">ﺡﺎﺒﺼﻤﻟﺍ ﻊﻨﻴﺼﺘﻟ  ~y~E~w~ ﻂﻐﺿﺍ',
  ['e_deliver']              = '<FONT FACE="A9eelsh">ﺡﺎﺒﺼﻤﻟﺍ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['ek_delivery_point']      = "<FONT FACE='A9eelsh'>ﺡﺎﺒﺼﻤﻟﺍ ﻊﻴﺑ ﺍ ﺡﺎﺒﺼﻤﻟﺍ",
  ['ekctrec_kit']          = 'عدة عمل كهرباء',
    
  -- vegetables 
  ['v_vegetables_locker']        = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻤﻟﺍ ﺔﻓﺮﻏ ﺍ ﺕﺍﻭﺮﻀﺨﻟﺍ",
  ['v_hen']                     = "<FONT FACE='A9eelsh'>1 ﺕﺍﻭﺮﻀﺨﻟﺍ ﻊﻤﺟ | ﺕﺍﻭﺮﻀﺨﻟﺍ",
  ['v__ready']           = 'سلة خضروات جاهزة للبيع',
  ['v_catch_ready']               = '<FONT FACE="A9eelsh">ﺕﺍﻭﺮﻀﺨﻟﺍ ﻊﻤﺠﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['v_deliver']                 = '<FONT FACE="A9eelsh">ﺕﺍﻭﺮﻀﺨﻟﺍ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['v_delivery_point']          = "<FONT FACE='A9eelsh'>2 ﺔﻠﺴﻟﺍ ﻊﻴﺑ | ﺕﺍﻭﺮﻀﺨﻟﺍ",
  ['vegetables_kit']          = 'عدة عمل الخضروات',

  ['dd_dress_locker']           = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻤﻟﺍ ﺔﻓﺮﻏ ﺍ ﻞﻴﻓﺍﺮﺗ ﻮﺟﺭﺎﻛ",
  ['dd_wool']                   = "<FONT FACE='A9eelsh'>ﻑﻮﺼﻟﺍ ﻊﻤﺟ ﺍ ﺔﺸﻤﻗﻷﺍ",
  ['dd_wool2']                   = 'صوف',
  ['dd_pickup']                 = '<FONT FACE="A9eelsh">ﻑﻮﺼﻟﺍ ﻊﻤﺠﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['dd_fabric']                 = "<FONT FACE='A9eelsh'>ﻑﻮﺼﻟﺍ ﺞﺴﻧ ﺍ ﺔﺸﻤﻗﻷﺍ",
  ['dd_fabric2']                 = 'قماش',
  ['dd_makefabric']             = '<FONT FACE="A9eelsh">ﻑﻮﺼﻟﺍ ﺞﺴﻨﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['dd_clothing']               = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻣ ﺔﻃﺎﻴﺧ ﺍ ﺔﺸﻤﻗﻷﺍ",
  ['dd_clothing2']               = 'ملابس',
  ['dd_makeclothing']           = '<FONT FACE="A9eelsh">ﺲﺑﻼﻣ ﺔﻃﺎﻴﺨﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['dd_deliver_clothes']        = '<FONT FACE="A9eelsh">ﺲﺑﻼﻤﻟﺍ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['dd_delivery_point']         = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻤﻟﺍ ﻊﻴﺑ ﺍ ﺔﺸﻤﻗﻷﺍ",
  ['tailor_kit']          		= 'عدة عمل ملابس',

  -- Dress Designer
  ['dd_dress_locker']           = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻤﻟﺍ ﺔﻓﺮﻏ ﺍ ﺔﺸﻤﻗﻷﺍ",
  ['dd_wool']                   = "<FONT FACE='A9eelsh'>ﻑﻮﺼﻟﺍ ﻊﻤﺟ ﺍ ﺔﺸﻤﻗﻷﺍ",
  ['dd_wool2']                   = 'صوف',
  ['dd_pickup']                 = '<FONT FACE="A9eelsh">ﻑﻮﺼﻟﺍ ﻊﻤﺠﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['dd_fabric']                 = "<FONT FACE='A9eelsh'>ﻑﻮﺼﻟﺍ ﺞﺴﻧ ﺍ ﺔﺸﻤﻗﻷﺍ",
  ['dd_fabric2']                 = 'قماش',
  ['dd_makefabric']             = '<FONT FACE="A9eelsh">ﻑﻮﺼﻟﺍ ﺞﺴﻨﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['dd_clothing']               = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻣ ﺔﻃﺎﻴﺧ ﺍ ﺔﺸﻤﻗﻷﺍ",
  ['dd_clothing2']               = 'ملابس',
  ['dd_makeclothing']           = '<FONT FACE="A9eelsh">ﺲﺑﻼﻣ ﺔﻃﺎﻴﺨﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['dd_deliver_clothes']        = '<FONT FACE="A9eelsh">ﺲﺑﻼﻤﻟﺍ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['dd_delivery_point']         = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻤﻟﺍ ﻊﻴﺑ ﺍ ﺔﺸﻤﻗﻷﺍ",
  ['tailor_kit']          		= 'عدة عمل ملابس',
  
  -- Farmer
  ['ff_farmer_locker']           = "<FONT FACE='A9eelsh'>ﺲﺑﻼﻤﻟﺍ ﺔﻓﺮﻏ ﺍ ﻉﺭﺍﺰﻤﻟﺍ",
  ['ff_farm']                   = "<FONT FACE='A9eelsh'>ﺐﻨﻌﻟﺍ ﻒﻄﻗ ﺍ ﻉﺭﺍﺰﻤﻟﺍ",
  ['ff_grape']                   = 'عنب',
  ['ff_pickup']                 = '<FONT FACE="A9eelsh">ﺐﻨﻌﻟﺍ ﻒﻄﻘﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['ff_juice']                 = "<FONT FACE='A9eelsh'>ﺮﻴﺼﻋ ﻊﻨﺻ ﺍ ﻉﺭﺍﺰﻤﻟﺍ",
  ['ff_juice2']                 = 'عصير عنب',
  ['ff_makejuice']             = '<FONT FACE="A9eelsh">ﺮﻴﺼﻋ ﻊﻨﺼﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['ff_wine']               = "<FONT FACE='A9eelsh'>ﺮﻤﺧ ﻊﻨﺻ ﺍ ﻉﺭﺍﺰﻤﻟﺍ",
  ['ff_wine2']               = 'خمر',
  ['ff_makewine']           = '<FONT FACE="A9eelsh">ﺮﻤﺧ ﻊﻨﺼﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['ff_deliver_juice']        = '<FONT FACE="A9eelsh">ﺐﻨﻌﻟﺍ ﺮﻴﺼﻋ ﻊﻴﺒﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['ff_delivery_point']         = "<FONT FACE='A9eelsh'>ﻊﻴﺒﻟﺍ ﺍ ﻉﺭﺍﺰﻤﻟﺍ",
  ['tailor_kit']          		= 'عدة عمل ملابس',  
  
  -- Reporter
  ['reporter_name']             = 'Blaine News',
  ['reporter_garage']           = 'Press ~INPUT_PICKUP~ to spawn ~y~VAN.',
   ['no_players_nearby'] = 'لايوجد لاعب قريب منك',
   ['cam'] = 'كاميرا',
   ['mic'] = 'مايك',
   ['bmic'] = 'بوم مايك',
   ['fine'] = 'فواتير️',
   ['contract'] = 'عقود',
   ['other'] = 'آخر',
   ['not_onduty'] = 'انت غير مسجل دخول في الخدمة',
   ['reporter_menu'] = 'جريدة ميلارومانو',
   ['reporter_cloakroom'] = 'جريدة روما',
   ['fine_total'] = 'فاتورة: %s - من الصحفي: %s',
  }
