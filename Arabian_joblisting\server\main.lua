ESX = nil
local availableJobs = {}

TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

MySQL.ready(function()
    MySQL.Async.fetchAll('SELECT name, label FROM jobs WHERE whitelisted = @whitelisted', {
        ['@whitelisted'] = false
    }, function(result)
        for i=1, #result, 1 do
            table.insert(availableJobs, {
                job = result[i].name,
                label = result[i].label,
                grade   = result[i].grade
            })
        end
    end)
end)

local disroles = {
    ["1384430613422936107"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - جندي", 0},
    ["1384430612319961179"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - جندي اول", 1},
    ["1384430611187503164"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - عريف", 2},
    ["1384430610130407444"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - وكيل رقيب", 3},
    ["1384430609040146572"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - رقيب", 4},
    ["1384430607794176110"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - رقيب اول", 5},
    ["1384430606385020998"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - رئيس رقباء", 6},
    ["1384430605223067688"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - ملازم", 7},
    ["1384430603482693643"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - ملازم اول", 8},
    ["1384430601863430164"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - نقيب", 9},
    ["1384430600500416563"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - رائد", 10},
    ["1384430597996286033"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - مقدم", 11},
    ["1384430596884922368"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - عقيد", 12},
    ["1384430595634888756"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - عميد", 13},
    ["1384430594074873927"] = {"police", "<span style='color:#34aeeb;'>  إدارة الشرطة - لواء ركن", 14},
    ["1384430592644485200"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - فريق ركن", 105},
    ["1384430591541379164"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - فريق اول ركن", 106},
    ["1384430585795182693"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - نائب قائد الأمن العام", 17},
    ["1384430584520249424"] = {"police", "<span style='color:#34aeeb;'> إدارة الشرطة - قائد الأمن العام", 18},

    ["1384430765172854835"] = {"ambulance", "<span style='color:#FF6C00;'>الهلال الاحمر - متدرب", 0},
    ["1384430763902238772"] = {"ambulance", "<span style='color:#FF6C00;'>الهلال الاحمر - مستوى 1", 1},
    ["1384430762551676958"] = {"ambulance", "<span style='color:#FF6C00;'>الهلال الاحمر - مستوى 2", 2},
    ["1384430761221816462"] = {"ambulance", "<span style='color:#FF6C00;'>الهلال الاحمر - مستوى 3", 3},
    ["1384430759917387877"] = {"ambulance", "<span style='color:#FF6C00;'>الهلال الاحمر - مستوى 4", 4},
    ["1384430758470615060"] = {"ambulance", "<span style='color:#FF6C00;'>الهلال الاحمر - مستوى 5", 5},
    ["1384430757321379961"] = {"ambulance", "<span style='color:#FF6C00;'>الهلال الاحمر - مستوى 6", 6},
    ["1384430756176199691"] = {"ambulance", "<span style='color:#FF6C00;'>الهلال الاحمر - مستوى 7", 7},
    ["1384430755043610654"] = {"ambulance", "<span style='color:#FF6C00;'>الهلال الاحمر - مستوى 8", 8},
    ["1384430753621868677"] = {"ambulance", "<span style='color:#FF6C00;'>الهلال الاحمر - مستوى 9", 9},
    ["1384430752669896715"] = {"ambulance", "<span style='color:#FF6C00;'>الهلال الاحمر - مستوى 10", 10},
    ["1384430744268705893"] = {"ambulance", "<span style='color:#FF6C00;'>الهلال الاحمر - نائب قائد", 11},
    ["1384430743165468702"] = {"ambulance", "<span style='color:#FF6C00;'>الهلال الاحمر - قائد", 12},

    ["1384430673514991626"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود -  جندي", 0},
    ["1384430672260894760"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود -  جندي أول", 1},
    ["1384430671052804136"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود -  عريف", 2},
    ["1384430669303906304"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود - وكيل رقيب", 3},
    ["1384430667667869798"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود - رقيب ", 4},
    ["1384430665738485832"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود - رقيب أول ", 5},
    ["1384430664258158633"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود - رئيس رقباء", 6},
    ["1384430662022598656"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود - ملازم", 7},
    ["1384430660244078622"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود - ملازم اول", 8},
    ["1384430659044376700"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود - نقيب", 9},
    ["1384430657694076949"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود - رائد", 10},
    ["1384430656549031989"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود - مقدم", 11},
    ["1384430655156523038"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود - عقيد", 12},
    ["1384430654049095690"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود - عميد", 13},
    ["1384430652895793202"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود - لواء", 14},
    ["1384430641336025110"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود - نائب قائد", 15},
    ["1384430640111288380"] = {"agent", "<span style='color:#3EFF00;'> حرس الحدود - قائد حرس الحدود", 16},

    ["1384430732793086053"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - متدرب", 0},
    ["1384430730381365329"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 1", 1},
    ["1384430729093582888"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 2", 2},
    ["1384430728023904337"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 3", 3},
    ["1384430726732316742"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 4", 4},
    ["1384430725264310393"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 5", 5},
    ["1384430723972337704"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 6", 6},
    ["1384430722097614888"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 7", 7},
    ["1384430720629604393"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 8", 8},
    ["1384430713113153557"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 9", 9},
    ["1384430712001790042"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 10", 10},
    ["1384430699670667284"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - نائب قائد الكراج", 11},
    ["1384430698487746712"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - قائد الكراج", 12},

    ["1384430535744684053"] = {"admin", "<span style='color:gold;'>الرقابة و التفتيش - الدعم الفني", 0},
    ["1384430533123113101"] = {"admin", "<span style='color:gold;'>الرقابة و التفتيش - مشرف", 1},
    ["1384430531789193297"] = {"admin", "<span style='color:gold;'>+الرقابة و التفتيش -  مشرف +", 2},
    ["1384430527238373427"] = {"admin", "<span style='color:gold;'>الرقابة و التفتيش - ادمن", 3},
    ["1384430520145940500"] = {"admin", "<span style='color:gold;'>الرقابة و التفتيش - ادمن +", 4},
}

ESX.RegisterServerCallback('esx_joblisting:getJobsList', function(source, cb)
    local src = source
    local disjobs = {}
    for k, v in pairs(availableJobs) do 
        disjobs[k] = v 
    end

    for k, v in pairs(exports['discord_perms'].GetRoles(src, src)) do
        for i, j in pairs(disroles) do 
            if i == v then 
                table.insert(disjobs, {
                    job = j[1],
                    label = j[2],
                    grade = j[3],
                })
                break
            end
        end
    end
    cb(disjobs)
end)

local AdminWebHook = "https://discord.com/api/webhooks/1107623744429625426/JyVIeWZ85totxzlIpQ_SWMKBQEyVx2Sp7H-Vj9xYMPrnfuOZ7aCGlYNKYWhy-LQ8EIa_" -- سجل تغير وظيفة الرقابة و التفتيش
local PoliceWebHook = "https://discord.com/api/webhooks/1107624702815514664/acV7ws9YGikYRC2y-GjgGHQodCfXSXpo-2bukk_4JZrvr8BCQaUwCbcR8Blv9yhsf5qD" -- سجل تغير وظيفة الشرطة
local AgentWebHook = "https://discord.com/api/webhooks/1107628106342223944/RPi7sXz-WYzYBezUEwfWhRN2h1xuL6-vdRLrnfl0RslFDns13nw-rxF8F9MPrfjGohPU" -- سجل تغير وظيفة حرس الحدود
local AmbulanceWebHook = "https://discord.com/api/webhooks/1107628451965456444/68CuKuA-zSptxSqnD3E5oJkiYKZs_z45J4jzQuPFYgf1yBDUWPGZ12Z29HJ34EX5WnRc" -- سجل تغير وظيفة الهلال الاحمر
local MechanicWebHook = "https://discord.com/api/webhooks/1107628558580469801/xmpacvKqGqmh_h7CdQGyPuEfXvYr71bKiYOLDUP1yy3vYO9MUiEoOz2e3Gdsjq63m0Lo" -- سجل تغير وظيفة الميكانيكي
RegisterServerEvent('esx_joblisting:setJob_kaugy36')
AddEventHandler('esx_joblisting:setJob_kaugy36', function(newjob, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then
	if xPlayer.job.name == "admin" then
	xPlayer.setJob(newjob, grade)
	local embeds_admin = {
		{
			["title"]= "تغير وظيفة من وظيفة الرقابة و التفتيش الى وظيفة اخرى",
			["type"]="rich",
            ["description"] = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\nقام بتغير وظيفته من وظيفة إدارة الرقابة و التفتيش الى وظيفة أخرى",
			["color"] = "16777215",
			["footer"]=  { ["text"]= "سجل تغير الوظيفة", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/1094780759950241913/1105667435006263346/c9ea4b4198cd73e9.png"},
		}
	}
	PerformHttpRequest(AdminWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds_admin}), { ['Content-Type'] = 'application/json' })
	elseif xPlayer.job.name == "police" then
	xPlayer.setJob(newjob, grade)
	local embeds_police = {
		{
			["title"]= "تغير وظيفة من وظيفة إدارة الشرطة الى وظيفة اخرى",
			["type"]="rich",
            ["description"] = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\nقام بتغير وظيفته من وظيفة إدارة الشرطة الى وظيفة اخرى",
			["color"] = "16777215",
			["footer"]=  { ["text"]= "سجل تغير الوظيفة", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/1101510296067133511/1105516071672365196/big.png"},
		}
	}
	PerformHttpRequest(PoliceWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds_police}), { ['Content-Type'] = 'application/json' })
	elseif xPlayer.job.name == "agent" then
	xPlayer.setJob(newjob, grade)
	local embeds_agent = {
		{
			["title"]= "تغير وظيفة من وظيفة حرس الحدود الى وظيفة أخرى",
			["type"]="rich",
            ["description"] = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\nقام بتغير وظيفته من وظيفة حرس الحدود الى وظيفة أخرى",
			["color"] = "16777215",
			["footer"]=  { ["text"]= "سجل تغير الوظيفة", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/1101293898921418783/1105246763218120744/images-removebg-preview.png"},
		}
	}
	PerformHttpRequest(AgentWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds_agent}), { ['Content-Type'] = 'application/json' })
	elseif xPlayer.job.name == "ambulance" then
	xPlayer.setJob(newjob, grade)
	local embeds_ambulance = {
		{
			["title"]= "تغير وظيفة من وظيفة الهلال الاحمر الى وظيفة أخرى",
			["type"]="rich",
            ["description"] = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\nقام بتغير وظيفته من وظيفة الهلال الاحمر الى وظيفة أخرى",
			["color"] = "16777215",
			["footer"]=  { ["text"]= "سجل تغير الوظيفة", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/1101510296067133511/1107624360891658240/5eadf2c883859_0.png"},
		}
	}
	PerformHttpRequest(AmbulanceWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds_ambulance}), { ['Content-Type'] = 'application/json' })
	elseif xPlayer.job.name == "mechanic" then
	xPlayer.setJob(newjob, grade)
	local embeds_mechanic = {
		{
			["title"]= "تغير وظيفة من وظيفة كراج الميكانيكي الى وظيفة أخرى",
			["type"]="rich",
            ["description"] = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\nقام بتغير وظيفته من وظيفة كراج الميكانيك الى وظيفة اخرى ",
			["color"] = "16777215",
			["footer"]=  { ["text"]= "سجل تغير الوظيفة", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/1105165717558530159/1105166854177177660/KG.png"},
		}
	}
	PerformHttpRequest(MechanicWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds_mechanic}), { ['Content-Type'] = 'application/json' })
	else
	xPlayer.setJob(newjob, grade)
    end
    end
end)

RegisterServerEvent('esx_joblisting:setJob_admin_73alhdba762_dkab62dvbeme')
AddEventHandler('esx_joblisting:setJob_admin_73alhdba762_dkab62dvbeme', function(job, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then
	        local embeds = {
		{
			["title"]= "تغير وظيفة الى وظيفة الرقابة و التفتيش",
			["type"]="rich",
            ["description"] = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\n من وظيفة: **"..xPlayer.job.label.." - "..xPlayer.job.grade_label.."** \n الى وظيفة: `الرقابة و التفتيش` \n المستوى الوظيفي : "..grade.."",
			["color"] = "2303786",
			["footer"]=  { ["text"]= "سجل تغير الوظيفة", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/931300530393874482/931302251513909348/7fd04efde345f231.png"},
		}
	}
	        PerformHttpRequest(AdminWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
			xPlayer.setJob(job, grade)
    end
end)

RegisterServerEvent('esx_joblisting:setJob_police_da3oid63')
AddEventHandler('esx_joblisting:setJob_police_da3oid63', function(job, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then
	        local embeds = {
		{
			["title"]= "تغير وظيفة الى وظيفة إدارة الشرطة",
			["type"]="rich",
            ["description"] = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\n من وظيفة: **"..xPlayer.job.label.." - "..xPlayer.job.grade_label.."** \n الى وظيفة: `إدارة الشرطة` \n المستوى الوظيفي : "..grade.."",
			["color"] = "2303786",
			["footer"]=  { ["text"]= "سجل تغير الوظيفة", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/931300530393874482/931302251513909348/7fd04efde345f231.png"},
		}
	}
	        PerformHttpRequest(PoliceWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
			xPlayer.setJob(job, grade)
    end
end)

RegisterServerEvent('esx_joblisting:setJob_agent_kaug362')
AddEventHandler('esx_joblisting:setJob_agent_kaug362', function(job, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then
	        local embeds = {
		{
			["title"]= "تغير وظيفة الى وظيفة حرس الحدود",
			["type"]="rich",
            ["description"] = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\n من وظيفة: **"..xPlayer.job.label.." - "..xPlayer.job.grade_label.."** \n الى وظيفة: `حرس الحدود` \n المستوى الوظيفي : "..grade.."",
			["color"] = "2303786",
			["footer"]=  { ["text"]= "سجل تغير الوظيفة", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/931300530393874482/931302251513909348/7fd04efde345f231.png"},
		}
	}
	        PerformHttpRequest(AgentWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
			xPlayer.setJob(job, grade)
    end
end)

RegisterServerEvent('esx_joblisting:setJob_ambulance_d8labd3')
AddEventHandler('esx_joblisting:setJob_ambulance_d8labd3', function(job, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then
	        local embeds = {
		{
			["title"]= "تغير وظيفة الى وظيفة الهلال الاحمر",
			["type"]="rich",
            ["description"] = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\n من وظيفة: **"..xPlayer.job.label.." - "..xPlayer.job.grade_label.."** \n الى وظيفة: `الهلال الاحمر \n المستوى الوظيفي : "..grade.."",
			["color"] = "2303786",
			["footer"]=  { ["text"]= "سجل تغير الوظيفة", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/931300530393874482/931302251513909348/7fd04efde345f231.png"},
		}
	}
	        PerformHttpRequest(AmbulanceWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
			xPlayer.setJob(job, grade)
    end
end)

RegisterServerEvent('esx_joblisting:setJob_mechanic_a73kvgad3')
AddEventHandler('esx_joblisting:setJob_mechanic_a73kvgad3', function(job, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then
	        local embeds = {
		{
			["title"]= "تغير وظيفة الى وظيفة كراج الميكانيك",
			["type"]="rich",
            ["description"] = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\n من وظيفة: **"..xPlayer.job.label.." - "..xPlayer.job.grade_label.."** \n الى وظيفة: `كراج الميكانيك \n المستوى الوظيفي : "..grade.."",
			["color"] = "2303786",
			["footer"]=  { ["text"]= "سجل تغير الوظيفة", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/931300530393874482/931302251513909348/7fd04efde345f231.png"},
		}
	}
	        PerformHttpRequest(MechanicWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
			xPlayer.setJob(job, grade)
    end
end)
