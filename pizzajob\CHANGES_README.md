# تغييرات نظام الدلفري - Pizza Job System Changes

## التغييرات المطبقة / Applied Changes

### 1. إزالة نظام العين (Target System Removal)
- تم إزالة الاعتماد على `ox_target` أو `qb-target`
- تم حذف جميع استخدامات `exports.ox_target:addLocalEntity`
- تم تغيير `Config.Target` إلى `'none'`

### 2. إضافة نظام الضغط على E (E Key Press System)
- تم إضافة نظام جديد للكشف عن القرب من الطباخ
- تم إضافة نظام للكشف عن القرب من بوت التسليم
- تم إضافة معالج للضغط على مفتاح E

### 3. الميزات الجديدة / New Features

#### أ) التفاعل مع الطباخ:
- عند الاقتراب من الطباخ (ضمن مسافة 3.0 متر)
- يظهر نص "[E] تحدث مع الطباخ" في أعلى الشاشة
- الضغط على E يفتح قائمة الدلفري

#### ب) التفاعل مع بوت التسليم:
- عند الاقتراب من بوت التسليم (ضمن مسافة 3.5 متر)
- يظهر نص "[E] تسليم الطلب" في أعلى الشاشة
- الضغط على E يبدأ عملية التسليم

#### ج) قائمة الطباخ المحدثة:
- بدء التوصيل / إيقاف التوصيل
- عرض المتصدرين
- استعادة الملابس الأصلية (إذا كانت متوفرة)

### 4. التحسينات التقنية / Technical Improvements

#### أ) الأداء:
- تحديث كل 100ms للكشف عن القرب (بدلاً من التحديث المستمر)
- إزالة الاعتماد على نظام العين الثقيل

#### ب) واجهة المستخدم:
- استخدام `lib.showTextUI` و `lib.hideTextUI` للنصوص
- ألوان مختلفة للطباخ (أزرق) وبوت التسليم (أخضر)
- أيقونات واضحة ومناسبة

### 5. الشروط المطلوبة / Requirements
- يجب أن يكون اللاعب لديه وظيفة الدلفري (`pizzajob`)
- يجب ألا يكون اللاعب في مركبة
- يجب ألا يكون اللاعب ميتاً

### 6. الملفات المعدلة / Modified Files
- `client/main.lua` - التغييرات الرئيسية
- `config.lua` - تعديل إعدادات Target System

### 7. كيفية الاستخدام / How to Use

#### للطباخ:
1. اقترب من الطباخ (ضمن 3.0 متر)
2. ستظهر رسالة "[E] تحدث مع الطباخ"
3. اضغط E لفتح القائمة
4. اختر الخيار المطلوب

#### للتسليم:
1. ابدأ التوصيل من قائمة الطباخ
2. اذهب إلى موقع التسليم
3. اقترب من البوت الأصفر (ضمن 3.5 متر)
4. ستظهر رسالة "[E] تسليم الطلب"
5. اضغط E لبدء عملية التسليم

### 8. المميزات المحافظ عليها / Preserved Features
- جميع أنظمة الأمان والحماية
- نظام المكافآت والخبرة
- نظام الزي الموحد
- نظام حماية المركبات
- نظام المراقبة والتسجيل

## ملاحظات مهمة / Important Notes
- النظام الجديد أكثر بساطة وسهولة في الاستخدام
- لا يتطلب أي إضافات خارجية إضافية
- يعمل مع جميع أنواع الخوادم (ESX/QB)
- متوافق مع جميع الميزات الموجودة

## الدعم / Support
إذا واجهت أي مشاكل، تأكد من:
1. إعادة تشغيل المورد
2. التأكد من وجود وظيفة `pizzajob`
3. التأكد من تحديث قاعدة البيانات إذا لزم الأمر
