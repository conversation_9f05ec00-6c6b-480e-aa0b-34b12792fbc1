Locales['fr'] = {
  -- Global menus
  ['cloakroom']                 = 'vestiaire',
  ['cloak_change']              = 'appuyez sur ~INPUT_PICKUP~ pour vous changer.',
  ['citizen_wear']              = 'tenue civile',
  ['job_wear']                  = 'tenue de travail',
  ['bank_deposit_g']            = 'une caution de ~g~',
  ['bank_deposit_r']            = 'une caution de ~g~$',
  ['bank_deposit2']             = '$ ~s~vous a été rendue suite à votre évanouissment.',
  ['foot_work']                 = 'vous devez être à pied pour pouvoir travailler.',
  ['next_point']                = 'rendez-vous à la prochaine étape après avoir complété celle-ci.',
  ['security_deposit']          = 'la caution rendue sera de ~g~$',
  ['not_your_vehicle']          = "ce n'est pas votre véhicule ou vous devez être conducteur.",
  ['in_vehicle']                = 'vous devez être dans un véhicule.',
  ['wrong_point']               = "vous n'êtes pas au bon point de livraison.",
  ['max_limit']                 = 'vous avez le maximum de: ',
  ['not_enough']                = "vous n'avez plus assez de",
  ['not_enough2']               = ' pour continuer cette tâche.',
  ['caution_taken']             = ' ~s~vous a été prélevée.',
  ['caution_returned']          = ' ~s~vous a été rendue.',
  ['spawn_veh']                 = 'spawner véhicule de fonction',
  ['spawn_veh_button']          = 'appuyez sur ~INPUT_PICKUP~ pour appeler le véhicule de livraison.',
  ['spawn_truck_button']        = 'appuyez sur ~INPUT_PICKUP~ pour appeler le camion.',
  ['service_vh']                = 'véhicule de fonction',
  ['return_vh']                 = 'supression du véhicule',
  ['return_vh_button']          = 'appuyez sur ~INPUT_PICKUP~ pour rendre le véhicule.',
  ['delivery_point']            = 'point de livraison',
  ['delivery']                  = 'livraison',
  ['public_enter']              = 'appuyez sur ~INPUT_PICKUP~ pour entrer dans l\'immeuble.',
  ['public_leave']              = 'appuyez sur ~INPUT_PICKUP~ pour aller à l\'entrée de l\'immeuble.',

  -- Lumber Jack job
  ['lj_locker_room']            = "lumberjack's Locker Room",
  ['lj_mapblip']                = 'tas de bois',
  ['lj_wood']                   = 'bois',
  ['lj_pickup']                 = 'appuyez sur ~INPUT_PICKUP~ pour récupérer du bois.',
  ['lj_cutwood']                = 'découpe du bois',
  ['lj_cutwood_button']         = 'appuyez sur ~INPUT_PICKUP~ pour couper du bois.',
  ['lj_board']                  = 'planches',
  ['lj_planks']                 = 'paquet de planche',
  ['lj_cutwood']                = 'bois coupé',
  ['lj_pick_boards']            = 'appuyez sur ~INPUT_PICKUP~ pour récupérer des planches.',
  ['lj_deliver_button']         = 'appuyez sur ~INPUT_PICKUP~ pour livrer les planches.',

  -- Fisherman
  ['fm_fish_locker']            = "fishermen's Locker Room",
  ['fm_fish']                   = 'poisson',
  ['fm_fish_area']              = 'zone de pêche',
  ['fm_fish_button']            = 'appuyez sur ~INPUT_PICKUP~ pour pêcher.',
  ['fm_spawnboat_title']        = 'spawner bateau',
  ['fm_spawnboat']              = 'appuyez sur ~INPUT_PICKUP~ pour appeler le bateau.',
  ['fm_boat_title']             = 'bateau',
  ['fm_boat_return_title']      = 'supression du bateau',
  ['fm_boat_return_button']     = 'appuyez sur ~INPUT_PICKUP~ pour rendre le bateau.',
  ['fm_deliver_fish']           = 'appuyez sur ~INPUT_PICKUP~ pour livrer le poisson.',

  -- Fuel
  ['f_oil_refiner']             = 'Vestiaire du raffineur',
  ['f_drill_oil']               = 'Extraction le pétrole',
  ['f_fuel']                    = 'pétrole',
  ['f_drillbutton']             = 'Appuyez sur ~INPUT_PICKUP~ pour forer.',
  ['f_fuel_refine']             = 'pétrol raffiné',
  ['f_refine_fuel_button']      = 'Appuyez sur ~ INPUT_PICKUP ~ pour raffiner le pétrole.',
  ['f_fuel_mixture']            = 'Mélange',
  ['f_gas']                     = 'Essence',
  ['f_fuel_mixture_button']     = 'Appuyez sur ~INPUT_PICKUP~ pour mélanger.',
  ['f_deliver_gas']             = "Appuyez sur ~INPUT_PICKUP~ pour livrer l'essence.",

  -- Miner
  ['m_miner_locker']            = "miner's Locker Room",
  ['m_rock']                    = 'rocher',
  ['m_pickrocks']               = 'appuyez sur ~INPUT_PICKUP~ pour récupérer des rochers.',
  ['m_washrock']                = 'roche lavé',
  ['m_rock_button']             = 'appuyez sur ~INPUT_PICKUP~ pour laver les roches.',
  ['m_rock_smelting']           = 'fonderie',
  ['m_copper']                  = 'cuivre',
  ['m_sell_copper']             = 'revente de cuivre',
  ['m_deliver_copper']          = 'appuyez sur ~INPUT_PICKUP~ pour livrer le cuivre.',
  ['m_iron']                    = 'fer',
  ['m_sell_iron']               = 'revente de fer',
  ['m_deliver_iron']            = 'appuyez sur ~INPUT_PICKUP~ pour livrer le fer.',
  ['m_gold']                    = 'or',
  ['m_sell_gold']               = "revente d'or",
  ['m_deliver_gold']            = "appuyez sur ~INPUT_PICKUP~ pour livrer l'or'.",
  ['m_diamond']                 = 'diamant',
  ['m_sell_diamond']            = "revente de diamants",
  ['m_deliver_diamond']         = 'appuyez sur ~INPUT_PICKUP~ pour livrer les diamants.',
  ['m_melt_button']             = 'appuyez sur ~INPUT_PICKUP~ pour fondre les roches.',

  -- Reporter
  ['reporter_name']             = 'le Maclerait Libéré',
  ['reporter_garage']           = 'appuyez sur ~INPUT_PICKUP~ pour descendre au garage.',

  -- Slaughterer
  ['s_slaughter_locker']        = "butcher's Locker Room",
  ['s_hen']                     = 'poulailler',
  ['s_alive_chicken']           = 'poulet vivant',
  ['s_catch_hen']               = 'appuyez sur ~INPUT_PICKUP~ pour attrapper des poulets vivants.',
  ['s_slaughtered_chicken']     = 'poulet à conditionner',
  ['s_chop_animal']             = 'appuyez sur ~INPUT_PICKUP~ pour dépecer les poulets.',
  ['s_slaughtered']             = 'abattoir',
  ['s_package']                 = 'emballage',
  ['s_packagechicken']          = 'poulet en barquette',
  ['s_unpackaged']              = 'poulet à conditionner',
  ['s_unpackaged_button']       = 'appuyez sur ~INPUT_PICKUP~ pour conditionner le poulet en barquette.',
  ['s_deliver']                 = 'appuyez sur ~INPUT_PICKUP~ pour livrer les barquettes de poulet.',

  -- Dress Designer
  ['dd_dress_locker']           = "dress Designer's Locker Room",
  ['dd_wool']                   = 'laine',
  ['dd_pickup']                 = 'appuyez sur ~INPUT_PICKUP~ pour récupérer de la laine.',
  ['dd_fabric']                 = 'tissu',
  ['dd_makefabric']             = 'appuyez sur ~INPUT_PICKUP~ pour fabriquer du tissu.',
  ['dd_clothing']               = 'vêtement',
  ['dd_makeclothing']           = 'appuyez sur ~INPUT_PICKUP~ pour récupérer des vêtements.',
  ['dd_deliver_clothes']        = 'appuyez sur ~INPUT_PICKUP~ pour livrer les vêtements.',
}
