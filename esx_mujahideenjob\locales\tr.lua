Locales['tr'] = {
	-- Cloakroom
	['cloakroom'] = 'kıyafet Odası',
	['citizen_wear'] = 'sivil kıyafet',
	['mujahideen_wear'] = 'polis kıyafeti',
	['gilet_wear'] = 'turuncu  ceket',
	['bullet_wear'] = 'kurşun geçirmez yelek',
	['no_outfit'] = '  üniforma yok!',
	['open_cloackroom'] = '~y~Kıyafetinizi~s~ değiştirmek için ~INPUT_CONTEXT~ tuşuna basın. ',
	-- Armory
	['remove_object'] = 'obje Al',
	['deposit_object'] = 'obje Koy',
	['get_weapon'] = 'cephanelikten silah al',
	['put_weapon'] = 'cephaneliğe silah koy',
	['buy_weapons'] = 'silah satın al',
	['armory'] = 'cephanelik',
	['open_armory'] = '~y~Cephaneliğe~s~ erişmek için ~INPUT_CONTEXT~ tuşuna basın.',
	['armory_owned'] = 'sahipsin',
	['armory_free'] = 'bedava',
	['armory_item'] = '%s TL',
	['armory_weapontitle'] = 'cephanelik - Silah al',
	['armory_componenttitle'] = 'cephanelik - Silah eklentileri',
	['armory_bought'] = ' %s Lira Karşılığında %s  satın aldın',
	['armory_money'] = 'yeteri kadar paran yok.',
	['armory_hascomponent'] = 'bu eklentiye zaten sahipsin !',
	['get_weapon_menu'] = 'cephanelik - Silah al',
	['put_weapon_menu'] = 'cephanelik - Silah koy',
	-- Vehicles
	['vehicle_menu'] = 'araç',
	['vehicle_blocked'] = 'tüm Spawn Noktaları Bloke Edilmiş!',
	['garage_prompt'] = '~y~Araç eylemlerine ulaşmak için ~s~ ~INPUT_CONTEXT~ tuşuna basınız.',
	['garage_title'] = 'araç eylemleri',
	['garage_stored'] = 'garajda',
	['garage_notstored'] = 'garajda değil',
	['garage_storing'] = 'arac garaja koyuluyor ama, etrafınızda oyuncu olmadığına emin olun .',
	['garage_has_stored'] = 'araç garajına koyuldu',
	['garage_has_notstored'] = 'yakında sana ait araç yok',
	['garage_notavailable'] = 'aracınız garajda değil.',
	['garage_blocked'] = 'müsait araç çıkarma noktası yok!',
	['garage_empty'] = 'garajında bir araç yok.',
	['garage_released'] = 'aracını garajdan çıkardın.',
	['garage_store_nearby'] = 'yakında araç yok.',
	['garage_storeditem'] = 'garajı Aç',
	['garage_storeitem'] = 'aracı garaja koy',
	['garage_buyitem'] = 'araç Galerisi',
	['garage_notauthorized'] = 'galeriye erişim iznin yok',
	['helicopter_prompt'] = '~y~Helikopter eylemlerine~s~ erişmek için ~INPUT_CONTEXT~ tuşuna basınız.',
	['shop_item'] = '%s TL',
	['vehicleshop_title'] = 'araç galerisi',
	['vehicleshop_confirm'] = 'bu aracı satın almak istiyor musun?',
	['vehicleshop_bought'] = '%s lira Karşılığında %s Satın aldın ',
	['vehicleshop_money'] = 'bu araca paran yetmiyor',
	['vehicleshop_awaiting_model'] = 'araç ~g~Yükleniyor~s~ lütfen bekleyiniz',
	['confirm_no'] = 'hayır',
	['confirm_yes'] = 'evet',
	-- Service
	['service_max'] = 'hizmete Giremezsin,Hizmette yeterli polis var: %s/%s',
	['service_not'] = 'hizmete Giremedin! İlk Önce Kıyafetlerini Değiştirmelisin.',
	['service_anonunce'] = 'hizmet Bilgisi',
	['service_in'] = 'hizmete Giriş Yaptın, Hoşgeldin!',
	['service_in_announce'] = 'operatör %s servise giriş yaptı!',
	['service_out'] = 'hizmetten Çıktın.',
	['service_out_announce'] = 'operatör %s servisten çıkış yaptı.',
	-- Action Menu
	['citizen_interaction'] = 'vatandaş Etkileşimi',
	['vehicle_interaction'] = 'araç Etkileşimi',
	['object_spawner'] = 'obje Yerleştirici',

	['id_card'] = 'kimlik Kartı',
	['search'] = 'ara',
	['handcuff'] = 'kelepçele / Kelepçeyi Çöz',
	['drag'] = 'taşı',
	['put_in_vehicle'] = 'araca Koy',
	['out_the_vehicle'] = 'araçtan Çıkar',
	['fine'] = 'ceza',
	['unpaid_bills'] = 'ödenmemiş Faturaları Kontrol Et',
	['license_check'] = 'lisansları Kontrol Et',
	['license_revoke'] = 'lisansa El Koy',
	['license_revoked'] = '~b~%s~s~ lisansın ~y~iptal edildi~s~!',
	['licence_you_revoked'] = '~b~%s~s~ adlı kullanıcıya ait ~y~%s~s~ adlı lisansına el koydun.',
	['no_players_nearby'] = 'yakında oyuncu yok!',
	['being_searched'] = 'üstün ~y~searched~s~ - ~b~Polis~s~ tarafından aranıyor',
	-- Vehicle interaction
	['vehicle_info'] = 'araç Bilgisi',
	['pick_lock'] = 'aracın kilidini aç',
	['vehicle_unlocked'] = 'araç ~g~açıldı~s~',
	['no_vehicles_nearby'] = 'yakınlarda araç yok',
	['impound'] = 'araca el koy',
	['impound_prompt'] = 'el konulan aracı geri bırakmak için ~INPUT_CONTEXT~ tuşuna basın',
	['impound_canceled'] = 'el konulma kaldırıldı',
	['impound_canceled_moved'] = 'araç hareket ettiği için el koyma iptal edildi',
	['impound_successful'] = 'araca el koydun',
	['search_database'] = 'araç bilgileri',
	['search_database_title'] = 'araç bilgileri - araç kayıt numarasından ara',
	['search_database_error_invalid'] = 'bu doğru bir araç kayıt numarası değil',
	-- Traffic interaction
	['traffic_interaction'] = 'traik işlemleri',
	['cone'] = 'koni',
	['barrier'] = 'bariyer',
	['spikestrips'] = 'emniyet şeridi',
	['box'] = 'kutu',
	['cash'] = 'para kurusu',
	-- ID Card Menu
	['name'] = 'isim: %s',
	['job'] = 'meslek: %s',
	['sex'] = 'cinsiyet: %s',
	['dob'] = 'doğum Tarihi: %s',
	['height'] = 'boy: %s',
	['bac'] = 'BAC: %s',
	['unknown'] = 'bilinmiyor',
	['male'] = 'erkek',
	['female'] = 'kadın',
	-- Body Search Menu
	['guns_label'] = '--- Silahlar ---',
	['inventory_label'] = '--- Envanter ---',
	['license_label'] = ' --- Lisanslar ---',
	['confiscate'] = 'el koy %s',
	['confiscate_weapon'] = 'el koyuldu %s ve %s mermi',
	['confiscate_inv'] = 'el koyuldu %sx %s',
	['confiscate_dirty'] = 'kara paraya el koyuldu: <span style="color:red;">%s TL</span>',
	['you_confiscated'] = '~b~%s~s~ adlı kişiden ~y~%sx~s~ ~b~%s~s~ aldın ',
	['got_confiscated'] = '~y~%s~s~ adlı kişi ~y~%sx~s~ ~b~%s~s~ eşyanı aldı',
	['you_confiscated_account'] = '~b~%s~s~ adlı oyuncunun ~g~%s TL~s~ (%s) kara parasına el konuldu',
	['got_confiscated_account'] = '~g~%s TL~s~ (%s) kara parana el konuldu ~y~%s~s~',
	['you_confiscated_weapon'] = '~b~%s~s~ adlı oyuncunun ~b~%s~s~ ve ~o~%s~s~ mermilerine el konuldu',
	['got_confiscated_weapon'] = '~b~%s~s~ ve ~o~%s~s~ mermilerine ~y~%s~s~ tarafından el konuldu',
	['traffic_offense'] = 'trafik cezaları',
	['minor_offense'] = 'küçük suç',
	['average_offense'] = 'ortalama suç',
	['major_offense'] = 'büyük suç',
	['fine_total'] = 'ceza: %s',
	-- Vehicle Info Menu
	['plate'] = 'plaka: %s',
	['owner_unknown'] = 'sahibi: Bilinmiyor',
	['owner'] = 'sahibi: %s',
	-- Boss Menu
	['open_bossmenu'] = 'menüyü açmak için ~INPUT_CONTEXT~ tuşuna basın',
	['quantity_invalid'] = 'miktar hatalı',
	['have_withdrawn'] = '~y~%sx~s~ ~b~%s~s~ çekildi',
	['have_deposited'] = '~y~%sx~s~ ~b~%s~s~ yatırıldı',
	['quantity'] = 'miktar',
	['inventory'] = 'envanter',
	['mujahideen_stock'] = 'polis Stoğu',
	-- Misc
	['remove_prop'] = 'objeyi silmek için ~INPUT_CONTEXT~ tuşuna basın',
	['map_blip'] = 'polis Karakolu',
	['unrestrained_timer'] = 'ellerinin yavaşça çözüldüğünü hissediyorsun.',
	-- Notifications
	['alert_mujahideen'] = 'polis Acil Durum',
	['phone_mujahideen'] = 'polis',
  }
