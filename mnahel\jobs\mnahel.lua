Config.Jobs.mnahel = {
    BlipInfos = {
      Sprite = 266,  -- أيقونة المناحل الأصلية
      Color = 46     -- لون أصفر للعسل
    },
    Vehicles = {
      Truck = {
        Spawner = 1,
        Hash = "burrito3",
        Trailer = "none",
        HasCaution = true
      }
    },
    Zones = {
      CloakRoom = {
        Pos   = {x = -1058.69, y = 4914.687, z = 211.81},  -- تسجيل دخول الأول
        Size  = {x = 1.5, y = 1.5, z = 1.5},
        Color = {r = 255, g = 215, b = 0},  -- لون ذهبي للعسل
        Marker= 21,
        Blip  = true,
        Name  = _U('s_mnahel_locker'),
        Type  = "cloakroom",
        Hint  = _U('cloak_change'),
      },
      CloakRooms = {
        Pos   = {x = 3719.531, y = 1748.364, z = 6.1501},  -- تسجيل دخول الثاني
        Size  = {x = 1.5, y = 1.5, z = 1.5},
        Color = {r = 255, g = 215, b = 0},  -- لون ذهبي للعسل
        Marker= 21,
        Blip  = true,
        Name  = _U('s_mnahel_locker'),
        Type  = "cloakroom",
        Hint  = _U('cloak_change'),
      },
  
BeeHiveArea = {
        Pos   = {x = -1137.53, y = 4936.138, z = 221.26},  -- نزلت متر للأرض
        Size  = {x = 4.0, y = 4.0, z = 1.0},  -- دائرة أصغر
        Color = {r = 255, g = 255, b = 0},  -- لون أصفر للدائرة
        Marker= 1,  -- دائرة
        Blip  = true,
        Name  = _U('beehive_collect'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_mnahel'),
            db_name= "mnahel_bucket",
            time   = 4000,  -- وقت جمع العسل من الخلية
            max    = 50,    -- كمية أكبر لأنه مكان واحد
            add    = 2,     -- إضافة عسل خام أكثر
            remove = 1,
            requires = "mnahel_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },


      -------------------------------------


      
      -------------------------------

      HoneyRefining = {
        Pos   = {x = 1414.740, y = 3812.583, z = 31.285},  -- تنقية وتصفية العسل (نزل متر)
        Size  = {x = 4.0, y = 4.0, z = 1.0},  -- دائرة 4x4
        Color = {r = 255, g = 215, b = 0},  -- لون ذهبي للعسل
        Marker= 1,
        Blip  = true,
        Name  = _U('s_getbottle'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_mnahel_name'),
            db_name= "mnahel_bottle",
            time   = 5000,  -- وقت تنقية العسل
            max    = 25,    -- كمية العسل المنقى
            add    = 1,
            remove = 1,
            requires = "mnahel_bucket",
            requires_name = _U('s_bucket_mnahel'),
            drop   = 100
          }
        },
        Hint  = _U('s_mnahelononbottle')
      },
  
      HoneyPackaging = {
        Pos   = {x = 2435.435, y = 4967.171, z = 41.347},  -- التغليف (نزل متر)
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 255, g = 215, b = 0},  -- لون ذهبي للتعبئة
        Marker= 1,
        Blip  = true,
        Name  = _U('s_packagemnahel'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_packagedmnahel'),
            db_name= "mnahel_box",
            time   = 4000,  -- وقت تعبئة العسل
            max    = 30,    -- كمية علب العسل
            add    = 2,     -- تعبئة علبتين في المرة
            remove = 1,
            requires = "mnahel_bottle",
            requires_name = _U('s_unpackagedmnahel'),
            drop   = 100
          }
        },
        Hint  = _U('s_unpackagedmnahel_button')
      },
  
      VehicleSpawner = {
        Pos   = {x = -1063.42, y = 4910.557, z = 212.18},  -- خيار سباون المركبة
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 36,
        Blip  = false,
        Name  = _U('spawn_veh'),
        Type  = "vehspawner",
        Spawner = 1,
        Hint  = _U('spawn_veh_button'),
        Caution = 2000
      },

      VehicleSpawnPoint = {
        Pos   = {x = -1081.74, y = 4916.453, z = 214.43},  -- سباون المركبة
        Size  = {x = 2.0, y = 2.0, z = 1.0},
        Marker= -1,
        Blip  = false,
        Name  = _U('service_vh'),
        Type  = "vehspawnpt",
        Spawner = 1,
        Heading = 106.9
      },
  
      VehicleDeletePoint = {
        Pos   = {x = -1062.82, y = 4899.326, z = 212.27},  -- خيار ارجاع المركبة
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 255, g = 0, b = 0},
        Marker= 36,
        Blip  = false,
        Name  = _U('return_vh'),
        Type  = "vehdelete",
        Hint  = _U('return_vh_button'),
        Spawner = 1,
        Caution = 2000,
        GPS = 0,
        Teleport = 0
      },
  
      Delivery = {
        Pos   = {
            [1] = {x = 4058.989, y = 1978.422, z = 22.290},  -- نزل متر للأرض
        },
          Size  = {
              [1] = {x = 10.0, y = 10.0, z = 1.0},  -- دائرة 10x10
          },
          Color = {r = 255, g = 215, b = 0},  -- لون ذهبي للعسل
          Marker= 1,  -- دائرة
          Blip  = true,
          Name  = _U('mnahel_delivery_point'),
          Type  = "delivery",
          Spawner = 1,

          Item  = {
              {
              name   = _U('delivery'),
              time   = 1500,  -- وقت عادي للبيع
              remove = 1,
              max    = 100,
              price  = Config.Prices.mnahel,
              xp  = Config.Xp.mnahel,
              requires = "mnahel_box",
              requires_name = _U('s_packagedmnahel'),
              drop   = 100
              }
          },
          Hint  = _U('mnahel_deliver')
      }
    }
  }
  