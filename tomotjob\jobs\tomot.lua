Config.Jobs.tomot = {
    BlipInfos = {
      Sprite = 140,
      Color = 21
    },
    Vehicles = {
      Truck = {
        Spawner = 1,
        Hash = "nissanditsun",
        Trailer = "none",
        HasCaution = true
      }
    },
    Zones = {
      CloakRoom = {
        --Pos   = {x = -1071.1319580078, y = -2003.7891845703, z = 15.78551197052},
        Pos   = {x = 2833.080, y = 4571.345, z = 46.554},
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 21,
        Blip  = true,
        Name  = _U('s_tomot_locker'),
        Type  = "cloakroom",
        Hint  = _U('cloak_change'),
      },
      CloakRooms = {
        --Pos   = {x = -1071.1319580078, y = -2003.7891845703, z = 15.78551197052},
        Pos   = {x = 766.7117,  y = -2481.9109, z = 20.1654},
        Size  = {x = 1.5, y = 1.5, z = 1.5},
        Color = {r = 225, g = 225, b = 225},
        Marker= 21,
        Blip  = true,
        Name  = _U('s_tomot_locker'),
        Type  = "cloakroom",
        Hint  = _U('cloak_change'),
      },
  
      Cow = {
        Pos   = {x = 2823.620, y = 4606.840, z = 46.474},
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 20,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_tomot'),
            db_name= "tomot_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "tomot_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },


      -------------------------------------
      Cow2 = {
        Pos   = {x = 2815.121, y = 4639.104, z = 45.423},
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 20,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_tomot'),
            db_name= "tomot_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "tomot_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow3 = {
        Pos   = {x = 2828.589, y = 4652.085, z = 46.555},
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 20,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_tomot'),
            db_name= "tomot_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "tomot_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow4 = {
        Pos   = {x = 2836.687, y = 4620.896, z = 47.754},
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 20,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_tomot'),
            db_name= "tomot_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "tomot_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow5 = {
        Pos   = {x = 2850.707, y = 4598.585, z = 47.930},
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 20,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_tomot'),
            db_name= "tomot_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "tomot_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow6 = {
        Pos   = {x = 2870.065, y = 4586.912, z = 47.480},
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 20,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_tomot'),
            db_name= "tomot_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "tomot_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow7 = {
        Pos   = {x = 2862.624, y = 4618.543, z = 48.614},
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 20,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_tomot'),
            db_name= "tomot_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "tomot_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow8 = {
        Pos   = {x = 2853.114, y = 4654.450, z = 48.318},
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 20,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_tomot'),
            db_name= "tomot_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "tomot_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow9 = {
        Pos   = {x = 2867.363, y = 4662.634, z = 48.133},
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 20,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_tomot'),
            db_name= "tomot_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "tomot_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow10 = {
        Pos   = {x = 2876.605, y = 4628.741, z = 48.884},
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 20,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_tomot'),
            db_name= "tomot_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "tomot_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow11 = {
        Pos   = {x = 2884.144, y = 4598.547, z = 47.977},
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 20,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_tomot'),
            db_name= "tomot_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "tomot_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow12 = {
        Pos   = {x = 2891.725, y = 4631.201, z = 48.709},
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 20,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_tomot'),
            db_name= "tomot_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "tomot_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      
      -------------------------------
  
      Bottle = {
        Pos   = {x = 2793.194, y = 4583.820, z = 43.500},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 1,
        Blip  = true,
        Name  = _U('s_getbottle'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_tomot_name'),
            db_name= "tomot_bottle",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "tomot_bucket",
            requires_name = _U('s_bucket_tomot'),
            drop   = 100
          }
        },
        Hint  = _U('s_tomotononbottle')
      },
  
      Packaging = {
        Pos   = {x = 1193.526, y = -1261.37, z = 34.100},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 1,
        Blip  = true,
        Name  = _U('s_packagetomot'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_packagedtomot'),
            db_name= "tomot_box",
            time   = 2000,
            max    = 100,
            add    = 5,
            remove = 1,
            requires = "tomot_bottle",
            requires_name = _U('s_unpackagedtomot'),
            drop   = 100
          }
        },
        Hint  = _U('s_unpackagedtomot_button')
      },
  
      VehicleSpawner = {
        Pos   = {x = 2825.978, y = 4572.066, z = 46.518},
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 36,
        Blip  = false,
        Name  = _U('spawn_veh'),
        Type  = "vehspawner",
        Spawner = 1,
        Hint  = _U('spawn_veh_button'),
        Caution = 2000
      },
  
      VehicleSpawnPoint = {
        Pos   = {x = 2824.265, y = 4578.886, z = 46.511},
        Size  = {x = 2.0, y = 2.0, z = 1.0},
        Marker= -1,
        Blip  = false,
        Name  = _U('service_vh'),
        Type  = "vehspawnpt",
        Spawner = 1,
        Heading = 106.9
      },
  
      VehicleDeletePoint = {
        Pos   = {x = 2830.534, y = 4562.830, z = 46.311},
        Size  = {x = 1.0, y = 1.0, z = 1.0},
        Color = {r = 255, g = 0, b = 0},
        Marker= 36,
        Blip  = false,
        Name  = _U('return_vh'),
        Type  = "vehdelete",
        Hint  = _U('return_vh_button'),
        Spawner = 1,
        Caution = 2000,
        GPS = 0,
        Teleport = 0
      },
  
      Delivery = {
        Pos   = {
            [1] = {x = 861.5342, y = -3026.1326, z = 5.7422},
            [2] = {x = -171.73, y = -2388.47, z = 6.0},
            [3] = {x = -1408.03, y = -2573.41, z = 13.95},
            [4] = {x = 861.5342, y = -3026.1326, z = 5.7422},
            [11] = {x = 861.5342, y = -3026.1326, z = 5.7422},
            [12] = {x = -171.73, y = -2388.47, z = 6.0},
            [13] = {x = -1408.03, y = -2573.41, z = 13.95},
            [14] = {x = 861.5342, y = -3026.1326, z = 5.7422},
        },	
          Size  = {
              [1] = {x = 16.0, y = 16.0, z = 1.0},
              [2] = {x = 7.0, y = 7.0, z = 7.0},
              [3] = {x = 12.0, y = 12.0, z = 1.0},
              [4] = {x = 16.0, y = 16.0, z = 1.0},
              [11] = {x = 16.0, y = 16.0, z = 1.0},
              [12] = {x = 16.0, y = 16.0, z = 1.0},
              [13] = {x = 16.0, y = 16.0, z = 1.0},
              [14] = {x = 16.0, y = 16.0, z = 1.0},
          },	
          Color = {r = 225, g = 225, b = 225},
          Marker= -1,
          Blip  = true,
          Name  = _U('tomot_delivery_point'),
          Type  = "delivery",
          Spawner = 1,
          Item  = {
              {
              name   = _U('delivery'),
              time   = 1500,
              remove = 1,
              max    = 100, -- if not present, probably an error at itemQtty >= item.max in esx_jobs_sv.lua
              price  = Config.Prices.tomot,
              xp  = Config.Xp.tomot,
              requires = "tomot_box",
              requires_name = _U('s_packagedtomot'),
              drop   = 100
              }
          },
          Hint  = _U('tomot_deliver')
      }
    }
  }
  