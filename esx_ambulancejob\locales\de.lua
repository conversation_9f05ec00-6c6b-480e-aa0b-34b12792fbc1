Locales['de'] = {
  -- Cloakroom
  ['cloakroom'] = 'Umkleide',
  ['ems_clothes_civil'] = 'Zivil',
  ['ems_clothes_ems'] = 'Uniform',
  -- Vehicles
  ['ambulance'] = 'Rettungsdienst',
  ['helicopter_prompt'] = 'Drücke ~INPUT_CONTEXT~ um auf das ~y~Helikopter-Menü~s~ zuzugreifen.',
  ['garage_prompt'] = 'Drücke ~INPUT_CONTEXT~ um auf das ~y~Fahrzeug-Menü~s~ zuzugreifen.',
  ['garage_title'] = 'Fahrzeugmenü',
  ['garage_stored'] = 'gelagert',
  ['garage_notstored'] = 'nicht in der Garage',
  ['garage_storing'] = 'Wir versuchen das Fahrzeug einzulagern. Bitte stelle sicher, dass sich kein Spieler in der Nähe des Fahrzeugs befindet.',
  ['garage_has_stored'] = 'Das Fahrzeug wurde in deiner Garage eingelagert.',
  ['garage_has_notstored'] = 'Es wurde kein Fahrzeug in der Nähe gefunden, dass dir gehört.',
  ['garage_notavailable'] = 'Dein Fahrzeug ist nicht in der Garage.',
  ['garage_blocked'] = 'Es existieren keine verfügbaren Spawnpoints!',
  ['garage_empty'] = 'Du hast keine Fahrzeuge in deiner Garage.',
  ['garage_released'] = 'Dein Fahrzeug wurde aus der Garage gefahren.',
  ['garage_store_nearby'] = 'Es sind keine Fahrzeuge in der Nähe.',
  ['garage_storeditem'] = 'Öffne Garage',
  ['garage_storeitem'] = 'Fahrzeug in der Garage einlagern',
  ['garage_buyitem'] = 'Fahrzeug-Shop',
  ['shop_item'] = '$%s',
  ['vehicleshop_title'] = 'Fahrzeug-Shop',
  ['vehicleshop_confirm'] = 'Du möchstest dieses Fahrzeug kaufen?',
  ['vehicleshop_bought'] = 'Du hast dir einen ~y~%s~s~ für ~g~$%s~s~ gekauft!',
  ['vehicleshop_money'] = 'Du kannst dir dieses Fahrzeug nicht leisten!',
  ['vehicleshop_awaiting_model'] = 'Dieses Fahrzeug ~g~LÄDT~s~ bitte warte',
  ['confirm_no'] = 'Nein',
  ['confirm_yes'] = 'Ja',
  -- Action Menu
  ['revive_inprogress'] = 'Eine Wiederbelebung findet statt!',
  ['revive_complete'] = 'Du hast ~y~%s~s~ wiederbelebt',
  ['revive_complete_award'] = 'Du hast ~y~%s~s~ wiederbelebt und dabei ~g~$%s~s~ erhalten!',
  ['revive_fail_offline'] = 'Dieser Spieler ist nicht länger online',
  ['heal_inprogress'] = 'Du heilst!',
  ['heal_complete'] = 'yDu hast ~y~%s~s~ geheilt!',
  ['no_players'] = 'Keine Spieler in der Nähe',
  ['player_not_unconscious'] = 'Diese Spieler ist nicht bewusstlos!',
  ['player_not_conscious'] = 'Dieser Spieler ist nicht bei Bewusstsein!',
  -- Boss Menu
  ['boss_actions'] = 'Chef-Aktionen',
  -- Misc
  ['invalid_amount'] = '~r~Ungültige Menge',
  ['actions_prompt'] = 'Drücke ~INPUT_CONTEXT~ um das ~y~Rettungsdienst-Menü~s~ zu öffnen.',
  ['deposit_amount'] = 'Menge deponieren',
  ['money_withdraw'] = 'Menge abheben',
  ['fast_travel'] = 'Drücke ~INPUT_CONTEXT~ zu schnellreisen.',
  ['open_pharmacy'] = 'Drücke ~INPUT_CONTEXT~ um die Apotheke zu öffnen.',
  ['pharmacy_menu_title'] = 'Apotheke',
  ['pharmacy_take'] = 'Nimm <span style="color:blue;">%s</span>',
  ['medikit'] = 'Medikit',
  ['bandage'] = 'Verbände',
  ['max_item'] = 'Du trägst bereits genug.',
  -- F6 Menu
  ['ems_menu'] = 'Rettungsdienst-Menü',
  ['ems_menu_title'] = 'Rettungsdienst-Menü',
  ['ems_menu_revive'] = 'Spieler wiederbeleben',
  ['ems_menu_putincar'] = 'In Fahrzeug stecken',
  ['ems_menu_small'] = 'Kleine Wunden behandeln',
  ['ems_menu_big'] = 'Ernsthafte Verletzungen behandeln',
  -- Phone
  ['alert_ambulance'] = 'Rettungsdienst alarmieren',
  -- Death
  ['respawn_available_in'] = 'Respawn in ~b~%s Minuten %s Sekunden~s~ verfügbar',
  ['respawn_bleedout_in'] = 'Du wirst in ~b~%s Minuten %s Sekunden~s~ ausbluten\n',
  ['respawn_bleedout_prompt'] = 'Drücke [~b~E~s~] um zu respawnen',
  ['respawn_bleedout_fine'] = 'Drücke [~b~E~s~] um für ~g~$%s~s~ zu respawnen',
  ['respawn_bleedout_fine_msg'] = 'Du hast ~r~$%s~s~ bezahlt, um zu respawnen.',
  ['distress_send'] = 'Drücke [~b~G~s~] um ein Notsignal zu senden',
  ['distress_sent'] = 'Notsignal wurde an den Rettungsdienst übermittelt!',
  ['combatlog_message'] = 'Du wurdest respawnt, da du den Server verlassen hast, als du gestorben bist.',
  -- Revive
  ['revive_help'] = 'Belebe einen Spieler wieder',
  -- Item
  ['used_medikit'] = 'Du hast ~y~ein~s~ Medikit verwendet',
  ['used_bandage'] = 'Du hast ~y~einen~s~ Verband verwendet',
  ['not_enough_medikit'] = 'Du hast keine ~b~Medikits~s~.',
  ['not_enough_bandage'] = 'Du hast keine ~b~Verbände~s~.',
  ['healed'] = 'Du wurdest behandelt.',
  -- Blips
  ['blip_hospital'] = 'Krankenhaus',
  ['blip_dead'] = 'Bewusstloser Spieler',
}
