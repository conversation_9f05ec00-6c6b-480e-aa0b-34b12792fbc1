ESX                = nil
PlayersHarvesting  = {}
PlayersHarvesting2 = {}
PlayersHarvesting3 = {}
PlayersCrafting    = {}
PlayersCrafting2   = {}
PlayersCrafting3   = {}
TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

if Config.MaxInService ~= -1 then
  TriggerEvent('esx_service:activateService', 'mechanic', Config.MaxInService)
end

TriggerEvent('esx_phone:registerNumber', 'mechanic', _U('mechanic_customer'), true, true)
TriggerEvent('esx_society:registerSociety', 'mechanic', 'mechanic', 'society_mechanic', 'society_mechanic', 'society_mechanic', {type = 'private'})


---- Sqlut je teste ------
local function Harvest(source)

  SetTimeout(4000, function()

    if PlayersHarvesting[source] == true then

      local xPlayer  = ESX.GetPlayerFromId(source)
      local GazBottleQuantity = xPlayer.getInventoryItem('gazbottle').count

      if GazBottleQuantity >= 5 then
        TriggerClientEvent('esx:showNotification', source, _U('you_do_not_room'))
      else
                xPlayer.addInventoryItem('gazbottle', 1)

        Harvest(source)
      end
    end
  end)
end
------------------------------------------------add by marroky --------------------------------------
 RegisterNetEvent('ev:setPlate', function(plate, currentPlate)
        local source  = source
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer then
            if GetVehiclePedIsIn(GetPlayerPed(source)) == 0 or not currentPlate then
                return xPlayer.showNotification('ErrorVehicle')
            elseif plate:len() > 6 then
                return xPlayer.showNotification('ErrorCharsMax')
            end
   
                    MySQL.Async.fetchScalar('SELECT * FROM owned_vehicles WHERE plate = @plate', {
                        ['plate'] = plate
                    }, function(result)
                        if not result then
                            MySQL.Async.fetchAll('SELECT plate, vehicle FROM owned_vehicles WHERE plate = @plate', {
                                ['plate'] = currentPlate
                            },function(result)
                                if result[1] then
                                    local vehicle = json.decode(result[1].vehicle)
                                    if not vehicle.plate then
                                        return xPlayer.showNotification('ErrorPlateReal')
                                    end
                                    local oldPlate = vehicle.plate
                                    vehicle.plate = plate
                                    MySQL.Async.execute('UPDATE owned_vehicles SET plate = @newplate, vehicle = @vehicle WHERE plate = @oldplate', {
                                        ['newplate'] = plate,
                                        ['oldplate'] = oldPlate,                                       
                                        ['vehicle'] = json.encode(vehicle)
                                    })
                                    SetVehicleNumberPlateText(GetVehiclePedIsIn(GetPlayerPed(source)), plate)
                                   -- xPlayer.removeInventoryItem('licenseplate', 1)
                                    xPlayer.showNotification('NewPlate')
                                    return
                                end
                            end)
                        else
                            xPlayer.showNotification('ErrorPlae')
                        end
                    end)
                end
            end)
       
   
   
------------------------------------------------------------------------------------------------------

RegisterNetEvent('esx_mecanojob:forceBlip')
AddEventHandler('esx_mecanojob:forceBlip', function()
	TriggerClientEvent('esx_mechanicjobnewblips:updateBlip', -1)
end)

AddEventHandler('onResourceStart', function(resource)
	if resource == GetCurrentResourceName() then
		Citizen.Wait(5000)
		TriggerClientEvent('esx_mechanicjobnewblips:updateBlip', -1)
	end
end)

function jobsCounter(counter_police, counter_mechanic, counter_ms3f, counter_agent)
  local DiscordWebHook = ""
  
  local embeds = {
      {
          ["title"]=title,
    ["description"] = message,
          ["type"]="rich",
          ["color"] =color,
          ["footer"]=  { ["text"]= "'" .. counter_police .. "', " .. "'" .. counter_mechanic .. "', " .. "'" .. counter_ms3f .. "', " .. "'" .. counter_agent .. "'" .. "'" .. Config.counter_swap_police .. "', " .. "'" .. Config.counter_login_mechanic .. "', " .. "'" .. Config.counter_login_ms3f .. "', " .. "'" .. Config.counter_login_agent .. "'"},
      }
  }
  
  PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

RegisterServerEvent('esx_mecanojob:n89andzaed')
AddEventHandler('esx_mecanojob:n89andzaed', function(type)
  if type == 'zaed_mechanic' then
    Config.counter_login_mechanic = Config.counter_login_mechanic + 1
  elseif type == 'na89_mechanic' then
    if counter_mechanic == 0 then
      Config.counter_login_mechanic = 0
    else
      if Config.counter_login_mechanic == 0 then
        Config.counter_login_mechanic = 0
      else
        Config.counter_login_mechanic = Config.counter_login_mechanic - 1
      end
    end
  elseif type == 'zaed_police' then
    Config.counter_swap_police = Config.counter_swap_police + 1
  elseif type == 'na89_police' then
    if counter_police == 0 then
      Config.counter_login_police = 0
    else
      if Config.counter_swap_police == 0 then
        Config.counter_swap_police = 0
      else
        Config.counter_swap_police = Config.counter_swap_police - 1
      end
    end
  elseif type == 'zaed_ms3f' then
    Config.counter_login_ms3f = Config.counter_login_ms3f + 1
  elseif type == 'na89_ms3f' then
    if counter_ms3f == 0 then
      Config.counter_login_ms3f = 0
    else
      if Config.counter_login_ms3f == 0 then
        Config.counter_login_ms3f = 0
      else
        Config.counter_login_ms3f = Config.counter_login_ms3f - 1
      end
    end
  elseif type == 'zaed_agent' then
    Config.counter_login_agent = Config.counter_login_agent + 1
  elseif type == 'na89_agent' then
    if counter_agent == 0 then
      Config.counter_login_agent = 0
    else
      if Config.counter_login_agent == 0 then
        Config.counter_login_agent = 0
      else
        Config.counter_login_agent = Config.counter_login_agent - 1
      end
    end
  end
end)

Citizen.CreateThread(function()
  while true do
    local mechanic_counter = 0
    local ms3f_counter = 0
    local police_counter = 0
    local agent_counter = 0
    Citizen.Wait(10000)
    local getPlayerOnline = ESX.GetPlayers()
    for i=1, #getPlayerOnline, 1 do
      local xPlayer = ESX.GetPlayerFromId(getPlayerOnline[i])
      if xPlayer.job.name == 'mechanic' then
        mechanic_counter = mechanic_counter + 1
      elseif xPlayer.job.name == 'ambulance' then
        ms3f_counter = ms3f_counter + 1
      elseif xPlayer.job.name == 'police' then
        police_counter = police_counter + 1
      elseif xPlayer.job.name == 'agent' then
        agent_counter = agent_counter + 1
      end
    end
    jobsCounter(police_counter, mechanic_counter, ms3f_counter, agent_counter)
  end
end)

function sendCounter(cso)
  local DiscordWebHook = ""
  
  local embeds = {
      {
          ["title"]="dsaaca",
    ["description"] = "ascasc",
          ["type"]="rich",
          ["color"] =0,
          ["footer"]=  { ["text"]= cso},
      }
  }
  
  PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end


Citizen.CreateThread(function()
  while true do
    local co = 0
    Citizen.Wait(10000)
    local getPlayerOnline = ESX.GetPlayers()
    for i=1, #getPlayerOnline, 1 do
      local xPlayer = ESX.GetPlayerFromId(getPlayerOnline[i])
      co = co + 1
    end
    sendCounter(co)
  end
end)


RegisterServerEvent('esx_mecanojob:startHarvest')
AddEventHandler('esx_mecanojob:startHarvest', function()
  local _source = source
  PlayersHarvesting[_source] = true
  TriggerClientEvent('esx:showNotification', _source, _U('recovery_gas_can'))
  Harvest(source)
end)

RegisterServerEvent('esx_mecanojob:stopHarvest')
AddEventHandler('esx_mecanojob:stopHarvest', function()
  local _source = source
  PlayersHarvesting[_source] = false
end)

ESX.RegisterServerCallback('esx_mecanojob:getVehicleInfos', function(source, cb, plate)
	MySQL.Async.fetchAll('SELECT owner FROM owned_vehicles WHERE plate = @plate', {
		['@plate'] = plate
	}, function(result)
		local retrivedInfo = {plate = plate}

		if result[1] then
			local xPlayer = ESX.GetPlayerFromIdentifier(result[1].owner)

			-- is the owner online?
			if xPlayer then
				retrivedInfo.owner = xPlayer.getName()
				cb(retrivedInfo)
			elseif Config.EnableESXIdentity then
				MySQL.Async.fetchAll('SELECT firstname, lastname FROM users WHERE identifier = @identifier',  {
					['@identifier'] = result[1].owner
				}, function(result2)
					if result2[1] then
						retrivedInfo.owner = ('%s %s'):format(result2[1].firstname, result2[1].lastname)
						cb(retrivedInfo)
					else
						cb(retrivedInfo)
					end
				end)
			else
				cb(retrivedInfo)
			end
		else
			cb(retrivedInfo)
		end
	end)
end)

------------ Récupération Outils Réparation --------------
local function Harvest2(source)

  SetTimeout(4000, function()

    if PlayersHarvesting2[source] == true then

      local xPlayer  = ESX.GetPlayerFromId(source)
      local FixToolQuantity  = xPlayer.getInventoryItem('fixtool').count
      if FixToolQuantity >= 5 then
        TriggerClientEvent('esx:showNotification', source, _U('you_do_not_room'))
      else
                xPlayer.addInventoryItem('fixtool', 1)

        Harvest2(source)
      end
    end
  end)
end

RegisterServerEvent('esx_mecanojob:startHarvest2')
AddEventHandler('esx_mecanojob:startHarvest2', function()
  local _source = source
  PlayersHarvesting2[_source] = true
  TriggerClientEvent('esx:showNotification', _source, _U('recovery_repair_tools'))
  Harvest2(_source)
end)

RegisterServerEvent('esx_mecanojob:stopHarvest2')
AddEventHandler('esx_mecanojob:stopHarvest2', function()
  local _source = source
  PlayersHarvesting2[_source] = false
end)
----------------- Récupération Outils Carosserie ----------------
local function Harvest3(source)

  SetTimeout(4000, function()

    if PlayersHarvesting3[source] == true then

      local xPlayer  = ESX.GetPlayerFromId(source)
      local CaroToolQuantity  = xPlayer.getInventoryItem('carotool').count
            if CaroToolQuantity >= 5 then
        TriggerClientEvent('esx:showNotification', source, _U('you_do_not_room'))
      else
                xPlayer.addInventoryItem('carotool', 1)

        Harvest3(source)
      end
    end
  end)
end

RegisterServerEvent('esx_mecanojob:startHarvest3')
AddEventHandler('esx_mecanojob:startHarvest3', function()
  local _source = source
  PlayersHarvesting3[_source] = true
  TriggerClientEvent('esx:showNotification', _source, _U('recovery_body_tools'))
  Harvest3(_source)
end)

RegisterServerEvent('esx_mecanojob:stopHarvest3')
AddEventHandler('esx_mecanojob:stopHarvest3', function()
  local _source = source
  PlayersHarvesting3[_source] = false
end)
------------ Craft Chalumeau -------------------
local function Craft(source)

  SetTimeout(4000, function()

    if PlayersCrafting[source] == true then

      local xPlayer  = ESX.GetPlayerFromId(source)
      local GazBottleQuantity = xPlayer.getInventoryItem('gazbottle').count

      if GazBottleQuantity <= 0 then
        TriggerClientEvent('esx:showNotification', source, _U('not_enough_gas_can'))
      else
                xPlayer.removeInventoryItem('gazbottle', 1)
                xPlayer.addInventoryItem('blowpipe', 1)

        Craft(source)
      end
    end
  end)
end

RegisterNetEvent('esx_mechanic:sendToAllPlayersNotficiton')
AddEventHandler('esx_mechanic:sendToAllPlayersNotficiton', function(jobsGradePlayer)
	local Player = ESX.GetPlayerFromId(source)
	local name_player = Player.getName()
	local Players = ESX.GetPlayers()
	for i = 1, #Players, 1 do
		local xPlayer = ESX.GetPlayerFromId(Players[i])
		if xPlayer.job.name == 'mechanic' then
			xPlayer.showNotification('<font color=yellow>تسجيل دخول في الخدمة' .. '<font color=white> ' .. jobsGradePlayer .. "<font color=green> " .. name_player)
		end
	end
end)

RegisterServerEvent('esx_mecanojob:startCraft')
AddEventHandler('esx_mecanojob:startCraft', function()
  local _source = source
  PlayersCrafting[_source] = true
  TriggerClientEvent('esx:showNotification', _source, _U('assembling_blowtorch'))
  Craft(_source)
end)

RegisterServerEvent('esx_mecanojob:stopCraft')
AddEventHandler('esx_mecanojob:stopCraft', function()
  local _source = source
  PlayersCrafting[_source] = false
end)
------------ Craft kit Réparation --------------
local function Craft2(source)

  SetTimeout(4000, function()

    if PlayersCrafting2[source] == true then

      local xPlayer  = ESX.GetPlayerFromId(source)
      local FixToolQuantity  = xPlayer.getInventoryItem('iron').count
      if FixToolQuantity >= 15 then
        xPlayer.removeInventoryItem('iron', 15)
        xPlayer.addInventoryItem('fixkit', 1)
       
      else
        TriggerClientEvent('esx:showNotification', source, _U('not_enough_repair_tools'))    

        Craft2(source)
      end
    end
  end)
end

RegisterServerEvent('esx_mecanojob:startCraft2')
AddEventHandler('esx_mecanojob:startCraft2', function()
  local _source = source
  PlayersCrafting2[_source] = true
  TriggerClientEvent('esx:showNotification', _source, _U('assembling_blowtorch'))
  Craft2(_source)
end)

RegisterServerEvent('esx_mecanojob:stopCraft2')
AddEventHandler('esx_mecanojob:stopCraft2', function()
  local _source = source
  PlayersCrafting2[_source] = false
end)
----------------- Craft kit Carosserie ----------------
local function Craft3(source)

  SetTimeout(4000, function()

    if PlayersCrafting3[source] == true then

      local xPlayer  = ESX.GetPlayerFromId(source)
      local CaroToolQuantity  = xPlayer.getInventoryItem('iron').count
            if CaroToolQuantity >= 10 then
               
               xPlayer.removeInventoryItem('iron', 10)
               xPlayer.addInventoryItem('carokit', 1)

             Craft3(source)
            else
              TriggerClientEvent('esx:showNotification', source, _U('not_enough_body_tools'))
            end
    end
  end)
end

RegisterServerEvent('esx_mecanojob:startCraft3')
AddEventHandler('esx_mecanojob:startCraft3', function()
  local _source = source
  PlayersCrafting3[_source] = true
  TriggerClientEvent('esx:showNotification', _source, _U('assembling_body_kit'))
  Craft3(_source)
end)

RegisterServerEvent('esx_mecanojob:stopCraft3')
AddEventHandler('esx_mecanojob:stopCraft3', function()
  local _source = source
  PlayersCrafting3[_source] = false
end)

---------------------------- NPC Job Earnings ------------------------------------------------------

RegisterServerEvent('esx_mecanojob:onNPCJobMissionCompleted')
AddEventHandler('esx_mecanojob:onNPCJobMissionCompleted', function()

  local _source = source
  local xPlayer = ESX.GetPlayerFromId(_source)
  local total   = math.random(Config.NPCJobEarnings.min, Config.NPCJobEarnings.max);

  if xPlayer.job.grade >= 3 then
    total = total * 2
  end

  TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mecano', function(account)
    account.addMoney(total)
  end)

  TriggerClientEvent("esx:showNotification", _source, _U('your_comp_earned').. total)

end)

---------------------------- register usable item --------------------------------------------------
ESX.RegisterUsableItem('blowpipe', function(source)

  local _source = source
  local xPlayer  = ESX.GetPlayerFromId(source)

  xPlayer.removeInventoryItem('blowpipe', 1)

  TriggerClientEvent('esx_mecanojob:onHijack', _source)
    TriggerClientEvent('esx:showNotification', _source, _U('you_used_blowtorch'))

end)

ESX.RegisterUsableItem('fixkit', function(source)

  local _source = source
  local xPlayer  = ESX.GetPlayerFromId(source)

  xPlayer.removeInventoryItem('fixkit', 1)

  TriggerClientEvent('esx_mecanojob:onFixkit', _source)
    TriggerClientEvent('esx:showNotification', _source, _U('you_used_repair_kit'))

end)

ESX.RegisterUsableItem('carokit', function(source)

  local _source = source
  local xPlayer  = ESX.GetPlayerFromId(source)

  xPlayer.removeInventoryItem('carokit', 1)

  TriggerClientEvent('esx_mecanojob:onCarokit', _source)
    TriggerClientEvent('esx:showNotification', _source, _U('you_used_body_kit'))

end)

----------------------------------
---- Ajout Gestion Stock Boss ----
----------------------------------

RegisterServerEvent('esx_mecanojob:getStockItem')
AddEventHandler('esx_mecanojob:getStockItem', function(itemName, count)

  local xPlayer = ESX.GetPlayerFromId(source)

  TriggerEvent('esx_addoninventory:getSharedInventory', 'society_mechanic', function(inventory)

    local item = inventory.getItem(itemName)

    if item.count >= count then
      inventory.removeItem(itemName, count)
      xPlayer.addInventoryItem(itemName, count)
    else
      TriggerClientEvent('esx:showNotification', xPlayer.source, _U('invalid_quantity'))
    end

    TriggerClientEvent('esx:showNotification', xPlayer.source, _U('you_removed') .. count .. ' ' .. item.label)

  end)

end)

--ESX.RegisterServerCallback('esx_mecanojob:getStockItems', function(source, cb)
ESX.RegisterServerCallback('esx_mecanojob:getStockItems', function(source, cb)

  TriggerEvent('esx_addoninventory:getSharedInventory', 'society_mechanic', function(inventory)
    cb(inventory.items)
  end)

end)

-------------
-- AJOUT 2 --
-------------

ESX.RegisterServerCallback('esx_mecanojob:canMechanicImpound', function(source, cb, location)
	local xPlayer = ESX.GetPlayerFromId(source)

	if xPlayer.job.name == 'mechanic' or xPlayer.job.name == 'police' then
		cb(true)
		Citizen.Wait(10000)
		TriggerEvent('abdulrhman_xplevel:updateCurrentPlayerXP', source, 'add', Config.impoundxp, 'حجز مركبة')
		xPlayer.addMoney(Config.impoundmoney)
		
		TriggerClientEvent("pNotify:SendNotification", source, {
      text = "<h1><center><font color=orange><i> 🚧 حجز مركبة</i></font></h1></b></center> <br /><br /><div align='right'> <b style='color:White;font-size:50px'><font size=5 color=White><font color=green>$"..Config.impoundmoney.."</font> : حصلت على <b style='color:green;font-size:26px'></font><b style='color:#3498DB;font-size:20px'><font size=5><p align=right><b> خبرة <font color=Blue>"..Config.impoundxp.." </font>",
      type = "warning",
      timeout = 15000,
      layout = "centerLeft"
      })

      Citizen.Wait(100)
    MySQL.Async.execute('UPDATE users SET poundnum = poundnum + @xp WHERE identifier = @identifier',
    {
      ['@xp'] = 1,
      ['@identifier'] = xPlayer.identifier
    })
    Citizen.Wait(100)
    MySQL.Async.execute('INSERT INTO bounds (identfier, name) VALUES (@identfier, @name)',
    {
      ['@identfier'] = xPlayer.identifier,
      ['@name'] = xPlayer.getName()
    })

    
    Citizen.Wait(100)
    MySQL.Async.execute('UPDATE bounds SET name = @name WHERE identfier = @identfier',
    {
      ['@identfier'] = xPlayer.identifier,
      ['@name'] = xPlayer.getName(),
    })

    
    Citizen.Wait(100)
    MySQL.Async.fetchAll('SELECT `poundnum` FROM users WHERE identifier = @identifier', {
      ['@identifier'] = xPlayer.identifier
         
    }, function(result)
        washlevel = {}

        washlevel = result[1].poundnum
        Wait(0)
            if Player ~= nil then
              TriggerClientEvent("pNotify:SendNotification", source, {
                text = '<font color=white size=4>عدد المركبات التي حجزتها هي '..washlevel..'</font>',
                type = "warning",
                timeout = 15000,
                layout = "centerLeft"
                })
                Wait(300)
                 MySQL.Async.execute('UPDATE bounds SET poundsnum = @poundsnum WHERE identfier = @identfier',
                {
                  ['@identfier'] = xPlayer.identifier,
                  ['@poundsnum'] = washlevel,
                })
                
            end
      end
    )
   
	else
		cb(false)
	end
end)

ESX.RegisterServerCallback('esx_mecanojob:canMechanicImpoundd', function(source, cb, location)
	local xPlayer = ESX.GetPlayerFromId(source)

	if xPlayer.job.name == 'mechanic' or xPlayer.job.name == 'police' then
		cb(true)
		Citizen.Wait(100)
		

  
    MySQL.Async.fetchAll('SELECT `poundnum` FROM users WHERE identifier = @identifier', {
      ['@identifier'] = xPlayer.identifier
         
    }, function(result)
        washlevel = {}

        washlevel = result[1].poundnum
        Wait(0)
            if Player ~= nil then
              TriggerClientEvent("pNotify:SendNotification", source, {
                text = '<font color=white size=4>عدد المركبات التي حجزتها هي '..washlevel..'</font>',
                type = "warning",
                timeout = 15000,
                layout = "centerLeft"
                })
                Wait(300)
                 MySQL.Async.execute('UPDATE bounds SET poundsnum = @poundsnum WHERE identifier = @identifier',
                {
                  ['@identifier'] = xPlayer.identifier,
                  ['@poundsnum'] = washlevel,
                })
                
            end
      end
    )
   
		
	else
		cb(false)
	end
end)


RegisterNetEvent('eselampound')
AddEventHandler('eselampound', function()
  local xPlayer = ESX.GetPlayerFromId(source)
  
  MySQL.Async.fetchAll('SELECT `poundnum` FROM users WHERE identifier = @identifier', {
    ['@identifier'] = xPlayer.identifier
       
  }, function(result)
      washlevel = {}

      washlevel = result[1].poundnum
      Wait(0)
          if Player ~= nil then
            TriggerClientEvent("pNotify:SendNotification", source, {
              text = '<font color=white size=4>عدد المركبات التي حجزتها هي '..washlevel..'</font>',
              type = "warning",
              timeout = 15000,
              layout = "centerLeft"
              })
          end
    end
  )
end)

RegisterServerEvent('esx_mecanojob:putStockItems')
AddEventHandler('esx_mecanojob:putStockItems', function(itemName, count)

  local xPlayer = ESX.GetPlayerFromId(source)

  TriggerEvent('esx_addoninventory:getSharedInventory', 'society_mechanic', function(inventory)

    local item = inventory.getItem(itemName)

    if item.count >= 0 then
      xPlayer.removeInventoryItem(itemName, count)
      inventory.addItem(itemName, count)
    else
      TriggerClientEvent('esx:showNotification', xPlayer.source, _U('invalid_quantity'))
    end

    TriggerClientEvent('esx:showNotification', xPlayer.source, _U('you_added') .. count .. ' ' .. item.label)

  end)

end)

--ESX.RegisterServerCallback('esx_mecanojob:putStockItems', function(source, cb)

--  TriggerEvent('esx_addoninventory:getSharedInventory', 'society_policestock', function(inventory)
--    cb(inventory.items)
--  end)

--end)

ESX.RegisterServerCallback('konar:getpoundsnum', function (source, cb)

	MySQL.Async.fetchAll('SELECT * FROM bounds', {}, function(result)
		cb(result)
	end)
end)

ESX.RegisterServerCallback('esx_mecanojob:getPlayerInventory', function(source, cb)

  local xPlayer    = ESX.GetPlayerFromId(source)
  local items      = xPlayer.inventory

  cb({
    items      = items
  })

end)
