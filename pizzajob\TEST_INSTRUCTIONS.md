# تعليمات اختبار النظام الجديد - Testing Instructions

## خطوات الاختبار / Testing Steps

### 1. اختبار التفاعل مع الطباخ
1. تأكد من أن لديك وظيفة `pizzajob`
2. اذهب إلى موقع الطباخ: `-1194.93, -894.138, 13.886`
3. اقترب من الطباخ (ضمن مسافة 3.0 متر)
4. يجب أن تظهر رسالة "[E] تحدث مع الطباخ" في أعلى الشاشة
5. اضغط على مفتاح E
6. يجب أن تفتح قائمة الدلفري

### 2. اختبار قائمة الطباخ
عند فتح القائمة، يجب أن ترى:
- **بدء التوصيل** (إذا لم تكن تقوم بالتوصيل)
- **إيقاف التوصيل** (إذا كنت تقوم بالتوصيل)
- **عرض المتصدرين**
- **استعادة ملابسك الأصلية** (إذا كان لديك ملابس محفوظة)

### 3. اختبار بدء التوصيل
1. اختر "بدء التوصيل" من القائمة
2. يجب أن تحصل على دراجة توصيل
3. يجب أن تظهر نقطة على الخريطة لموقع التسليم
4. اذهب إلى موقع التسليم

### 4. اختبار التسليم
1. في موقع التسليم، ابحث عن البوت الأصفر
2. اقترب من البوت (ضمن مسافة 3.5 متر)
3. يجب أن تظهر رسالة "[E] تسليم الطلب" في أعلى الشاشة
4. اضغط على مفتاح E
5. يجب أن تبدأ عملية التسليم مع شريط التقدم

### 5. اختبار الأمان
- تأكد من أن النصوص تظهر فقط عندما تكون لديك الوظيفة المطلوبة
- تأكد من أن النصوص تختفي عندما تبتعد
- تأكد من أن النصوص لا تظهر عندما تكون في مركبة

## المشاكل المحتملة وحلولها / Potential Issues and Solutions

### المشكلة: لا تظهر النصوص
**الحل:**
1. تأكد من أن لديك وظيفة `pizzajob`
2. تأكد من إعادة تشغيل المورد
3. تحقق من وحدة التحكم للأخطاء

### المشكلة: لا تعمل قائمة الطباخ
**الحل:**
1. تأكد من أن `ox_lib` مثبت ويعمل
2. تحقق من وحدة التحكم للأخطاء
3. تأكد من أن الإعدادات صحيحة

### المشكلة: لا يعمل التسليم
**الحل:**
1. تأكد من أنك قريب من البوت
2. تأكد من أن لديك دراجة التوصيل
3. تأكد من أنك لست في مركبة

## الأوامر المفيدة للاختبار / Useful Testing Commands

```lua
-- للحصول على وظيفة الدلفري (ESX)
/setjob [player_id] pizzajob 0

-- للحصول على وظيفة الدلفري (QB)
/job pizzajob

-- للتحقق من الموقع الحالي
/coords

-- لإعادة تشغيل المورد
/restart pizzajob
```

## نصائح للاختبار / Testing Tips

1. **اختبر مع أكثر من لاعب** للتأكد من أن النظام يعمل للجميع
2. **اختبر بدون الوظيفة** للتأكد من أن النظام محمي
3. **اختبر في مركبة** للتأكد من أن النصوص لا تظهر
4. **اختبر المسافات المختلفة** للتأكد من دقة الكشف

## التحقق من الأداء / Performance Check

- راقب استخدام الذاكرة
- تحقق من عدم وجود تأخير في الاستجابة
- تأكد من أن النصوص تظهر وتختفي بسلاسة

## الإبلاغ عن المشاكل / Bug Reporting

إذا وجدت أي مشاكل، يرجى تسجيل:
1. الخطوات التي أدت للمشكلة
2. رسائل الخطأ في وحدة التحكم
3. إعدادات الخادم (ESX/QB)
4. إصدار ox_lib المستخدم
