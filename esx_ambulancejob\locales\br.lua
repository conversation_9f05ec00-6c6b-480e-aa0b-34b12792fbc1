Locales['br'] = {
  -- Cloakroom
  ['cloakroom'] = 'guarda roupa',
  ['ems_clothes_civil'] = 'roupa normal',
  ['ems_clothes_ems'] = 'entrar como socorrista',
  -- Vehicles
  ['ambulance'] = 'ambulancia',
  ['helicopter_prompt'] = 'pressione ~INPUT_CONTEXT~ para acessar a ~y~Helicopter Actions~s~.',
  ['garage_prompt'] = 'pressione ~INPUT_CONTEXT~ para entra no ~y~Vehicle Actions~s~.',
  ['garage_title'] = 'ações do veiculo',
  ['garage_stored'] = 'guardado',
  ['garage_notstored'] = 'não foi guardado',
  ['garage_storing'] = 'estamos tentando guardar o veículo, verifique se não há cidadões ao seu redor.',
  ['garage_has_stored'] = 'o veículo foi guardado na sua garagem',
  ['garage_has_notstored'] = 'nem um dos seus veiculos foi encontrado proximo',
  ['garage_notavailable'] = 'o seu veículo não está guardado na garagem.',
  ['garage_blocked'] = 'não há pontos de spawn disponíveis!',
  ['garage_empty'] = 'você não tem veículos na sua garagem.',
  ['garage_released'] = 'seu veículo foi liberado da garagem.',
  ['garage_store_nearby'] = 'não há veículos próximos.',
  ['garage_storeditem'] = 'abrir garagem',
  ['garage_storeitem'] = 'veículo da loja na garagem',
  ['garage_buyitem'] = 'loja de veículos',
  ['shop_item'] = '$%s',
  ['vehicleshop_title'] = 'loja de veículos',
  ['vehicleshop_confirm'] = 'você quer comprar este veículo?',
  ['vehicleshop_bought'] = 'você comprou ~y~%s~s~ para ~g~$%s~s~',
  ['vehicleshop_money'] = 'você não pode pagar esse veículo',
  ['vehicleshop_awaiting_model'] = 'o veículo está atualmente ~g~DOWNLOADING & LOADING~s~, por favor aguarde',
  ['confirm_no'] = 'não',
  ['confirm_yes'] = 'sim',
  -- Action Menu
  ['revive_inprogress'] = 'reanimação em andamento',
  ['revive_complete'] = 'você ressuscitou ~y~%s~s~',
  ['revive_complete_award'] = 'você ressuscitou ~y~%s~s~, ~g~$%s~s~',
  ['revive_fail_offline'] = 'esse cidadão não está mais na cidade',
  ['heal_inprogress'] = 'você está curando!',
  ['heal_complete'] = 'você curou ~y~%s~s~',
  ['no_players'] = 'nenhum cidadão nas proximidades',
  ['player_not_unconscious'] = 'não está inconsciente',
  ['player_not_conscious'] = 'esse cidadão não está consciente!',
  -- Boss Menu
  ['boss_actions'] = 'menu administração',
  -- Misc
  ['invalid_amount'] = '~r~Quantidade inválida',
  ['actions_prompt'] = 'aperte ~INPUT_CONTEXT~ para entra ~y~Ambulance Actions~s~.',
  ['deposit_amount'] = 'quantidade inválida para deposito',
  ['money_withdraw'] = 'quantidade a ser retirada',
  ['fast_travel'] = 'aperte ~INPUT_CONTEXT~ para mover-se rapidamente.',
  ['open_pharmacy'] = 'aperte ~INPUT_CONTEXT~ para abrir a farmácia.',
  ['pharmacy_menu_title'] = 'farmácia',
  ['pharmacy_take'] = 'tomar <span style="color:blue;">%s</span>',
  ['medikit'] = 'seringa',
  ['bandage'] = 'vendagem',
  ['max_item'] = 'você não pode carregar mais',
  -- F6 Menu
  ['ems_menu'] = 'interação com cidadão',
  ['ems_menu_title'] = 'ambulância - Interação com cidadão',
  ['ems_menu_revive'] = 'reanimar',
  ['ems_menu_putincar'] = 'colocar no veiculo',
  ['ems_menu_small'] = 'curar pequenas feridas',
  ['ems_menu_big'] = 'tratando lesões graves',
  ['ems_menu_search'] = 'paciente não encontrado',
  -- Phone
  ['alert_ambulance'] = 'alerta socorrista',
  -- Death
  ['respawn_available_in'] = 'você irar reviver em ~b~%s minutos %s segundos~s~',
  ['respawn_bleedout_in'] = 'você vai sangrar ~b~%s minutos %s segundos~s~\n',
  ['respawn_bleedout_prompt'] = 'aguarde [~b~E~s~] para reviver',
  ['respawn_bleedout_fine'] = 'aguarde [~b~E~s~] para reviver por ~g~$%s~s~',
  ['respawn_bleedout_fine_msg'] = 'você precisa pagar ~r~$%s~s~ para reviver',
  ['distress_send'] = 'pressione [~b~G~s~] enviar sinal de socorro',
  ['distress_sent'] = 'sinal de socorro foi enviado para as unidades disponíveis!',
  ['combatlog_message'] = 'você foi revivido porque deixou o servidor anteriormente quando estava morto.',
  -- Revive
  ['revive_help'] = 'reviver um cidadão',
  -- Item
  ['used_medikit'] = 'você usou 1x Seringa',
  ['used_bandage'] = 'você usou 1x Vendagem',
  ['not_enough_medikit'] = 'você não tem ~b~Seringa~s~.',
  ['not_enough_bandage'] = 'você não tem ~b~Vendagem~s~.',
  ['healed'] = 'você foi tratado.',
  -- Blips
  ['blip_hospital'] = 'hospital',
  ['blip_dead'] = 'cidadão inconsciente',
}
