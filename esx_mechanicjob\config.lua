Config = {}
Config.Locale                     = 'en'
Config.counter_login_mechanic = 0
Config.counter_login_ms3f = 0
Config.counter_login_agent = 0
Config.counter_swap_police = 0
Config.DrawDistance               = 20.0 -- How close you need to be in order for the markers to be drawn (in GTA units).
Config.MaxInService               = -1
Config.EnablePlayerManagement     = true -- Enable society managing.
Config.EnableSocietyOwnedVehicles = false

Config.EnableServiceMain = true -- إيقاف تشغيل وتشغيل الحاجة لتسجيل الدخول للعمل و القائمة

Config.NPCSpawnDistance           = 500.0
Config.NPCNextToDistance          = 25.0
Config.NPCJobEarnings             = { min = 15, max = 40 }
Config.NPCJobEarningsXp             = { min = 5, max = 10 }

Config.Vehicles = {
	'adder',
	'asea',
	'asterope',
	'banshee',
	'buffalo',
	'sultan',
	'baller3'
}

Config.Zones = {

	MechanicActions = {

		Pos   = { x = 826.9086, y = -953.7482, z = 22.0876 },
		Size  = { x = 1.0, y = 1.0, z = 1.0 },
		Color = { r = 255, g = 255, b = 0 },
		Type  = 21
	},

	MechanicActions1 = {

		Pos   = { x = 92.7211, y = 6517.1499, z = 31.2553 },
		Size  = { x = 1.0, y = 1.0, z = 1.0 },
		Color = { r = 255, g = 255, b = 0 },
		Type  = 21
	},

	Garage = {
		Pos   = { x = 797.8585, y = -957.6904, z = 25.9064 },
		Size  = { x = 0.0, y = 0.0, z = 0.0 },
		Color = { r = 204, g = 204, b = 0 },
		Type  = -1
	},


--- مدينة لوس سانتوس
	Craft = { 
		Pos   = { x = 842.4626, y = -977.1495, z = 26.4989 }, 
		Size  = { x = 0.5, y = 0.2, z = 0.5 },
		Color = { r = 50, g = 150, b = 204 },
		Type  = 21
	},
	
	Craft1 = { 
		Pos   = { x = 81.6005, y = 6506.1445, z = 31.2552 }, 
		Size  = { x = 0.5, y = 0.2, z = 0.5 },
		Color = { r = 50, g = 150, b = 204 },
		Type  = 21
	},

	CraftTwo = {
		Pos   = { x = 842.4254, y = -968.2736, z = 26.4989 },
		Size  = { x = 0.5, y = 0.2, z = 0.5 },
		Color = { r = 50, g = 150, b = 204 },
		Type  = 21
	},

--- مدينة ساندي شورز

	CraftThree = {
		Pos   = { x = 842.4631, y = -960.3093, z = 26.4989 },
		Size  = { x = 0.5, y = 0.2, z = 0.5 },
		Color = { r = 50, g = 150, b = 204 },
		Type  = 21
	},

	CraftFour = {
		Pos   = { x = 842.4631, y = -960.3093, z = 26.4989 },
		Size  = { x = 0.5, y = 0.2, z = 0.5 },
		Color = { r = 50, g = 150, b = 204 },
		Type  = 21
	},

	CraftFive = { 
		Pos   = { x = 842.4485, y = -951.4526, z = 26.4989 },
		Size  = { x = 0.5, y = 0.2, z = 0.5 },
		Color = { r = 50, g = 150, b = 204 },
		Type  = 21
	},


	VehicleSpawnPoint = {
		Pos   = { x = 794.0495, -955.7833, 25.8420},
		Size  = { x = 1.5, y = 1.5, z = 1.0 },
		Type  = -1
	},

	VehicleDeleter = {
		Pos   = { x = 791.8453, y = -915.9159, z = 25.2744},
		Size  = { x = 2.5, y = 2.5, z = 2.5 },
		Color = { r = 204, g = 0, b = 0 },
		Type  = 36
	},

	VehicleDelivery = {
		Pos   = { x = 791.8453, y = -915.9159, z = 25.2744},
		Size  = { x = 20.0, y = 20.0, z = 3.0 },
		Color = { r = 204, g = 204, b = 0 },
		Type  = -1
	}
}

Config.timeBetwenImpoundMechanic = 100

Config.impoundxp = 100 
Config.impoundmoney = 7000

Config.impound = {
	location = {
		[1] = {label='حجز لوس سانتوس',coords=vector3(401.76,-1631.69,29.29),radius=10.0},
		[2] = {label='حجز ساندي شورز',coords=vector3(1743.81,3629.39,34.57),radius=20.0},
		[3] = {label='حجز بليتو',coords=vector3(-360.17,6071.86,31.5),radius=15.0}
	}
}

Config.Towables = {
	vector3(-2480.9, -212.0, 17.4),
	vector3(-2723.4, 13.2, 15.1),
	vector3(-3169.6, 976.2, 15.0),
	vector3(-3139.8, 1078.7, 20.2),
	vector3(-1656.9, -246.2, 54.5),
	vector3(-1586.7, -647.6, 29.4),
	vector3(-1036.1, -491.1, 36.2),
	vector3(-1029.2, -475.5, 36.4),
	vector3(75.2, 164.9, 104.7),
	vector3(-534.6, -756.7, 31.6),
	vector3(487.2, -30.8, 88.9),
	vector3(-772.2, -1281.8, 4.6),
	vector3(-663.8, -1207.0, 10.2),
	vector3(719.1, -767.8, 24.9),
	vector3(-971.0, -2410.4, 13.3),
	vector3(-1067.5, -2571.4, 13.2),
	vector3(-619.2, -2207.3, 5.6),
	vector3(1192.1, -1336.9, 35.1),
	vector3(-432.8, -2166.1, 9.9),
	vector3(-451.8, -2269.3, 7.2),
	vector3(939.3, -2197.5, 30.5),
	vector3(-556.1, -1794.7, 22.0),
	vector3(591.7, -2628.2, 5.6),
	vector3(1654.5, -2535.8, 74.5),
	vector3(1642.6, -2413.3, 93.1),
	vector3(1371.3, -2549.5, 47.6),
	vector3(383.8, -1652.9, 37.3),
	vector3(27.2, -1030.9, 29.4),
	vector3(229.3, -365.9, 43.8),
	vector3(-85.8, -51.7, 61.1),
	vector3(-4.6, -670.3, 31.9),
	vector3(-111.9, 92.0, 71.1),
	vector3(-314.3, -698.2, 32.5),
	vector3(-366.9, 115.5, 65.6),
	vector3(-592.1, 138.2, 60.1),
	vector3(-1613.9, 18.8, 61.8),
	vector3(-1709.8, 55.1, 65.7),
	vector3(-521.9, -266.8, 34.9),
	vector3(-451.1, -333.5, 34.0),
	vector3(322.4, -1900.5, 25.8)
}

for k,v in ipairs(Config.Towables) do
	Config.Zones['Towable' .. k] = {
		Pos   = v,
		Size  = { x = 1.5, y = 1.5, z = 1.0 },
		Color = { r = 204, g = 204, b = 0 },
		Type  = -1
	}
end

return Config