ESX = exports['es_extended']:getSharedObject()
local HasAlreadyEnteredMarker, LastZone = false, nil
local CurrentAction, CurrentActionMsg, CurrentActionData = nil, '', {}
local CurrentlyTowedVehicle, Blips, NPCOnJob, NPCTargetTowable, NPCTargetTowableZone = nil, {}, false, nil, nil
local NPCHasSpawnedTowable, NPCLastCancel, NPCHasBeenNextToTowable, NPCTargetDeleterZone = false, GetGameTimer() - 5 * 60000, false, false
local isDead, isBusy = false, false
local isOnDuty = false -- addon service by abdulrhman

ESX = nil

Citizen.CreateThread(function()
	while ESX == nil do
		TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
		Citizen.Wait(0)
	end

	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(10)
	end

	ESX.PlayerData = ESX.GetPlayerData()
	if ESX.PlayerData.job.name == 'police' or ESX.PlayerData.job.name == 'agent' or ESX.PlayerData.job.name == 'ambulance' or ESX.PlayerData.job.name == 'mechanic'  or ESX.PlayerData.job.name == 'mec' then
		if GetResourceState("rp-radio") == "started" then
			exports["rp-radio"]:GivePlayerAccessToFrequencies(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20)
		end
	elseif ESX.PlayerData.job.name == 'admin' then
		if GetResourceState("rp-radio") == "started" then
			exports["rp-radio"]:GivePlayerAccessToFrequencies(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20)
		end
		Citizen.Wait(5)
	end
	TriggerServerEvent('esx_mecanojob:forceBlip')
end)


local Cooldown_count = 0
	
	local function Cooldown(sec)
		CreateThread(function()
			Cooldown_count = sec
			while Cooldown_count ~= 0 do
				Cooldown_count = Cooldown_count - 1
				Wait(1000)
			end	
			Cooldown_count = 0
		end)	
	end

function SelectRandomTowable()
	local index = GetRandomIntInRange(1,  #Config.Towables)

	for k,v in pairs(Config.Zones) do
		if v.Pos.x == Config.Towables[index].x and v.Pos.y == Config.Towables[index].y and v.Pos.z == Config.Towables[index].z then
			return k
		end
	end
end

function StartNPCJob()
	NPCOnJob = true

	NPCTargetTowableZone = SelectRandomTowable()
	local zone = Config.Zones[NPCTargetTowableZone]

	Blips['NPCTargetTowableZone'] = AddBlipForCoord(zone.Pos.x,  zone.Pos.y,  zone.Pos.z)
	SetBlipRoute(Blips['NPCTargetTowableZone'], true)

	ESX.ShowNotification(_U('drive_to_indicated'))
end

function StopNPCJob(cancel)
	if Blips['NPCTargetTowableZone'] then
		RemoveBlip(Blips['NPCTargetTowableZone'])
		Blips['NPCTargetTowableZone'] = nil
	end

	if Blips['NPCDelivery'] then
		RemoveBlip(Blips['NPCDelivery'])
		Blips['NPCDelivery'] = nil
	end

	Config.Zones.VehicleDelivery.Type = -1

	NPCOnJob = false
	NPCTargetTowable  = nil
	NPCTargetTowableZone = nil
	NPCHasSpawnedTowable = false
	NPCHasBeenNextToTowable = false

	if cancel then
		ESX.ShowNotification(_U('mission_canceled'))
	else
		--TriggerServerEvent('esx_mecanojob:onNPCJobCompleted')
	end
end

local blipsCops = {}

function createBlip(id)
	local ped = GetPlayerPed(id)
	local blip = GetBlipFromEntity(ped)
	
	if not DoesBlipExist(blip) then -- Add blip and create head display on player
		blip = AddBlipForEntity(ped)
		SetBlipSprite(blip, 643)
		SetBlipColour(blip, 47)
		ShowHeadingIndicatorOnBlip(blip, true) -- Player Blip indicator
		SetBlipRotation(blip, math.ceil(GetEntityHeading(ped))) -- update rotation
		BeginTextCommandSetBlipName('STRING')
		AddTextComponentSubstringPlayerName('<FONT FACE="sharlock">ﻲﻜﻴﻧﺎﻜﻴﻣ'..id)
		EndTextCommandSetBlipName(blip)
		SetBlipScale(blip, 0.85) -- set scale
		SetBlipAsShortRange(blip, true)

		table.insert(blipsCops, blip) -- add blip to array so we can remove it later
	end
end

function createBlipmechanic(id)
	local ped = GetPlayerPed(id)
	local blip = GetBlipFromEntity(ped)
	
	if not DoesBlipExist(blip) then -- Add blip and create head display on player
		blip = AddBlipForEntity(ped)
		SetBlipSprite(blip, 643)
		SetBlipColour(blip, 47)
		ShowHeadingIndicatorOnBlip(blip, true) -- Player Blip indicator
		SetBlipRotation(blip, math.ceil(GetEntityHeading(ped))) -- update rotation
		BeginTextCommandSetBlipName('STRING')
		AddTextComponentSubstringPlayerName(GetPlayerName(id))
		EndTextCommandSetBlipName(blip)
		SetBlipScale(blip, 0.85) -- set scale
		SetBlipAsShortRange(blip, true)

		table.insert(blipsCops, blip) -- add blip to array so we can remove it later
	end
end



function createBlipambulance(id)
	local ped = GetPlayerPed(id)
	local blip = GetBlipFromEntity(ped)
	
	if not DoesBlipExist(blip) then -- Add blip and create head display on player
		blip = AddBlipForEntity(ped)
		SetBlipSprite(blip, 198)
		SetBlipColour(blip, 5)
		ShowHeadingIndicatorOnBlip(blip, true) -- Player Blip indicator
		SetBlipRotation(blip, math.ceil(GetEntityHeading(ped))) -- update rotation
		BeginTextCommandSetBlipName('STRING')
		AddTextComponentSubstringPlayerName(GetPlayerName(id))
		EndTextCommandSetBlipName(blip)
		SetBlipScale(blip, 0.85) -- set scale
		SetBlipAsShortRange(blip, true)

		table.insert(blipsCops, blip) -- add blip to array so we can remove it later
	end
end

function createBlip3(id)
	local ped = GetPlayerPed(id)
	local blip = GetBlipFromEntity(ped)
	
	if not DoesBlipExist(blip) then -- Add blip and create head display on player
		blip = AddBlipForEntity(ped)
		SetBlipSprite(blip, 1)
		SetBlipColour(blip, 47)
		ShowHeadingIndicatorOnBlip(blip, true) -- Player Blip indicator
		SetBlipRotation(blip, math.ceil(GetEntityHeading(ped))) -- update rotation
		BeginTextCommandSetBlipName('STRING')
		AddTextComponentSubstringPlayerName(GetPlayerName(id))
		EndTextCommandSetBlipName(blip)
		SetBlipScale(blip, 0.85) -- set scale
		SetBlipAsShortRange(blip, true)

		table.insert(blipsCops, blip) -- add blip to array so we can remove it later
	end
end

function createBlip4(id)
	local ped = GetPlayerPed(id)
	local blip = GetBlipFromEntity(ped)
	
	if not DoesBlipExist(blip) then -- Add blip and create head display on player
		blip = AddBlipForEntity(ped)
		SetBlipSprite(blip, 487)
		SetBlipColour(blip, 21)
		ShowHeadingIndicatorOnBlip(blip, true) -- Player Blip indicator
		SetBlipRotation(blip, math.ceil(GetEntityHeading(ped))) -- update rotation
		BeginTextCommandSetBlipName('STRING')
		AddTextComponentSubstringPlayerName(GetPlayerName(id))
		EndTextCommandSetBlipName(blip)
		SetBlipScale(blip, 0.85) -- set scale
		SetBlipAsShortRange(blip, true)

		table.insert(blipsCops, blip) -- add blip to array so we can remove it later
	end
end

function createBlipadmin(id)
	local ped = GetPlayerPed(id)
	local blip = GetBlipFromEntity(ped)
	
	if not DoesBlipExist(blip) then -- Add blip and create head display on player
		blip = AddBlipForEntity(ped)
		SetBlipSprite(blip, 58)
		SetBlipColour(blip, 46)
		ShowHeadingIndicatorOnBlip(blip, true) -- Player Blip indicator
		SetBlipRotation(blip, math.ceil(GetEntityHeading(ped))) -- update rotation
		BeginTextCommandSetBlipName('STRING')
		AddTextComponentSubstringPlayerName(GetPlayerName(id))
		EndTextCommandSetBlipName(blip)
		SetBlipScale(blip, 0.85) -- set scale
		SetBlipAsShortRange(blip, true)

		table.insert(blipsCops, blip) -- add blip to array so we can remove it later
	end
end



function createBlip5(id)
	local ped = GetPlayerPed(id)
	local blip = GetBlipFromEntity(ped)
	
	if not DoesBlipExist(blip) then -- Add blip and create head display on player
		blip = AddBlipForEntity(ped)
		SetBlipSprite(blip, 60)
		SetBlipColour(blip, 47)
		ShowHeadingIndicatorOnBlip(blip, true) -- Player Blip indicator
		SetBlipRotation(blip, math.ceil(GetEntityHeading(ped))) -- update rotation
		BeginTextCommandSetBlipName('STRING')
		AddTextComponentSubstringPlayerName(GetPlayerName(id))
		EndTextCommandSetBlipName(blip)
		SetBlipScale(blip, 0.85) -- set scale
		SetBlipAsShortRange(blip, true)

		table.insert(blipsCops, blip) -- add blip to array so we can remove it later
	end
end

function createBlip6(id)
	local ped = GetPlayerPed(id)
	local blip = GetBlipFromEntity(ped)
	
	if not DoesBlipExist(blip) then -- Add blip and create head display on player
		blip = AddBlipForEntity(ped)
		SetBlipSprite(blip, 198)
		SetBlipColour(blip, 5)
		ShowHeadingIndicatorOnBlip(blip, true) 
		SetBlipRotation(blip, math.ceil(GetEntityHeading(ped))) 
		BeginTextCommandSetBlipName('STRING')
		AddTextComponentSubstringPlayerName(GetPlayerName(id))
		EndTextCommandSetBlipName(blip)
		SetBlipScale(blipblip, 0.85) 
		SetBlipAsShortRange(blip, true)

		table.insert(blipsCops, blip) -- add blip to array so we can remove it later
	end
end




RegisterNetEvent('esx_mechanicjobnewblips:updateBlip')
AddEventHandler('esx_mechanicjobnewblips:updateBlip', function()

	-- Refresh all blips
	for k, existingBlip in pairs(blipsCops) do
		RemoveBlip(existingBlip)
	end

	-- Clean the blip table
	blipsCops = {}

	-- Enable blip?
	
	blipposition()

	-- Is the player a cop? In that case show all the blips for other cops
	--if ESX.PlayerData.job and ESX.PlayerData.job.name == 'ambulance' then
	if ESX.PlayerData.job.name == 'admin' then
		ESX.TriggerServerCallback('esx_society:getOnlinePlayers', function(players)
			for i=1, #players, 1 do
				if players[i].job.name == 'mechanic' or players[i].job.name == 'mec' then
					local id = GetPlayerFromServerId(players[i].source)
					if NetworkIsPlayerActive(id) and GetPlayerPed(id) ~= PlayerPedId() then
						createBlipmechanic(id)
					end
				elseif players[i].job.name == 'taxi'  then
					local id = GetPlayerFromServerId(players[i].source)
					if NetworkIsPlayerActive(id) and GetPlayerPed(id) ~= PlayerPedId() then
						createBlip6(id)
					end
				elseif players[i].job.name == 'police' then
					local id = GetPlayerFromServerId(players[i].source)
					if NetworkIsPlayerActive(id) and GetPlayerPed(id) ~= PlayerPedId() then
						createBlip5(id)
					end
				elseif players[i].job.name == 'ambulance' then
					local id = GetPlayerFromServerId(players[i].source)
					if NetworkIsPlayerActive(id) and GetPlayerPed(id) ~= PlayerPedId() then
						createBlipambulance(id)
					end
				elseif players[i].job.name == 'agent' then
					local id = GetPlayerFromServerId(players[i].source)
					if NetworkIsPlayerActive(id) and GetPlayerPed(id) ~= PlayerPedId() then
						createBlip4(id)
					end
				elseif players[i].job.name == 'admin' then
					local id = GetPlayerFromServerId(players[i].source)
					if NetworkIsPlayerActive(id) and GetPlayerPed(id) ~= PlayerPedId() then
						createBlipadmin(id)
					end
				else
					local id = GetPlayerFromServerId(players[i].source)
					if NetworkIsPlayerActive(id) and GetPlayerPed(id) ~= PlayerPedId() then
						createBlip3(id)
					end
				end
			end
		end)
	end

	

		ESX.TriggerServerCallback('esx_society:getOnlinePlayers', function(players)
			for i=1, #players, 1 do
				if players[i].job.name == 'mechanic' then
					local id = GetPlayerFromServerId(players[i].source)
					if NetworkIsPlayerActive(id) and GetPlayerPed(id) ~= PlayerPedId() then
						createBlip(id)
					end
				end
			end
		end)
		

		
	--end

end)

function blipposition()
	local blipmain = GetMainPlayerBlipId()
	local id = GetPlayerFromServerId(source)
	local ped = GetPlayerPed(id)
    if (ESX.PlayerData.job.name == 'mechanic' or ESX.PlayerData.job.name == 'mec') then
		
	SetBlipSprite(blipmain, 643)
	SetBlipColour(blipmain, 47)
	ShowHeadingIndicatorOnBlip(blip, true) 
	SetBlipRotation(blipmain, math.ceil(GetEntityHeading(ped))) 
	BeginTextCommandSetBlipName('STRING')
	AddTextComponentSubstringPlayerName(GetPlayerName(PlayerId()))
	EndTextCommandSetBlipName(blblipmainip)
	SetBlipScale(blipmain, 0.85) 
	SetBlipAsShortRange(blipmain, true)
	elseif ESX.PlayerData.job.name == 'ambulance' then
	SetBlipSprite(blipmain, 586)
	SetBlipColour(blipmain, 1)
	ShowHeadingIndicatorOnBlip(blipmain, true) 
	SetBlipRotation(blipmain, math.ceil(GetEntityHeading(ped))) 
	BeginTextCommandSetBlipName('STRING')
	AddTextComponentSubstringPlayerName(GetPlayerName(PlayerId()))
	EndTextCommandSetBlipName(blipmain)
	SetBlipScale(blipmain, 0.85) 
	SetBlipAsShortRange(blipmain, true)
   elseif ESX.PlayerData.job.name == 'admin' then
	SetBlipSprite(blipmain, 58)
	SetBlipColour(blipmain, 46)
	ShowHeadingIndicatorOnBlip(blipmain, true) 
	SetBlipRotation(blipmain, math.ceil(GetEntityHeading(ped))) 
	BeginTextCommandSetBlipName('STRING')
	AddTextComponentSubstringPlayerName(GetPlayerName(PlayerId()))
	EndTextCommandSetBlipName(blipmain)
	SetBlipScale(blipmain, 0.85) 
	SetBlipAsShortRange(blipmain, true)
	elseif ESX.PlayerData.job.name == 'taxi' then
	SetBlipSprite(blipmain, 198)
	SetBlipColour(blipmain, 5)
	ShowHeadingIndicatorOnBlip(blipmain, true) 
	SetBlipRotation(blipmain, math.ceil(GetEntityHeading(ped))) 
	BeginTextCommandSetBlipName('STRING')
	AddTextComponentSubstringPlayerName(GetPlayerName(PlayerId()))
	EndTextCommandSetBlipName(blipmain)
	SetBlipScale(blipmain, 0.85) 
	SetBlipAsShortRange(blipmain, true)
	elseif ESX.PlayerData.job.name == 'police' then
	elseif ESX.PlayerData.job.name == 'agent' then
	else
		SetBlipSprite(blipmain, 6)
		ShowHeadingIndicatorOnBlip(blipmain, true) 
		SetBlipRotation(blipmain, math.ceil(GetEntityHeading(ped))) 
		BeginTextCommandSetBlipName('STRING')
		AddTextComponentSubstringPlayerName(GetPlayerName(PlayerId()))
		EndTextCommandSetBlipName(blipmain)
		SetBlipScale(blipmain, 0.85) 
		SetBlipAsShortRange(blipmain, true)
    end
end




function OpenMechanicActionsMenu()
	local elements = {
		{label = _U('work_wear'),      value = 'cloakroom'},
		{label = _U('civ_wear'),       value = 'cloakroom2'}
		--{label = _U('deposit_stock'),  value = 'put_stock'}
	}

	-- if Config.EnablePlayerManagement and ESX.PlayerData.job and ESX.PlayerData.job.grade_name == 'boss' or ESX.PlayerData.job.grade_name == 'bosstwo' then
	-- 	--table.insert(elements, {label = _U('withdraw_stock'), value = 'get_stock'})
	-- 	table.insert(elements, {label = _U('boss_actions'), value = 'boss_actions'})		
	-- end

	ESX.UI.Menu.CloseAll()

	ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'mechanic_actions', {
		title    = _U('mechanic'),
		align    = 'top-left',
		elements = elements
	}, function(data, menu)
		if data.current.value == 'vehicle_list' then
			if Config.EnableSocietyOwnedVehicles then

				local elements = {}

				ESX.TriggerServerCallback('esx_society:getVehiclesInGarage', function(vehicles)
					for i=1, #vehicles, 1 do
						table.insert(elements, {
							label = GetDisplayNameFromVehicleModel(vehicles[i].model) .. ' [' .. vehicles[i].plate .. ']',
							value = vehicles[i]
						})
					end

					ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'vehicle_spawner', {
						title    = _U('service_vehicle'),
						align    = 'top-left',
						elements = elements
					}, function(data, menu)
						menu.close()
						local vehicleProps = data.current.value

						ESX.Game.SpawnVehicle(vehicleProps.model, Config.Zones.VehicleSpawnPoint.Pos, 270.0, function(vehicle)
							ESX.Game.SetVehicleProperties(vehicle, vehicleProps)
							local playerPed = PlayerPedId()
							TaskWarpPedIntoVehicle(playerPed,  vehicle,  -1)
						end)

						TriggerServerEvent('esx_society:removeVehicleFromGarage', 'mechanic', vehicleProps)
					end, function(data, menu)
						menu.close()
					end)
				end, 'mechanic')

			else

				local elements = {
					{label = _U('flat_bed'),  value = 'flatbed'},
					{label = _U('tow_truck'), value = 'towtruck2'}
				}

				if Config.EnablePlayerManagement and ESX.PlayerData.job and (ESX.PlayerData.job.grade_name == 'boss' or ESX.PlayerData.job.grade_name == 'chief' or ESX.PlayerData.job.grade_name == 'experimente') then
					table.insert(elements, {label = 'SlamVan', value = 'slamvan3'})
				end

				ESX.UI.Menu.CloseAll()

				ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'spawn_vehicle', {
					title    = _U('service_vehicle'),
					align    = 'top-left',
					elements = elements
				}, function(data, menu)
					if Config.MaxInService == -1 then
						ESX.Game.SpawnVehicle(data.current.value, Config.Zones.VehicleSpawnPoint.Pos, 90.0, function(vehicle)
							local playerPed = PlayerPedId()
							TaskWarpPedIntoVehicle(playerPed, vehicle, -1)
						local vehicleProps = ESX.Game.GetVehicleProperties(vehicle)
						TriggerServerEvent('esx_vehicleshop:setVehicleOwnedJob', vehicleProps, "mechanic")							
						end)
					else
						ESX.TriggerServerCallback('esx_service:enableService', function(canTakeService, maxInService, inServiceCount)
							ESX.Game.SpawnVehicle(data.current.value, Config.Zones.VehicleSpawnPoint.Pos, 90.0, function(vehicle)
								local playerPed = PlayerPedId()
								TaskWarpPedIntoVehicle(playerPed,  vehicle, -1)
							end)
						end, 'mechanic')
					end

					menu.close()
				end, function(data, menu)
					menu.close()
					OpenMechanicActionsMenu()
				end)

			end
		elseif data.current.value == 'cloakroom' then
			menu.close()
			TriggerServerEvent('esx_mecanojob:n89andzaed', 'zaed_mechanic')
			isOnDuty = true -- addon service
			ESX.ShowNotification(_U('service_in'))
			TriggerServerEvent('esx_mechanic:sendToAllPlayersNotficiton', ESX.PlayerData.job.grade_label)
			ESX.TriggerServerCallback('esx_skin:getPlayerSkin', function(skin, jobSkin)
				if skin.sex == 0 then
					TriggerEvent('skinchanger:loadClothes', skin, jobSkin.skin_male)
				else
					TriggerEvent('skinchanger:loadClothes', skin, jobSkin.skin_female)
				end
			end)
		elseif data.current.value == 'cloakroom2' then
			menu.close()
			TriggerServerEvent('esx_mecanojob:n89andzaed', 'na89_mechanic')
			isOnDuty = false -- addon service
			ESX.ShowNotification(_U('service_out'))
			ESX.TriggerServerCallback('esx_skin:getPlayerSkin', function(skin, jobSkin)
				TriggerEvent('skinchanger:loadSkin', skin)
			end)
		elseif data.current.value == 'put_stock' then
			OpenPutStocksMenu()
		elseif data.current.value == 'get_stock' then
			OpenGetStocksMenu()
		elseif data.current.value == 'boss_actions' then
			TriggerEvent('esx_society:openBossMenu', 'mechanic', function(data, menu)
				menu.close()
			end)
		end
	end, function(data, menu)
		menu.close()

		CurrentAction     = 'mechanic_actions_menu'
		CurrentActionMsg  = _U('open_actions')
		CurrentActionData = {}
	end)
end



function OpenMechanicHarvestMenu()
	local grade = ESX.PlayerData.job.grade_name

	if grade == 'mechanic5' or grade == 'mechanic6' or grade == 'mechanic7' or grade == 'mechanic8' or grade == 'mechanic9' or grade == 'bosstwo' or grade == 'boss' then
		local elements = {
			{label = _U('gas_can'), value = 'gaz_bottle'},
			{label = _U('repair_tools'), value = 'fix_tool'},
			{label = _U('body_work_tools'), value = 'caro_tool'}
		}

		ESX.UI.Menu.CloseAll()

		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'mechanic_harvest', {
			title    = _U('harvest'),
			align    = 'top-left',
			elements = elements
		}, function(data, menu)
			menu.close()

			if data.current.value == 'gaz_bottle' then
				TriggerServerEvent('esx_mecanojob:startHarvest')
			elseif data.current.value == 'fix_tool' then
				TriggerServerEvent('esx_mecanojob:startHarvest2')
			elseif data.current.value == 'caro_tool' then
				TriggerServerEvent('esx_mecanojob:startHarvest3')
			end
		end, function(data, menu)
			menu.close()
			CurrentAction     = 'mechanic_harvest_menu'
			CurrentActionMsg  = _U('harvest_menu')
			CurrentActionData = {}
		end)
	else
		ESX.ShowNotification(_U('not_experienced_enough'))
	end
end

function OpenMechanicCraftMenu()
	local grade = ESX.PlayerData.job.grade_name

	if grade == 'mechanic5' or grade == 'mechanic6' or grade == 'mechanic7' or grade == 'mechanic8' or grade == 'mechanic9' or grade == 'bosstwo' or grade == 'boss' then
		local elements = {
			{label = _U('repair_kit'), value = 'fix_kit'},
			{label = _U('body_kit'),   value = 'caro_kit'}
		}

		ESX.UI.Menu.CloseAll()

		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'mechanic_craft', {
			title    = _U('craft'),
			align    = 'top-left',
			elements = elements
		}, function(data, menu)
			menu.close()

			if data.current.value == 'blow_pipe' then
				TriggerServerEvent('esx_mecanojob:startCraft')
			elseif data.current.value == 'fix_kit' then
				TriggerServerEvent('esx_mecanojob:startCraft2')
			elseif data.current.value == 'caro_kit' then
				TriggerServerEvent('esx_mecanojob:startCraft3')
			end
		end, function(data, menu)
			menu.close()

			CurrentAction     = 'mechanic_craft_menu'
			CurrentActionMsg  = _U('craft_menu')
			CurrentActionData = {}
		end)
	else
		ESX.ShowNotification(_U('not_experienced_enough'))
	end
end

RegisterNetEvent('esx:getCounterPolice')
AddEventHandler('esx:getCounterPolice', function(mode)
	if mode == '+' then
		ESX.TriggerServerCallback('leojob:RefreshCounterPolice', function(counter_many)
			Config.counter_swap_police = counter_many
			TriggerServerEvent('esx_mecanojob:n89andzaed', 'zaed_police')
		end)
	elseif mode == '-' then
		ESX.TriggerServerCallback('leojob:RefreshCounterPolice', function(counter_many)
			Config.counter_swap_police = counter_many
			TriggerServerEvent('esx_mecanojob:n89andzaed', 'na89_police')
		end)
	end
end)
function OpenVehicleInfosMenu(vehicleData)

	ESX.TriggerServerCallback('esx_mecanojob:getVehicleInfos', function(retrivedInfo)

		local elements = {}

		table.insert(elements, {label = _U('plate', retrivedInfo.plate), value = nil})

		if retrivedInfo.owner == nil then
			table.insert(elements, {label = _U('owner_unknown'), value = nil})
		else
			table.insert(elements, {label = _U('owner', retrivedInfo.owner), value = nil})
		end

		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'vehicle_infos',
		{
			title    = 'معلومات المركبة',
			align    = 'top-left',
			elements = elements
		}, nil, function(data, menu)
			menu.close()
		end)

	end, vehicleData.plate)

end

function OpenMobileMechanicActionsMenu()
	ESX.UI.Menu.CloseAll()

	ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'mobile_mechanic_actions', {
		title    = _U('mechanic'),
		align    = 'top-left',
		elements = {
			{label = 'تغيير المسمى الميداني', value = 'change_name' },
			{label = _U('billing'),       value = 'billing'},
			{label = _U('vehicle_interaction'),	value = 'vehicle_interaction'},			
			{label = _U('hijack'),        value = 'hijack_vehicle'},
			{label = _U('repair'),        value = 'fix_vehicle'},
			{label = 'سمكرة',        		value = 'caro_vehicle'},			
			{label = _U('clean'),         value = 'clean_vehicle'},
			{label = _U('imp_veh'),       value = 'del_vehicle'},
			{label = _U('flat_bed'),      value = 'dep_vehicle'},
			{label = 'أستعلم عن عدد حجوزاتك',      value = 'poundnum'},
			{label = _U('place_objects'), value = 'object_spawner'},
			{label = _U('extra'), 			value = 'OpenVehicleExtrasMenu'},
---------------------------------------add by marrooky------------------------
           --{label = ' تغير الوحة المركبة', 			value = 'palet'}
------------------------------------------------------------------------------			
	}}, function(data, menu)
		if isBusy then return end


		if data.current.value == 'vehicle_interaction' then
			local elements  = {}
			local playerPed = PlayerPedId()
			local coords    = GetEntityCoords(playerPed)
			local vehicle   = ESX.Game.GetVehicleInDirection()
			local class = GetVehicleClass(vehicle)
			
			if DoesEntityExist(vehicle) then
				if class ~= 18 then
					local vehicleData = ESX.Game.GetVehicleProperties(vehicle)
					OpenVehicleInfosMenu(vehicleData)
					--ESX.ShowNotification('ﺔﺑﺎﻗﺮﻟﺍ ﻯﺪﻟ ﻡﻼﻌﺘﺳﻻﺍ ﻖﻴﺛﻮﺗ ﻢﺘﻳ')
				else
					ESX.ShowNotification('لايمكن الإستعمال')
				end
			else
				ESX.ShowNotification('لاتوجد مركبة قريبه منك')
			end
		elseif data.current.value == 'change_name' then 
			ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'getName', {
				title = "ضع مسماك المدياني الجديد"
			}, function(data10, menu10)
				if data10.value then 
					ESX.ShowNotification("تم تغيير مسماك الميداني إلى: "..data10.value)
					ExecuteCommand('nameinradio '..data10.value)
					menu10.close()
				else
					ESX.ShowNotification("قم بوضع قيمة صحيحة")
					menu10.close()
				end
				
			end, function(menu10, data10)
				menu10.close()
			end)
		elseif data.current.value == 'poundnum' then
			ESX.TriggerServerCallback('esx_mecanojob:canMechanicImpoundd', function(canImpound)
			
			end)
			
		elseif data.current.value == 'billing' then
			ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'billing', {
				title = _U('invoice_amount')
			}, function(data, menu)
				local amount = tonumber(data.value)

				if amount == nil or amount < 0 then
					ESX.ShowNotification(_U('amount_invalid'))
				else
					local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
					if closestPlayer == -1 or closestDistance > 3.0 then
						ESX.ShowNotification(_U('no_players_nearby'))
					else
						menu.close()
						billPass = 'a82mKba0bma2'
						TriggerServerEvent('esx_billing:sendKBill_28vn2', billPass, GetPlayerServerId(closestPlayer), 'society_mechanic', _U('mechanic'), amount)
					end
				end
			end, function(data, menu)
				menu.close()
			end)
		elseif data.current.value == 'hijack_vehicle' then
			local playerPed = PlayerPedId()
			local vehicle = ESX.Game.GetVehicleInDirection()
			local coords = GetEntityCoords(playerPed)

			if IsPedSittingInAnyVehicle(playerPed) then
				ESX.ShowNotification(_U('inside_vehicle'))
				return
			end

			if DoesEntityExist(vehicle) then
				isBusy = true
				TaskStartScenarioInPlace(playerPed, 'WORLD_HUMAN_WELDING', 0, true)
				Citizen.CreateThread(function()
					Citizen.Wait(10000)

					SetVehicleDoorsLocked(vehicle, 1)
					SetVehicleDoorsLockedForAllPlayers(vehicle, false)
					if not IsPedInAnyVehicle(playerPed) then ClearPedTasksImmediately(playerPed) end

					ESX.ShowNotification(_U('vehicle_unlocked'))
					isBusy = false
				end)
			else
				ESX.ShowNotification(_U('no_vehicle_nearby'))
			end
	elseif data.current.value == 'fix_vehicle' then
		DoAction('fixkit',15000)
		ESX.UI.Menu.CloseAll()
	elseif data.current.value == 'caro_vehicle' then
		DoAction('carokit',25000)
		ESX.UI.Menu.CloseAll()
		elseif data.current.value == 'clean_vehicle' then
			local playerPed = PlayerPedId()
			local vehicle   = ESX.Game.GetVehicleInDirection()
			local coords    = GetEntityCoords(playerPed)

			if IsPedSittingInAnyVehicle(playerPed) then
				ESX.ShowNotification(_U('inside_vehicle'))
				return
			end

			if DoesEntityExist(vehicle) then
				isBusy = true
				TaskStartScenarioInPlace(playerPed, 'WORLD_HUMAN_MAID_CLEAN', 0, true)
				Citizen.CreateThread(function()
					Citizen.Wait(10000)

					SetVehicleDirtLevel(vehicle, 0)
					if not IsPedInAnyVehicle(playerPed) then ClearPedTasksImmediately(playerPed) end
					TriggerServerEvent('esx_mecanojob:checkIsSetXpLevelJobsWhiteList2')
					isBusy = false
				end)
			else
				ESX.ShowNotification(_U('no_vehicle_nearby'))
			end
		elseif data.current.value == 'del_vehicle' then
		DoActionImpound(10000)
		
		------------------------------add by marroky --------------------------------
		elseif data.current.value == 'palet' then
		local playerPed = PlayerPedId()
		ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'billing', {
				title = 'اختر ٦ احرف للوحة الجديدة'
			}, function(data, menu)
				local palet = data.value

				if palet == nil or string.len(palet) > 6 then
					ESX.ShowNotification('احرف للوحة الجديدة')
				else
					
					if IsPedSittingInAnyVehicle(playerPed) == false then
						ESX.ShowNotification('يجب ان تكون داخل المركبة')
					else
						menu.close()
						local vehicle = GetVehiclePedIsIn(playerPed, false)
						local oldplate = ESX.Game.GetVehicleProperties(vehicle).plate
						TriggerServerEvent('ev:setPlate',palet, oldplate)
					end
				end
			end, function(data, menu)
				menu.close()
			end)
		-----------------------------------------------------------------------------
		
		--[[local playerPed = PlayerPedId()

		if IsPedSittingInAnyVehicle(playerPed) then
			local vehicle = GetVehiclePedIsIn(playerPed, false)

			if GetPedInVehicleSeat(vehicle, -1) == playerPed then
				--ESX.ShowNotification(_U('vehicle_impounded'))
				TriggerEvent('esx_mecanojob:jobNotifyGeneral', _U('vehicle_impounded'), 'success', 7000, true)
				ESX.Game.DeleteVehicle(vehicle)
				if Config.EnableJobLogs == true then
					TriggerServerEvent('esx_joblogs:AddInLog', "mecano", "del_vehicle", GetPlayerName(PlayerId()))
				end
			else
				--ESX.ShowNotification(_U('must_seat_driver'))
				TriggerEvent('esx_mecanojob:jobNotifyGeneral', _U('must_seat_driver'), 'error', 7000, true)
			end
		else
			local vehicle = ESX.Game.GetVehicleInDirection()

			if DoesEntityExist(vehicle) then
				--ESX.ShowNotification(_U('vehicle_impounded'))
				TriggerEvent('esx_mecanojob:jobNotifyGeneral', _U('vehicle_impounded'), 'success', 7000, true)
				ESX.Game.DeleteVehicle(vehicle)
				if Config.EnableJobLogs == true then
					TriggerServerEvent('esx_joblogs:AddInLog', "mecano", "del_vehicle", GetPlayerName(PlayerId()))
				end
			else
				--ESX.ShowNotification(_U('must_near'))
				TriggerEvent('esx_mecanojob:jobNotifyGeneral', _U('must_near'), 'error', 7000, true)
			end
		end]]
	elseif data.current.value == 'dep_vehicle' then
		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'flatbedmenusd', {
			title    = ('قائمة السطحه'),
			align    = 'top-left',
			elements = {
				{label = ('أنزال السطحه'), 		value = 'deployramp'},
				{label = ('رفع السطحه'),  		value = 'ramprm'},
				{label = ('ربط السياره علي السطحه'),		value = 'attach'},
				{label = ('فك السياره من علي السطحه'),	value = 'detach'},
			}
		}, function(data2, menu2)
			
	

			if data2.current.value == 'deployramp' then
				TriggerEvent('deployramp')
			elseif data2.current.value == 'ramprm' then
				TriggerEvent('ramprm')
			elseif data2.current.value == 'attach' then
				TriggerEvent('attach')
			elseif data2.current.value == 'detach' then
				TriggerEvent('detach')
			end


			end, function(data2, menu2)
				menu2.close()
			end)

	elseif data.current.value == 'OpenVehicleExtrasMenu' then
		OpenVehicleExtrasMenu()			
		ESX.ShowNotification('<font color=red>يمنع</font> <font color=orange>إستخدام الاكسترا خارج كراج الميكانيكي</font>')
		elseif data.current.value == 'object_spawner' then
			local playerPed = PlayerPedId()

			if IsPedSittingInAnyVehicle(playerPed) then
				ESX.ShowNotification(_U('inside_vehicle'))
				return
			end

		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'mobile_mechanic_actions_spawn', {
			title    = _U('objects'),
			align    = 'top-left',
			elements = {
				{label = _U('roadcone'), 		value = 'prop_roadcone02a'},
				--{label = _U('toolbox'),  		value = 'prop_toolchest_01'},
				--{label = _U('light_high'),		value = 'prop_worklight_01a'},
				--{label = _U('barrier_arrow'),	value = 'prop_mp_arrow_barrier_01'},
			}
		}, function(data2, menu2)
			local model   = data2.current.value
			local coords  = GetEntityCoords(playerPed)
			local forward = GetEntityForwardVector(playerPed)
			local x, y, z = table.unpack(coords + forward * 1.0)

			if model == 'prop_roadcone02a' then
				z = z --  - 2.0
			elseif model == 'prop_toolchest_01' then
				z = z --  - 2.0
			end

			ESX.Game.SpawnObject(model, {
				x = x,
				y = y,
				z = z
			}, function(obj)
				SetEntityHeading(obj, GetEntityHeading(playerPed))
				PlaceObjectOnGroundProperly(obj)
			end)

			end, function(data2, menu2)
				menu2.close()
			end)
		end
	end, function(data, menu)
		menu.close()
	end)
end

RegisterNetEvent('esx_swap:tofunction')
AddEventHandler('esx_swap:tofunction', function()
	DoActionImpound(10000)
end)

function DoActionImpound(waitime)
	_disableAllControlActions = true
	local playerPed = PlayerPedId()
	local coords    = GetEntityCoords(playerPed)
	local found = false
	local NN = nil
	for locationNumber,data in pairs(Config.impound.location) do
		found = true
		NN = locationNumber
	end
	Citizen.CreateThread(function()
		while _disableAllControlActions do
			Citizen.Wait(0)
			DisableAllControlActions(0) --disable all control (comment it if you want to detect how many time key pressed)
		end
	end)
	
	if IsPedSittingInAnyVehicle(playerPed) then
		ESX.ShowNotification(_U('inside_vehicle'))
		return
	end
	
	if IsAnyVehicleNearPoint(coords.x, coords.y, coords.z, 5.0) then
		local vehicle = nil
	
		vehicle = GetClosestVehicle(coords.x, coords.y, coords.z, 10.0, 0, 71)
		--vehicle = ESX.Game.GetVehicleInDirection()
		
		if DoesEntityExist(vehicle) then
			--------------------------
			ESX.TriggerServerCallback('esx_mecanojob:canMechanicImpound', function(canImpound)
				if canImpound then					
					TriggerEvent('pogressBar:drawBar', waitime, '<font size=5>حجز المركبة')
					Citizen.Wait(1500)
					TriggerServerEvent('esx_misc3:try2', ESX.Game.GetVehicleProperties(vehicle).plate)
					TaskStartScenarioInPlace(playerPed, 'CODE_HUMAN_MEDIC_TEND_TO_DEAD', 0, true)
					Citizen.CreateThread(function()
						Citizen.Wait(waitime-3000)
						--ESX.Game.DeleteVehicle(vehicle)
						AdvancedOnesyncDeleteVehicle(vehicle)
						if not IsPedInAnyVehicle(playerPed) then ClearPedTasksImmediately(playerPed) end
						Citizen.Wait(1500)
						_disableAllControlActions = false
						--if Config.EnableJobLogs == true then
							--TriggerServerEvent('esx_joblogs:AddInLog', "mecano", "del_vehicle", GetPlayerName(PlayerId()))
						--end
					end)
				else
					_disableAllControlActions = false
				end
			end,locationNumber)							
		else
			ESX.ShowNotification(_U('no_vehicle_nearby'))
			_disableAllControlActions = false
		end	
	else
		ESX.ShowNotification(_U('no_vehicle_nearby'))
		_disableAllControlActions = false
	end
end

function AdvancedOnesyncDeleteVehicle(vehicle)
	ESX.Game.DeleteVehicle(vehicle)
	
	local entity = vehicle
    carModel = GetEntityModel(entity)
    carName = GetDisplayNameFromVehicleModel(carModel)
    NetworkRequestControlOfEntity(entity)
    
    local timeout = 2000
    while timeout > 0 and not NetworkHasControlOfEntity(entity) do
        Wait(100)
        timeout = timeout - 100
    end

    SetEntityAsMissionEntity(entity, true, true)
    
    local timeout = 2000
    while timeout > 0 and not IsEntityAMissionEntity(entity) do
        Wait(100)
        timeout = timeout - 100
    end

    Citizen.InvokeNative( 0xEA386986E786A54F, Citizen.PointerValueIntInitialized( entity ) )
    
    if (DoesEntityExist(entity)) then 
        DeleteEntity(entity)
    end 
end

function DoAction(item,waitime)
	_disableAllControlActions = true
				
	Citizen.CreateThread(function()
		while _disableAllControlActions do
			Citizen.Wait(0)
			DisableAllControlActions(0) --disable all control (comment it if you want to detect how many time key pressed)
		end
	end)
	
	local playerPed = PlayerPedId()
	local coords    = GetEntityCoords(playerPed)
	
	if IsPedSittingInAnyVehicle(playerPed) then
		ESX.ShowNotification(_U('inside_vehicle'))
		return
	end
	
	if IsAnyVehicleNearPoint(coords.x, coords.y, coords.z, 5.0) then
		local vehicle = nil
	
		vehicle = GetClosestVehicle(coords.x, coords.y, coords.z, 10.0, 0, 71)
		--vehicle = ESX.Game.GetVehicleInDirection()
		
		if DoesEntityExist(vehicle) then
			--------------------------
			--ESX.TriggerServerCallback('esx_mecanojob:canMechanicDoAction', function(canRepaire)
				--if canRepaire then
				if Cooldown_count == 0 then
				Cooldown(60)
					exports['progressBars']:startUI(waitime, 'تصليح المركبة')
					SetVehicleDoorOpen(vehicle, 4, false)
					Citizen.Wait(1500)
					if item == 'fixkit' then
						TaskStartScenarioInPlace(playerPed, 'PROP_HUMAN_BUM_BIN', 0, true)
					elseif item == 'carokit' then	
						TaskStartScenarioInPlace(playerPed, 'WORLD_HUMAN_HAMMERING', 0, true)
					end	
					Citizen.CreateThread(function()
						Citizen.Wait(waitime-3000)
						SetVehicleFixed(vehicle)
						SetVehicleDeformationFixed(vehicle)
						SetVehicleUndriveable(vehicle, false)
						if not IsPedInAnyVehicle(playerPed) then ClearPedTasksImmediately(playerPed) end
						Citizen.Wait(1500)
						SetVehicleDoorShut(vehicle, 4, false)
						if item == 'carokit' then
							TriggerServerEvent('esx_mecanojob:checkIsSetXpLevelJobsWhiteList3')
						elseif item == 'fixkit' then
							TriggerServerEvent('esx_mecanojob:checkIsSetXpLevelJobsWhiteList')
						end
						_disableAllControlActions = false
						
					end)
				else
					_disableAllControlActions = false
					ESX.ShowNotification('<font color=red>يجب الأنتظار</font>. <font color=orange>'..Cooldown_count..' ثانية')
				end
			--end,item)
		else
			ESX.ShowNotification(_U('no_vehicle_nearby'))
			_disableAllControlActions = false
		end	
	else
		ESX.ShowNotification(_U('no_vehicle_nearby'))
		_disableAllControlActions = false
	end		
end

function OpenGetStocksMenu()
	ESX.TriggerServerCallback('esx_mecanojob:getStockItems', function(items)
		local elements = {}

		for i=1, #items, 1 do
			table.insert(elements, {
				label = 'x' .. items[i].count .. ' ' .. items[i].label,
				value = items[i].name
			})
		end

		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'stocks_menu', {
			title    = _U('mechanic_stock'),
			align    = 'top-left',
			elements = elements
		}, function(data, menu)
			local itemName = data.current.value

			ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'stocks_menu_get_item_count', {
				title = _U('quantity')
			}, function(data2, menu2)
				local count = tonumber(data2.value)

				if count == nil then
					ESX.ShowNotification(_U('invalid_quantity'))
				else
					menu2.close()
					menu.close()
					TriggerServerEvent('esx_mecanojob:getStockItem', itemName, count)

					Citizen.Wait(1000)
					OpenGetStocksMenu()
				end
			end, function(data2, menu2)
				menu2.close()
			end)
		end, function(data, menu)
			menu.close()
		end)
	end)
end

function OpenPutStocksMenu()
	ESX.TriggerServerCallback('esx_mecanojob:getPlayerInventory', function(inventory)
		local elements = {}

		for i=1, #inventory.items, 1 do
			local item = inventory.items[i]

			if item.count > 0 then
				table.insert(elements, {
					label = item.label .. ' x' .. item.count,
					type  = 'item_standard',
					value = item.name
				})
			end
		end

		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'stocks_menu', {
			title    = _U('inventory'),
			align    = 'top-left',
			elements = elements
		}, function(data, menu)
			local itemName = data.current.value

			ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'stocks_menu_put_item_count', {
				title = _U('quantity')
			}, function(data2, menu2)
				local count = tonumber(data2.value)

				if count == nil then
					ESX.ShowNotification(_U('invalid_quantity'))
				else
					menu2.close()
					menu.close()
					TriggerServerEvent('esx_mecanojob:putStockItems', itemName, count)

					Citizen.Wait(1000)
					OpenPutStocksMenu()
				end
			end, function(data2, menu2)
				menu2.close()
			end)
		end, function(data, menu)
			menu.close()
		end)
	end)
end

RegisterNetEvent('esx_mecanojob:onHijack')
AddEventHandler('esx_mecanojob:onHijack', function()
	local playerPed = PlayerPedId()
	local coords = GetEntityCoords(playerPed)

	if IsAnyVehicleNearPoint(coords.x, coords.y, coords.z, 5.0) then
		local vehicle

		if IsPedInAnyVehicle(playerPed, false) then
			vehicle = GetVehiclePedIsIn(playerPed, false)
		else
			vehicle = GetClosestVehicle(coords.x, coords.y, coords.z, 5.0, 0, 71)
		end

		local chance = math.random(100)
		local alarm  = math.random(100)

		if DoesEntityExist(vehicle) then
			if alarm <= 33 then
				SetVehicleAlarm(vehicle, true)
				StartVehicleAlarm(vehicle)
			end

			TaskStartScenarioInPlace(playerPed, 'WORLD_HUMAN_WELDING', 0, true)

			Citizen.CreateThread(function()
				Citizen.Wait(10000)
				if chance <= 66 then
					SetVehicleDoorsLocked(vehicle, 1)
					SetVehicleDoorsLockedForAllPlayers(vehicle, false)
					if not IsPedInAnyVehicle(playerPed) then ClearPedTasksImmediately(playerPed) end
					ESX.ShowNotification(_U('veh_unlocked'))
				else
					ESX.ShowNotification(_U('hijack_failed'))
					if not IsPedInAnyVehicle(playerPed) then ClearPedTasksImmediately(playerPed) end
				end
			end)
		end
	end
end)

RegisterNetEvent('esx_mecanojob:onCarokit')
AddEventHandler('esx_mecanojob:onCarokit', function()
	local playerPed = PlayerPedId()
	local coords = GetEntityCoords(playerPed)

	if IsAnyVehicleNearPoint(coords.x, coords.y, coords.z, 5.0) then
		local vehicle

		if IsPedInAnyVehicle(playerPed, false) then
			vehicle = GetVehiclePedIsIn(playerPed, false)
		else
			vehicle = GetClosestVehicle(coords.x, coords.y, coords.z, 5.0, 0, 71)
		end

		if DoesEntityExist(vehicle) then
			TaskStartScenarioInPlace(playerPed, 'WORLD_HUMAN_HAMMERING', 0, true)
			Citizen.CreateThread(function()
				Citizen.Wait(10000)
				SetVehicleFixed(vehicle)
				SetVehicleDeformationFixed(vehicle)
				if not IsPedInAnyVehicle(playerPed) then ClearPedTasksImmediately(playerPed) end
				ESX.ShowNotification(_U('body_repaired'))
			end)
		end
	end
end)

RegisterNetEvent('esx_mecanojob:onFixkit')
AddEventHandler('esx_mecanojob:onFixkit', function()
	local playerPed = PlayerPedId()
	local coords = GetEntityCoords(playerPed)

	if IsAnyVehicleNearPoint(coords.x, coords.y, coords.z, 5.0) then
		local vehicle

		if IsPedInAnyVehicle(playerPed, false) then
			vehicle = GetVehiclePedIsIn(playerPed, false)
		else
			vehicle = GetClosestVehicle(coords.x, coords.y, coords.z, 5.0, 0, 71)
		end

		if DoesEntityExist(vehicle) then
			TaskStartScenarioInPlace(playerPed, 'PROP_HUMAN_BUM_BIN', 0, true)
			Citizen.CreateThread(function()
				Citizen.Wait(20000)
				SetVehicleFixed(vehicle)
				SetVehicleDeformationFixed(vehicle)
				SetVehicleUndriveable(vehicle, false)
				if not IsPedInAnyVehicle(playerPed) then ClearPedTasksImmediately(playerPed) end
				ESX.ShowNotification(_U('veh_repaired'))
			end)
		end
	end
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
	ESX.PlayerData = xPlayer
	ESX.PlayerLoaded = true
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	ESX.PlayerData.job = job
	
	isOnDuty = false
	Citizen.Wait(5000)
	TriggerServerEvent('esx_mecanojob:forceBlip')
end)

AddEventHandler('esx_mecanojob:hasEnteredMarker', function(zone)
	if zone == 'NPCJobTargetTowable' then

	elseif zone =='VehicleDelivery' then
		NPCTargetDeleterZone = true
	elseif zone == 'MechanicActions' or zone == 'MechanicActionsTwo' then
		CurrentAction     = 'mechanic_actions_menu'
		CurrentActionMsg  = _U('open_actions')
		CurrentActionData = {}
	elseif zone == 'Garage' then
		CurrentAction     = 'mechanic_harvest_menu'
		CurrentActionMsg  = _U('harvest_menu')
		CurrentActionData = {}
	elseif zone == 'Craft' or zone == 'CraftTwo' or zone == 'CraftThree' or zone == 'CraftFour' or zone == 'CraftFive' then
		CurrentAction     = 'mechanic_craft_menu'
		CurrentActionMsg  = _U('craft_menu')
		CurrentActionData = {}
	elseif zone == 'VehicleDeleter' then
		local playerPed = PlayerPedId()

		if IsPedInAnyVehicle(playerPed, false) then
			local vehicle = GetVehiclePedIsIn(playerPed,  false)

			CurrentAction     = 'delete_vehicle'
			CurrentActionMsg  = _U('veh_stored')
			CurrentActionData = {vehicle = vehicle}
		end
	end
end)

AddEventHandler('esx_mecanojob:hasExitedMarker', function(zone)
	if zone =='VehicleDelivery' then
		NPCTargetDeleterZone = false
	elseif zone == 'Craft' then
		TriggerServerEvent('esx_mecanojob:stopCraft')
		TriggerServerEvent('esx_mecanojob:stopCraft2')
		TriggerServerEvent('esx_mecanojob:stopCraft3')
	elseif zone == 'Garage' then
		TriggerServerEvent('esx_mecanojob:stopHarvest')
		TriggerServerEvent('esx_mecanojob:stopHarvest2')
		TriggerServerEvent('esx_mecanojob:stopHarvest3')
	end

	CurrentAction = nil
	ESX.UI.Menu.CloseAll()
end)

AddEventHandler('esx_mecanojob:hasEnteredEntityZone', function(entity)
	local playerPed = PlayerPedId()

	if ESX.PlayerData.job and (ESX.PlayerData.job.name == 'mechanic' or ESX.PlayerData.job.name == 'mec') and not IsPedInAnyVehicle(playerPed, false) then
		CurrentAction     = 'remove_entity'
		CurrentActionMsg  = _U('press_remove_obj')
		CurrentActionData = {entity = entity}
	end
end)

AddEventHandler('esx_mecanojob:hasExitedEntityZone', function(entity)
	if CurrentAction == 'remove_entity' then
		CurrentAction = nil
	end
end)

RegisterNetEvent('esx_phone:loaded')
AddEventHandler('esx_phone:loaded', function(phoneNumber, contacts)
	local specialContact = {
		name       = _U('mechanic'),
		number     = 'mechanic',
		base64Icon = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEwAACxMBAJqcGAAAA4BJREFUWIXtll9oU3cUx7/nJA02aSSlFouWMnXVB0ejU3wcRteHjv1puoc9rA978cUi2IqgRYWIZkMwrahUGfgkFMEZUdg6C+u21z1o3fbgqigVi7NzUtNcmsac40Npltz7S3rvUHzxQODec87vfD+/e0/O/QFv7Q0beV3QeXqmgV74/7H7fZJvuLwv8q/Xeux1gUrNBpN/nmtavdaqDqBK8VT2RDyV2VHmF1lvLERSBtCVynzYmcp+A9WqT9kcVKX4gHUehF0CEVY+1jYTTIwvt7YSIQnCTvsSUYz6gX5uDt7MP7KOKuQAgxmqQ+neUA+I1B1AiXi5X6ZAvKrabirmVYFwAMRT2RMg7F9SyKspvk73hfrtbkMPyIhA5FVqi0iBiEZMMQdAui/8E4GPv0oAJkpc6Q3+6goAAGpWBxNQmTLFmgL3jSJNgQdGv4pMts2EKm7ICJB/aG0xNdz74VEk13UYCx1/twPR8JjDT8wttyLZtkoAxSb8ZDCz0gdfKxWkFURf2v9qTYH7SK7rQIDn0P3nA0ehixvfwZwE0X9vBE/mW8piohhl1WH18UQBhYnre8N/L8b8xQvlx4ACbB4NnzaeRYDnKm0EALCMLXy84hwuTCXL/ExoB1E7qcK/8NCLIq5HcTT0i6u8TYbXUM1cAyyveVq8Xls7XhYrvY/4n3gC8C+dsmAzL1YUiyfWxvHzsy/w/dNd+KjhW2yvv/RfXr7x9QDcmo1he2RBiCCI1Q8jVj9szPNixVfgz+UiIGyDSrcoRu2J16d3I6e1VYvNSQjXpnucAcEPUOkGYZs/l4uUhowt/3kqu1UIv9n90fAY9jT3YBlbRvFTD4fw++wHjhiTRL/bG75t0jI2ITcHb5om4Xgmhv57xpGOg3d/NIqryOR7z+r+MC6qBJB/ZB2t9Om1D5lFm843G/3E3HI7Yh1xDRAfzLQr5EClBf/HBHK462TG2J0OABXeyWDPZ8VqxmBWYscpyghwtTd4EKpDTjCZdCNmzFM9k+4LHXIFACJN94Z6FiFEpKDQw9HndWsEuhnADVMharoYJBp9XrcGQKJ4qFE9k+6r2+MG3k5N8VQ22TVglbX2ZwOzX2VvNKr91zmY6S7N6zqZicVT2WNLyVSehESaBhxnOALfMeYX+K/S2yv7wmMAlvwyuR7FxQUyf0fgc/jztfkJr7XeGgC8BJJgWNV8ImT+AAAAAElFTkSuQmCC'
	}

	TriggerEvent('esx_phone:addSpecialContact', specialContact.name, specialContact.number, specialContact.base64Icon)
end)

-- Pop NPC mission vehicle when inside area
Citizen.CreateThread(function()
	while true do
		Citizen.Wait(10)

		if NPCTargetTowableZone and not NPCHasSpawnedTowable then
			local coords = GetEntityCoords(PlayerPedId())
			local zone   = Config.Zones[NPCTargetTowableZone]

			if GetDistanceBetweenCoords(coords, zone.Pos.x, zone.Pos.y, zone.Pos.z, true) < Config.NPCSpawnDistance then
				local model = Config.Vehicles[GetRandomIntInRange(1,  #Config.Vehicles)]

				ESX.Game.SpawnVehicle(model, zone.Pos, 0, function(vehicle)
					NPCTargetTowable = vehicle
				end)

				NPCHasSpawnedTowable = true
			end
		end

		if NPCTargetTowableZone and NPCHasSpawnedTowable and not NPCHasBeenNextToTowable then
			local coords = GetEntityCoords(PlayerPedId())
			local zone   = Config.Zones[NPCTargetTowableZone]

			if GetDistanceBetweenCoords(coords, zone.Pos.x, zone.Pos.y, zone.Pos.z, true) < Config.NPCNextToDistance then
				ESX.ShowNotification(_U('please_tow'))
				NPCHasBeenNextToTowable = true
			end
		end
	end
end)

-- Create Blips
Citizen.CreateThread(function()
	local blip = AddBlipForCoord(Config.Zones.MechanicActions.Pos.x, Config.Zones.MechanicActions.Pos.y, Config.Zones.MechanicActions.Pos.z)

	SetBlipSprite (blip, 446)
	SetBlipDisplay(blip, 4)
	SetBlipScale  (blip, 1.0)
	SetBlipColour (blip, 5)
	SetBlipAsShortRange(blip, true)

	BeginTextCommandSetBlipName('STRING')
	AddTextComponentSubstringPlayerName(_U('blip_mechanic'))
	EndTextCommandSetBlipName(blip)
	
	
	if Config.Zones.MechanicActionsTwo then
		local blip2 = AddBlipForCoord(Config.Zones.MechanicActionsTwo.Pos.x, Config.Zones.MechanicActionsTwo.Pos.y, Config.Zones.MechanicActionsTwo.Pos.z)

		SetBlipSprite (blip2, 446)
		SetBlipDisplay(blip2, 4)
		SetBlipScale  (blip2, 1.0)
		SetBlipColour (blip2, 5)
		SetBlipAsShortRange(blip2, true)

		BeginTextCommandSetBlipName('STRING')
		AddTextComponentSubstringPlayerName(_U('blip_mechanic'))
		EndTextCommandSetBlipName(blip2)
	end
end)

-- Display markers
Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)

		if ESX.PlayerData.job and (ESX.PlayerData.job.name == 'mechanic' or ESX.PlayerData.job.name == 'mec') then
			local coords, letSleep = GetEntityCoords(PlayerPedId()), true

			for k,v in pairs(Config.Zones) do
				if v.Type ~= -1 and GetDistanceBetweenCoords(coords, v.Pos.x, v.Pos.y, v.Pos.z, true) < Config.DrawDistance then
					DrawMarker(v.Type, v.Pos.x, v.Pos.y, v.Pos.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, v.Size.x, v.Size.y, v.Size.z, v.Color.r, v.Color.g, v.Color.b, 100, false, true, 2, false, nil, nil, false)
					letSleep = false
				end
			end

			if letSleep then
				Citizen.Wait(500)
			end
		else
			Citizen.Wait(500)
		end
	end
end)

-- Enter / Exit marker events
Citizen.CreateThread(function()
	while true do
		Citizen.Wait(10)

		if ESX.PlayerData.job and (ESX.PlayerData.job.name == 'mechanic' or ESX.PlayerData.job.name == 'mec') then

			local coords = GetEntityCoords(PlayerPedId())
			local isInMarker = false
			local currentZone = nil

			for k,v in pairs(Config.Zones) do
				if(GetDistanceBetweenCoords(coords, v.Pos.x, v.Pos.y, v.Pos.z, true) < v.Size.x) then
					isInMarker  = true
					currentZone = k
				end
			end

			if (isInMarker and not HasAlreadyEnteredMarker) or (isInMarker and LastZone ~= currentZone) then
				HasAlreadyEnteredMarker = true
				LastZone                = currentZone
				TriggerEvent('esx_mecanojob:hasEnteredMarker', currentZone)
			end

			if not isInMarker and HasAlreadyEnteredMarker then
				HasAlreadyEnteredMarker = false
				TriggerEvent('esx_mecanojob:hasExitedMarker', LastZone)
			end

		end
	end
end)

Citizen.CreateThread(function()
	local trackedEntities = {
		'prop_roadcone02a',
		--'prop_toolchest_01',
		--'prop_worklight_01a',
		--'prop_mp_arrow_barrier_01',
	}

	while true do
		Citizen.Wait(500)
		if ESX.PlayerData.job and (ESX.PlayerData.job.name == 'mechanic' or ESX.PlayerData.job.name == 'mec') then
			local playerPed = PlayerPedId()
			local coords    = GetEntityCoords(playerPed)
	
			local closestDistance = -1
			local closestEntity   = nil
	
			for i=1, #trackedEntities, 1 do
				local object = GetClosestObjectOfType(coords, 3.0, GetHashKey(trackedEntities[i]), false, false, false)
	
				if DoesEntityExist(object) then
					local objCoords = GetEntityCoords(object)
					local distance  = GetDistanceBetweenCoords(coords, objCoords, true)
	
					if closestDistance == -1 or closestDistance > distance then
						closestDistance = distance
						closestEntity   = object
					end
				end
			end
	
			if closestDistance ~= -1 and closestDistance <= 3.0 then
				if LastEntity ~= closestEntity then
					TriggerEvent('esx_mecanojob:hasEnteredEntityZone', closestEntity)
					LastEntity = closestEntity
				end
			else
				if LastEntity then
					TriggerEvent('esx_mecanojob:hasExitedEntityZone', LastEntity)
					LastEntity = nil
				end
			end
		else
			Citizen.Wait(3000)
		end		
	end
end)

-- Key Controls
Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)

		if CurrentAction then
			ESX.ShowHelpNotification(CurrentActionMsg)

			if IsControlJustReleased(0, 38) and ESX.PlayerData.job and (ESX.PlayerData.job.name == 'mechanic' or ESX.PlayerData.job.name == 'mec') then

				if CurrentAction == 'mechanic_actions_menu' then
					OpenMechanicActionsMenu()
				elseif CurrentAction == 'mechanic_harvest_menu' then
				    if isOnDuty or not Config.EnableServiceMain then -- addon service
					OpenMechanicHarvestMenu()
					else -- addon service
				    ESX.ShowNotification(_U('service_not')) -- addon service
			        end	 -- addon service
				elseif CurrentAction == 'mechanic_craft_menu' then
				    if isOnDuty or not Config.EnableServiceMain then -- addon service
					OpenMechanicCraftMenu()
					else -- addon service
				    ESX.ShowNotification(_U('service_not')) -- addon service
			        end	 -- addon service
				elseif CurrentAction == 'delete_vehicle' then
					if Config.EnableSocietyOwnedVehicles then

						local vehicleProps = ESX.Game.GetVehicleProperties(CurrentActionData.vehicle)
						TriggerServerEvent('esx_society:putVehicleInGarage', 'mechanic', vehicleProps)
                    
					else

						if
							GetEntityModel(vehicle) == GetHashKey('flatbed')   or
							GetEntityModel(vehicle) == GetHashKey('towtruck2') or
							GetEntityModel(vehicle) == GetHashKey('slamvan3')
						then
							TriggerServerEvent('esx_service:disableService', 'mechanic')
						end

					end

					ESX.Game.DeleteVehicle(CurrentActionData.vehicle)
					TriggerServerEvent('esx_vehicleshop:DeleteVehicleFromMechanic', "mechanic")	

				elseif CurrentAction == 'remove_entity' then
					DeleteEntity(CurrentActionData.entity)
				end

				CurrentAction = nil
			end
		end

		if IsControlJustReleased(0, 167) and not isDead and ESX.PlayerData.job and (ESX.PlayerData.job.name == 'mechanic' or ESX.PlayerData.job.name == 'mec') then
		    if isOnDuty or not Config.EnableServiceMain then -- addon service
			OpenMobileMechanicActionsMenu()
			else -- addon service
				ESX.ShowNotification(_U('service_not')) -- addon service
			end	 -- addon service
		end

	end
end)

--------------
--extas menu--
--------------
function OpenVehicleExtrasMenu()
    local playerPed = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(playerPed, false)
    local availableExtras = {}

    if not DoesEntityExist(vehicle) then
	    ESX.ShowNotification(_U('novehiclenearby'))
        return
    end

    for i=0, 12 do
        if DoesExtraExist(vehicle, i) then
            local state = IsVehicleExtraTurnedOn(vehicle, i) == 1
            table.insert(availableExtras, {
                label = ('اكسسوار <span style="color:darkgoldenrod;">%s</span>: %s'):format(i, GetExtraLabel(state)),
                state = state,
                extraId = i
            })
        end
    end

    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'vehicle_extras', {
        title    = 'اكسسوارات السيارة',
        align    = 'top-left',
        elements = availableExtras
    }, function(data, menu)
        ToggleVehicleExtra(vehicle, data.current.extraId, data.current.state)

        menu.close()
        OpenVehicleExtrasMenu()
    end, function(data, menu)
        menu.close()
    end)
end

function ToggleVehicleExtra(vehicle, extraId, extraState)
    SetVehicleExtra(vehicle, extraId, extraState)
end

function GetExtraLabel(state)
    if state then
        return '<span style="color:green;">مفعل</span>'
    elseif not state then
        return '<span style="color:darkred;">غير مفعل</span>'
    end
end
--------------------
--END--extras menu--
--------------------

AddEventHandler('esx:onPlayerDeath', function(data) isDead = true end)
AddEventHandler('esx:onPlayerSpawn', function(spawn) isDead = false end)


function DisplayHelpText(str)
	SetTextComponentFormat("STRING")
	AddTextComponentString(str)
	DisplayHelpTextFromStringLabel(0, 0, 1, -1)
end


function bossmenu()
	ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'bossmenu', {
		title    = ('قائمة الأدارة'),
		align    = 'top-left',
		elements = {
			{label = ('الموظفين'), 		value = 'deployramp'},
			{label = ('ادارة الحجوزات'),  		value = 'ramprm'},
			
		}
	}, function(data2, menu2)
		


		if data2.current.value == 'deployramp' then
			TriggerEvent('esx_society:openBossMenu', 'mechanic', function(data, menu)
				menu.close()
			end, { wash = false }) -- disable washing money	
		elseif data2.current.value == 'ramprm' then
			ESX.TriggerServerCallback('konar:getpoundsnum', function(customers)
				local elements = {
					head = { 'معلومات الشخص', 'أسم الميكانيكي', 'عدد حجوزات الميكانيكي'},  
					rows = {}
				}
		
				for i=1, #customers, 1 do
					table.insert(elements.rows, {
						data = customers[i],
						cols = {
							customers[i].identfier,
							customers[i].name,
							customers[i].poundsnum,
							
						}
					})
				end

				ESX.UI.Menu.Open('list', GetCurrentResourceName(), 'poundsnum', elements, function(data2, menu2)

				end, function(data2, menu2)
					menu2.close()
				end)
			end)
		end


		end, function(data2, menu2)
			menu2.close()
		end)
end

Citizen.CreateThread(function()  -- منطقه  مدير
    while true do
		local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
		local playerPed = PlayerPedId()
		if GetDistanceBetweenCoords(coords, vector3(69.2572, 6502.5112, 31.2553)) < 10.0 then
			if Config.EnablePlayerManagement and ESX.PlayerData.job and ESX.PlayerData.job.grade_name == 'boss' or ESX.PlayerData.job.grade_name == 'bosstwo' then
				DrawMarker(1, vector3(69.2572, 6502.5112, 31.2553), 0.0, 0.0, 0.0, 0, 0.0, 0.0, 1.0, 1.0, 1.0, 255, 255, 0, 200, false, true, 2, true, false, false, false)
				 if GetDistanceBetweenCoords(coords, vector3(69.2572, 6502.5112, 31.2553)) < 2.0 then
					DisplayHelpText('<FONT FACE="sharlock">جﺍﺮﻜﻟﺍ ةﺭﺍﺩﻷ ~y~E~w~ ﻂﻐﺿأ')
					if IsControlJustReleased(0, 38) then
						bossmenu()
					end
				end
			end
			inzone2 = true
			lastsleep3 = 0
		elseif GetDistanceBetweenCoords(coords, vector3(826.6699, -949.1270, 22.0877)) < 10.0 then
			if Config.EnablePlayerManagement and ESX.PlayerData.job and ESX.PlayerData.job.grade_name == 'boss' or ESX.PlayerData.job.grade_name == 'bosstwo' then
				DrawMarker(1, vector3(826.6699, -949.1270, 22.0877), 0.0, 0.0, 0.0, 0, 0.0, 0.0, 1.0, 1.0, 1.0, 255, 255, 0, 200, false, true, 2, true, false, false, false)
				 if GetDistanceBetweenCoords(coords, vector3(826.6699, -949.1270, 22.0877)) < 2.0 then
					DisplayHelpText('<FONT FACE="sharlock">جﺍﺮﻜﻟﺍ ةﺭﺍﺩﻷ ~y~E~w~ ﻂﻐﺿأ')
					if IsControlJustReleased(0, 38) then
						bossmenu()
					end
				end
			end
			inzone2 = true
			lastsleep3 = 0
		else
			inzone2 = false
			lastsleep3 = 500
		end
		Citizen.Wait(lastsleep3)
    end
end)


