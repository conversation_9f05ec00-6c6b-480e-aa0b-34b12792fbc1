Config = {}
Config.location_sell_drugs_1 = false
Config.location_sell_drugs_1 = false
Config.location_sell_drugs_1 = false
---- [[[[[[[[[[[ اضافة خبرة ]]]]]]]]]]] -------
Config.XP0 = 750 -- حد اعطاء الخبرة ل الدعم الفني
Config.XP1 = 1000-- حد اعطاء الخبرة ل مشرف
Config.XP2 = 1500-- حد اعطاء الخبرة ل مشرف +
Config.XP3 = 2000-- حد اعطاء الخبرة ل ادمن
---- [[[[[[[[[[[ خصم خبرة ]]]]]]]]]]] -------
Config.XP00 = 750 -- حد خصم الخبرة ل الدعم الفني
Config.XP01 = 1000 -- حد خصم الخبرة ل مشرف
Config.XP02 = 1500-- حد خصم الخبرة ل مشرف +
Config.XP03 = 2000 -- حد خصم الخبرة ل ادمن

---- [[[[[[[[[[[ اعطاء اموال ]]]]]]]]]]] -------
Config.money0 = 50000 -- حد اعطاء اموال دعم فني
Config.money1 = 150000 -- حد اعطاء اموال مشرف
Config.money2 = 300000 -- حد اعطاء اموال مشرف +
Config.money3 = 1000000 -- حد اعطاء اموال ادمن

---- [[[[[[[[[[[ خصم اموال ]]]]]]]]]]] -------
Config.money00 = 150000 -- حد خصم اموال دعم فني
Config.money01 = 250000 -- حد خصم اموال مشرف
Config.money02 = 500000 -- حد خصم اموال مشرف +
Config.money03 = 1000000 -- حد خصم اموال ادمن

---- [[[[[[[[[[[ اعطاء اموال غير شرعي ]]]]]]]]]]] -------
Config.black0 = 100000 -- حد خصم اموال دعم فني
Config.black1 = 200000 -- حد خصم اموال مشرف
Config.black2 = 300000 -- حد خصم اموال مشرف +
Config.black3 = 400000 -- حد خصم اموال ادمن

---- [[[[[[[[[[[ سحب اموال غير شرعي ]]]]]]]]]]] -------
Config.black00 = 500000 -- حد خصم اموال دعم فني
Config.black01 = 700000 -- حد خصم اموال مشرف
Config.black02 = 900000 -- حد خصم اموال مشرف +
Config.black03 = 1000000 -- حد خصم اموال ادمن

Config.maxjail = 130

Config.moh = true

Config.DrawDistance               = 10.0 -- How close do you need to be for the markers to be drawn (in GTA units).
Config.MarkerType                 = {Cloakrooms = 20, Armories = 21, BossActions = 22, Vehicles = 36, Helicopters = 34}
Config.MarkerSize                 = {x = 1.5, y = 1.5, z = 0.5}
Config.MarkerColor                = {r = 250, g = 30, b = 34}

Config.EnablePlayerManagement     = true -- Enable if you want society managing.
Config.EnableArmoryManagement     = true
Config.EnableESXIdentity          = true -- Enable if you're using esx_identity.
Config.EnableLicenses             = false -- Enable if you're using esx_license.

Config.EnableHandcuffTimer        = true -- Enable handcuff timer? will unrestrain player after the time ends.
Config.HandcuffTimer              = 10 * 60000 -- 10 minutes.

Config.EnableJobBlip              = true -- Enable blips for cops on duty, requires esx_society.
Config.EnableCustomPeds           = false -- Enable custom peds in cloak room? See Config.CustomPeds below to customize peds.

Config.EnableESXService           = false -- Enable esx service?
Config.MaxInService               = -1 -- How much people can be in service at once?

Config.ESX          = true
Config.Tnotify      = false

Config.NoClipSpeed  = 1.95
Config.MenuAlign    = 'bottom-right'

Config.Locale                     = 'en'

Configuracion = {}

Configuracion.which = "name" -- don touch
Configuracion.drawDistance = 100

Configuracion.Grupos = {
    'superadmin',
    'admin',
    'moderator'
}

Config.pas                     = 'TWQQQQQQQQQQQQQQQQQQQQQQQQQQQQ'

itzsalah = {}
itzsalah.key = 10 -- Page Up
itzsalah.which = "name" -- "steam", "steamv2", "license", "licensev2", "name"
itzsalah.commandName = "id" -- chat command /id
itzsalah.drawDistance = 500

Config.bulletproof_cooltime = 10 --min

--Config.Weapon		= "WEAPON_PISTOL"
Config.Weapon		= "WEAPON_PISTOL"
Config.Icon         = ""
Config.Avatar       = ""

-- loggggg
Config.givemoney = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- اعطاء اموال
Config.hgz = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- حجز موتر(يمكن مو شغال)
Config.unjail = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- اعفاء من السجن
Config.jail = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- سجن
Config.ast3lam = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- استعلام 
Config.delchat = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' --حذف الشات 
Config.adminreviveself = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- رقابي انعش نفسه 
Config.giveadminjobforself = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- رقابي اعطى وضيفة لنفسه 
Config.givelicenses = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- --اعطاء رخصة 
Config.removelicense = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- سحب رخصة 
Config.givevehicle = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- اعطاء مركبة 
Config.removeshopfromthder = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- سحب متجر للوصل الحد الاقصى للانذارات
Config.removeshop = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- سحب متجر من رقابي
Config.giveshop = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH'  -- اعطاء متجر
Config.thedrmtgr = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- تحذير متجر 
Config.givemoneytoall = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' --اعطاء اموال لجميع المتصلين 
Config.givexptoall = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- اعطاء خبرة لجميع المتصلين
Config.givexptoone = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- اعطاء خبرة لشخص
Config.givemoneybank = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- اعطاء اموال في البنك 
Config.givemoneycash = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- اعطاء اموال في الكاش
Config.giveblackmoney = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' --اعطاء اموال غير شرعية 
Config.removeblackmoney = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' --سحب اموال غير شرعية 
Config.removemoneybank = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- سحب اموال من البنك
Config.removexp = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' -- ازالة خبرة 
Config.removemoneycash = 'https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH' --  سحب اموال من الكاش
Config.ReviveAll    = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH"
Config.Revive       = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH"
Config.Goto         = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH"
Config.Bring        = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH"
Config.Kill         = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH"
Config.WeaponPlayer = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH"
Config.Freeze       = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH"
Config.Kick         = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH"
Config.KickAll      = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH"
Config.Veh          = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH" -- 
Config.Obj          = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH"--  
Config.VehTime      = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH"-- 
Config.Storm        = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH"-- 
Config.GiveXPMainLogs    = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH"
Config.RemoveXPMainLogs   = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH"
Config.ReviveAll    = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH"
Config.Revive       = "https://discord.com/api/webhooks/1336110160774692946/hguWEOzxxNgsQNe88BxBGIiAjHqD5ho5LLE_5HW0rt3-tMddxPf2J-p4Orz4JZR7_DTH"
Config.pawwsordadmin = "yes" -- الباسورد حق اعطاء خبرة





Config.finesAdmin = {
	[1] = { -- عرامات الرقابة و التفتيش
		{ label = 'قيادة غير واقعية - المره الاولى', almrh = 10, amount = 10000 , ticket = false},
		{ label = 'قيادة غير واقعية - المره الثانية', almrh = 20, amount = 15000 , ticket = false},
		{ label = 'قيادة غير واقعية - المره الثالثة', almrh = 30, amount = 20000 , ticket = false},
		{ label = 'عدم الألتزام ب طابور الميناء - المره الاولى', almrh = 11, amount = 15000 , ticket = false},
		{ label = 'عدم الألتزام ب طابور الميناء - المره الثانية', almrh = 21, amount = 25000 , ticket = false},
		{ label = 'عدم الألتزام ب طابور الميناء - المره الثالثة', almrh = 31, amount = 35000 , ticket = false},
		{ label = 'حمل ادوات عمل في الحقيبة - المره الاولى', almrh = 12, amount = 5000 , ticket = false},
		{ label = 'حمل ادوات عمل في الحقيبة - المره الثانية', almrh = 22, amount = 10000 , ticket = false},
		{ label = 'حمل ادوات عمل في الحقيبة - المره الثالثة', almrh = 32, amount = 15000 , ticket = false},
		{ label = 'الهروب بعد سرقة البقالة بدون تفاوض - المره الاولى', almrh = 13, amount = 25000 , ticket = false},
		{ label = 'الهروب بعد سرقة البقالة بدون تفاوض - المره الثانية', almrh = 23, amount = 30000 , ticket = false},
		{ label = 'الهروب بعد سرقة البقالة بدون تفاوض - المره الثالثة', almrh = 33, amount = 35000 , ticket = false},
		{ label = 'الاعتداء على المسعف - المره الاولى', almrh = 14, amount = 25000 , ticket = false},
		{ label = 'الاعتداء على المسعف - المره الثانية', almrh = 24, amount = 30000 , ticket = false},
		{ label = 'الاعتداء على المسعف - المره الثالثة', almrh = 34, amount = 35000 , ticket = false},
		{ label = 'الاعتداء على كراج التعديل والتزويد - المره الاولى', almrh = 15, amount = 25000 , ticket = false},
		{ label = 'الاعتداء على كراج التعديل والتزويد - المره الثانية', almrh = 25, amount = 30000 , ticket = false},
		{ label = 'الاعتداء على كراج التعديل والتزويد - المره الثالثة', almrh = 35, amount = 35000 , ticket = false},
	}
}


Config.adminStations = {

	LSPD = {

		Blip = {
			Coords  = vector3(10000000000000000000, 100000000000000000000, 100000000000000000),
			Sprite  = 137,
			Display = 4,
			Scale   = 1.2,
			Colour  = 46
		},

		Cloakrooms = {
			--vector3(238.73, -1099.68, 29.36)
			vector3(-74.4957, -818.7656, 326.1752)
		},

		Armories = {
			vector3(-70.8868, -817.6839, 326.1752)
		},

		Vehicles = {
			{
				Spawner = vector3(251.8999938964844, -1176.97998046875, 29.5),
				InsideShop = vector3(327.26, -1159.88, 28.82),
				SpawnPoints = {
					{coords = vector3(232.98, -1157.01, 29.17), heading = 100.0, radius = 6.0},
					{coords = vector3(232.98, -1161.76, 29.17), heading = 100.0, radius = 6.0},
					{coords = vector3(232.98, -1165.74, 29.17), heading = 100.0, radius = 6.0},
				}
			}
		},

		Helicopters = {
			{
				Spawner = vector3(461.1, -981.5, -53.6),
				InsideShop = vector3(477.0, -1106.4, 43.0),
				SpawnPoints = {
					{coords = vector3(449.5, -981.2, 43.6), heading = 92.6, radius = 10.0}
				}
			}
		},

		BossActions = {
			vector3(-1061.5509, -235.3437, 44.0211)
		}

	},
	
	LSPD2 = { -- مركز شرطة ساندي

		Blip = {
		},

		Cloakrooms = {
			--vector3(238.73, -1099.68, 29.36)
			vector3(1849.597, 3695.695, 34.26194)
		},

		Armories = {
			vector3(1842.297, 3690.874, 34.2584)
		},

		Vehicles = {
			{
				Spawner = vector3(1877.59, 3689.67, 33.4),
				InsideShop = vector3(327.26, -1159.88, 28.82),
				SpawnPoints = {
					{coords = vector3(1874.45, 3695.36, 33.47), heading = 120.17, radius = 6.0},
					{coords = vector3(1869.98, 3702.24, 33.34), heading = 120.17, radius = 6.0}
				}
			}
		},

		Helicopters = {
		},

		BossActions = {
			vector3(1862.54, 3690.51, 34.26)
		}

	},
	
	LSPD3 = { -- كراج الميكانيك

		Blip = {
		},

		Cloakrooms = {
			--vector3(238.73, -1099.68, 29.36)
			vector3(2207.607, 2916.823, 48.037)
		},

		Armories = {
			vector3(2201.114, 2914.999, 48.027)
		},

		Vehicles = {
			{
				Spawner = vector3(2197.487, 2923.042, 46.526),
				InsideShop = vector3(2208.46, 2938.49, 58.58),
				SpawnPoints = {
					{coords = vector3(2191.492, 2919.555, 46.394), heading = 187.79, radius = 6.0},
					{coords = vector3(2193.13, 2906.46, 46.394), heading = 187.79, radius = 6.0}
				}
			}
		},

		Helicopters = {
		},

		BossActions = {
			vector3(2202.66, 2909.23, 48.03)
		}

	},
	
	LSPD4 = { -- مركز شرطة بليتو

		Blip = {
		},

		Cloakrooms = {
            vector3(-453.350, 6015.530, 31.716)			
		},

		Armories = {
			vector3(-428.703, 5995.285, 31.716),
		},

		Vehicles = {

			{
				Spawner = vector3(-447.924, 6026.608, 31.490),
				InsideShop = vector3(-447.411, 6050.057, 31.340),
				SpawnPoints = {
					{coords = vector3(-453.995, 6028.269, 31.340), heading = 311.61, radius = 6.0},
					{coords = vector3(-467.369, 6015.701, 31.340), heading = 309.73, radius = 6.0}
				}
			},
			
		},

		Helicopters = {
		},

		BossActions = {
			vector3(-433.49, 6003.53, 31.72),
		}

	},
	
	LSPD5 = { -- مركز شرطة لوس رئيسي

		Blip = {
		},

		Cloakrooms = {
            vector3(-453.350, 6015.530, 31.716)			
		},

		Armories = {
			vector3(-428.703, 5995.285, 31.716),
		},

		Vehicles = {

			{
				Spawner = vector3(-1083.4208, -264.8883, 37.7077),
				InsideShop = vector3(-447.411, 6050.057, 31.340),
				SpawnPoints = {
					{coords = vector3(-453.995, 6028.269, 31.340), heading = 311.61, radius = 6.0},
					{coords = vector3(-467.369, 6015.701, 31.340), heading = 309.73, radius = 6.0}
				}
			},
			
		},

		Helicopters = {
		},

		BossActions = {
			vector3(-433.49, 6003.53, 31.72),
		}

	},
	
	LSPD5 = { -- ميناء  البحري

		Blip = {
		},

		Cloakrooms = {
            vector3(-57.59000015258789, -2483.409912109375, 6.03000020980835),			
		},

		Armories = {
			vector3(808.5564, -2912.493, 6.126944),
		},

		Vehicles = {

			{
				Spawner = vector3(-1083.5940, -264.8284, 37.7152),
				InsideShop = vector3(791.14, -2992.6, 6.02),
				SpawnPoints = {
					{coords = vector3(-1075.9401, -265.7571, 37.6923), heading = 271.49, radius = 6.0},
					{coords = vector3(-1075.9401, -265.7571, 37.6923), heading = 271.49, radius = 6.0},
					{coords = vector3(-1075.9401, -265.7571, 37.6923), heading = 271.49, radius = 6.0},
					{coords = vector3(-1075.9401, -265.7571, 37.6923), heading = 271.49, radius = 6.0},
					{coords = vector3(-1075.9401, -265.7571, 37.6923), heading = 271.49, radius = 6.0}
				}
			},
			
		},

		Helicopters = {
			{
				Spawner = vector3(793.52, -2919.13, 10.27),
				InsideShop = vector3(805.73, -2915.81, 12.09),
				SpawnPoints = {
					{coords = vector3(805.73, -2915.81, 12.09), heading = 266.67, radius = 6.0}
				}
			}
		},

		BossActions = {
			vector3(812.08, -2903.83, 6.13),
		}

	},
	
	LSPD6 = { --  

		Blip = {
		},

		Cloakrooms = {
            vector3(-63.4831, -2513.93, 7.3874),			
		},

		Armories = {
			vector3(-43.8663, -2516.79, 7.3874),
		},

		Vehicles = {

			{
				Spawner = vector3(-39.3681, -2515.36, 6.1592),
				InsideShop = vector3(-40.2293, -2544.95, 6.0100),
				SpawnPoints = {
					{coords = vector3(-46.33, -2529.25, 6.0100), heading = 325.9, radius = 6.0},
					{coords = vector3(-40.84, -2521.93, 6.0100), heading = 325.9, radius = 6.0}
				}
			},
			
		},

		Helicopters = {
		},

		BossActions = {
			vector3(-42.2667, -2514.61, 7.3874),
		}

	}
	
}

Config.WeaponsPrice = {
	WEAPON_PISTOL            = 3000,
	WEAPON_PUMPSHOTGUN       = 35000,
	WEAPON_ADVANCEDRIFLE     = 35000,
	WEAPON_CARBINERIFLE      = 45000,
	WEAPON_STUNGUN           = 5000,
	WEAPON_FLAREGUN          = 5000,
	WEAPON_FLARE             = 3000,
	WEAPON_NIGHTSTICK        = 0,
	WEAPON_FLASHLIGHT        = 0,
	WEAPON_PETROLCAN         = 0,
	WEAPON_FIREEXTINGUISHER  = 0
}

Config.AuthorizedWeapons = {
	boss = {
		{ weapon = 'WEAPON_PISTOL', components = { 0, 0, 1000, 4000, nil },                 price = Config.WeaponsPrice.WEAPON_PISTOL },
		{ weapon = 'WEAPON_PUMPSHOTGUN', components = { 2000, 6000, nil },                  price = Config.WeaponsPrice.WEAPON_PUMPSHOTGUN },
		{ weapon = 'WEAPON_CARBINERIFLE', components = { 0, 9000, 15000, 4000, 10000, 10000, 8000, nil }, price = Config.WeaponsPrice.WEAPON_CARBINERIFLE },
		{ weapon = 'WEAPON_NIGHTSTICK',     price = Config.WeaponsPrice.WEAPON_NIGHTSTICK },
		{ weapon = 'WEAPON_STUNGUN',        price = Config.WeaponsPrice.WEAPON_STUNGUN },
		{ weapon = 'WEAPON_FLASHLIGHT',     price = Config.WeaponsPrice.WEAPON_FLASHLIGHT },
		{ weapon = 'WEAPON_FLARE',          price = Config.WeaponsPrice.WEAPON_FLARE },
		{ weapon = 'WEAPON_PETROLCAN',      price = Config.WeaponsPrice.WEAPON_PETROLCAN },
		{ weapon = 'WEAPON_FIREEXTINGUISHER',  price = Config.WeaponsPrice.WEAPON_FIREEXTINGUISHER }
	},

	high_admin = {
		{ weapon = 'WEAPON_PISTOL', components = { 0, 0, 1000, 4000, nil },                 price = Config.WeaponsPrice.WEAPON_PISTOL },
		{ weapon = 'WEAPON_PUMPSHOTGUN', components = { 2000, 6000, nil },                  price = Config.WeaponsPrice.WEAPON_PUMPSHOTGUN },
		{ weapon = 'WEAPON_CARBINERIFLE', components = { 0, 9000, 15000, 4000, 10000, 10000, 8000, nil }, price = Config.WeaponsPrice.WEAPON_CARBINERIFLE },
		{ weapon = 'WEAPON_NIGHTSTICK',     price = Config.WeaponsPrice.WEAPON_NIGHTSTICK },
		{ weapon = 'WEAPON_STUNGUN',        price = Config.WeaponsPrice.WEAPON_STUNGUN },
		{ weapon = 'WEAPON_FLASHLIGHT',     price = Config.WeaponsPrice.WEAPON_FLASHLIGHT },
		{ weapon = 'WEAPON_FLARE',          price = Config.WeaponsPrice.WEAPON_FLARE },
		{ weapon = 'WEAPON_PETROLCAN',      price = Config.WeaponsPrice.WEAPON_PETROLCAN },
		{ weapon = 'WEAPON_FIREEXTINGUISHER',  price = Config.WeaponsPrice.WEAPON_FIREEXTINGUISHER }
	},

	meduim_admin = {
		{ weapon = 'WEAPON_PISTOL', components = { 0, 0, 1000, 4000, nil },                 price = Config.WeaponsPrice.WEAPON_PISTOL },
		{ weapon = 'WEAPON_PUMPSHOTGUN', components = { 2000, 6000, nil },                  price = Config.WeaponsPrice.WEAPON_PUMPSHOTGUN },
		{ weapon = 'WEAPON_CARBINERIFLE', components = { 0, 9000, 15000, 4000, 10000, 10000, 8000, nil }, price = Config.WeaponsPrice.WEAPON_CARBINERIFLE },
		{ weapon = 'WEAPON_NIGHTSTICK',     price = Config.WeaponsPrice.WEAPON_NIGHTSTICK },
		{ weapon = 'WEAPON_STUNGUN',        price = Config.WeaponsPrice.WEAPON_STUNGUN },
		{ weapon = 'WEAPON_FLASHLIGHT',     price = Config.WeaponsPrice.WEAPON_FLASHLIGHT },
		{ weapon = 'WEAPON_FLARE',          price = Config.WeaponsPrice.WEAPON_FLARE },
		{ weapon = 'WEAPON_PETROLCAN',      price = Config.WeaponsPrice.WEAPON_PETROLCAN },
		{ weapon = 'WEAPON_FIREEXTINGUISHER',  price = Config.WeaponsPrice.WEAPON_FIREEXTINGUISHER }
	},

	recruit = {
		{ weapon = 'WEAPON_PISTOL', components = { 0, 0, 1000, 4000, nil },                 price = Config.WeaponsPrice.WEAPON_PISTOL },
		{ weapon = 'WEAPON_PUMPSHOTGUN', components = { 2000, 6000, nil },                  price = Config.WeaponsPrice.WEAPON_PUMPSHOTGUN },
		{ weapon = 'WEAPON_CARBINERIFLE', components = { 0, 9000, 15000, 4000, 10000, 10000, 8000, nil }, price = Config.WeaponsPrice.WEAPON_CARBINERIFLE },
		{ weapon = 'WEAPON_NIGHTSTICK',     price = Config.WeaponsPrice.WEAPON_NIGHTSTICK },
		{ weapon = 'WEAPON_STUNGUN',        price = Config.WeaponsPrice.WEAPON_STUNGUN },
		{ weapon = 'WEAPON_FLASHLIGHT',     price = Config.WeaponsPrice.WEAPON_FLASHLIGHT },
		{ weapon = 'WEAPON_FLARE',          price = Config.WeaponsPrice.WEAPON_FLARE },
		{ weapon = 'WEAPON_PETROLCAN',      price = Config.WeaponsPrice.WEAPON_PETROLCAN },
		{ weapon = 'WEAPON_FIREEXTINGUISHER',  price = Config.WeaponsPrice.WEAPON_FIREEXTINGUISHER }
	},

	low_admin = {
		{ weapon = 'WEAPON_PISTOL', components = { 0, 0, 1000, 4000, nil },                 price = Config.WeaponsPrice.WEAPON_PISTOL },
		{ weapon = 'WEAPON_PUMPSHOTGUN', components = { 2000, 6000, nil },                  price = Config.WeaponsPrice.WEAPON_PUMPSHOTGUN },
		{ weapon = 'WEAPON_CARBINERIFLE', components = { 0, 9000, 15000, 4000, 10000, 10000, 8000, nil }, price = Config.WeaponsPrice.WEAPON_CARBINERIFLE },
		{ weapon = 'WEAPON_NIGHTSTICK',     price = Config.WeaponsPrice.WEAPON_NIGHTSTICK },
		{ weapon = 'WEAPON_STUNGUN',        price = Config.WeaponsPrice.WEAPON_STUNGUN },
		{ weapon = 'WEAPON_FLASHLIGHT',     price = Config.WeaponsPrice.WEAPON_FLASHLIGHT },
		{ weapon = 'WEAPON_FLARE',          price = Config.WeaponsPrice.WEAPON_FLARE },
		{ weapon = 'WEAPON_PETROLCAN',      price = Config.WeaponsPrice.WEAPON_PETROLCAN },
		{ weapon = 'WEAPON_FIREEXTINGUISHER',  price = Config.WeaponsPrice.WEAPON_FIREEXTINGUISHER }
	}
}


-- CHECK SKINCHANGER CLIENT MAIN.LUA for matching elements
Config.Uniforms = {

	cid_badge = { -- باقة الامن الجنائي
		male = {
			--['chain_1'] = 125,  ['chain_2'] = 0
			['chain_1'] = 128,  ['chain_2'] = 0
		},
		female = {
			['chain_1'] = 128,  ['chain_2'] = 0
		}
	},
	
	remove_bullet_wear = { -- إزالة مضاد رصاص
		male = {
			['bproof_1'] = 0,  ['bproof_2'] = 0
		},
		female = {
			['bproof_1'] = 0,  ['bproof_2'] = 0
		}
	},
	
	cid_badge_remove = { -- إزالة باقة الامن الجنائي
		male = {
			['chain_1'] = 0,  ['chain_2'] = 0
		},
		female = {
			['chain_1'] = 0,  ['chain_2'] = 0
		}
	},
	
	gun_bealt = {        	-- حزام مسدس
		male = {
			['tshirt_1'] = 130,  ['tshirt_2'] = 0
		},
		female = {       
			['tshirt_1'] = 130,  ['tshirt_2'] = 0
		}
	},
	radio_bealt = { -- حزام لاسلكي ومطاعة
		male = {
			['tshirt_1'] = 58,  ['tshirt_2'] = 0
		},
		female = {
			['tshirt_1'] = 58,  ['tshirt_2'] = 0
		}
	},
	black_jacket = { -- جاكيت اسود
		male = {
			['tshirt_1'] = 56,  ['tshirt_2'] = 0
		},
		female = {
			['tshirt_1'] = 56,  ['tshirt_2'] = 0
		}
	},   
	
	helmet_open_police = { 	-- خوذة اسود مفتوح 
		male = {
			['helmet_1'] = 126,  ['helmet_2'] = 18
		},
		female = {
			['helmet_1'] = 126,  ['helmet_2'] = 18
		}
	},
	helmet_1 = { --بريهة اسود
		male = {
			['helmet_1'] = 28,  ['helmet_2'] = 0,
		},
		female = {
			['helmet_1'] = 28,  ['helmet_2'] = 0,
		}
	},
	helmet_2 = { --كاب شرطة ابيض
		male = {
			['helmet_1'] = 113, ['helmet_2'] = 5,
		},
		female = {
			['helmet_1'] = 113, ['helmet_2'] = 5,
		}
	},
	
	helmet_close_police = {	-- خوذة اسود مغلق
		male = {
			['helmet_1'] = 125,  ['helmet_2'] = 18
		},
		female = {
			['helmet_1'] = 125,  ['helmet_2'] = 18
		}
	},
	
	helmet_close_police = {	-- خوذة اسود مغلق
		male = {
			['helmet_1'] = 125,  ['helmet_2'] = 18
		},
		female = {
			['helmet_1'] = 125,  ['helmet_2'] = 18
		}
	},
	
	cid_wear = { -- مكافحة المخدرات
		male = {
			tshirt_1 = 122,  tshirt_2 = 0,
			torso_1 = 3,   torso_2 = 1,
			decals_1 = 0,   decals_2 = 0,
			arms = 30, arms_2 = 0,
			pants_1 = 33,   pants_2 = 0,
			shoes_1 = 1,   shoes_2 = 0,
			helmet_1 = 58,  helmet_2 = 2,
			chain_1 = 0,    chain_2 = 0,
			ears_1 = 0,     ears_2 = 0
		},
		female = {
			['tshirt_1'] = 130,  ['tshirt_2'] = 0,
			['torso_1'] = 43,   ['torso_2'] = 0,
			['decals_1'] = 0,   ['decals_2'] = 0,
			['arms'] = 11,
			['pants_1'] = 20,   ['pants_2'] = 2,
			['shoes_1'] = 14,   ['shoes_2'] = 2,
			['helmet_1'] = -1,  ['helmet_2'] = 0,
			['chain_1'] = 0,    ['chain_2'] = 0,
			['ears_1'] = 0,     ['ears_2'] = 0
		}
	},
  admin1 = { -- مكافحة المخدرات
  male = {
	tshirt_1 = 122,  tshirt_2 = 0,
	torso_1 = 108,   torso_2 = 1,
	decals_1 = 0,   decals_2 = 0,
	arms = 30, arms_2 = 0,
	pants_1 = 33,   pants_2 = 0,
	shoes_1 = 1,   shoes_2 = 0,
	helmet_1 = 58,  helmet_2 = 2,
	chain_1 = 0,    chain_2 = 0,
	ears_1 = 0,     ears_2 = 0
},
  female = {
    ['tshirt_1'] = 130,  ['tshirt_2'] = 0,
    ['torso_1'] = 43,   ['torso_2'] = 0,
    ['decals_1'] = 0,   ['decals_2'] = 0,
    ['arms'] = 11,
    ['pants_1'] = 20,   ['pants_2'] = 2,
    ['shoes_1'] = 14,   ['shoes_2'] = 2,
    ['helmet_1'] = -1,  ['helmet_2'] = 0,
    ['chain_1'] = 0,    ['chain_2'] = 0,
    ['ears_1'] = 0,     ['ears_2'] = 0
  }
},
admin2 = { -- مكافحة المخدرات
male = {
	tshirt_1 = 122,  tshirt_2 = 0,
	torso_1 = 108,   torso_2 = 0,
	decals_1 = 0,   decals_2 = 0,
	arms = 30, arms_2 = 0,
	pants_1 = 33,   pants_2 = 0,
	shoes_1 = 1,   shoes_2 = 0,
	helmet_1 = 58,  helmet_2 = 2,
	chain_1 = 0,    chain_2 = 0,
	ears_1 = 0,     ears_2 = 0
},
female = {
  ['tshirt_1'] = 130,  ['tshirt_2'] = 0,
  ['torso_1'] = 43,   ['torso_2'] = 0,
  ['decals_1'] = 0,   ['decals_2'] = 0,
  ['arms'] = 11,
  ['pants_1'] = 20,   ['pants_2'] = 2,
  ['shoes_1'] = 14,   ['shoes_2'] = 2,
  ['helmet_1'] = -1,  ['helmet_2'] = 0,
  ['chain_1'] = 0,    ['chain_2'] = 0,
  ['ears_1'] = 0,     ['ears_2'] = 0
}
},
admin3 = { -- مكافحة المخدرات
male = {
	tshirt_1 = 15,  tshirt_2 = 0,
	torso_1 = 95,   torso_2 = 0,
	decals_1 = 0,   decals_2 = 0,
	arms = 31, arms_2 = 0,
	pants_1 = 33,   pants_2 = 0,
	shoes_1 = 1,   shoes_2 = 0,
	helmet_1 = 58,  helmet_2 = 2,
	chain_1 = 0,    chain_2 = 0,
	ears_1 = 0,     ears_2 = 0
},
female = {
  ['tshirt_1'] = 130,  ['tshirt_2'] = 0,
  ['torso_1'] = 43,   ['torso_2'] = 0,
  ['decals_1'] = 0,   ['decals_2'] = 0,
  ['arms'] = 11,
  ['pants_1'] = 20,   ['pants_2'] = 2,
  ['shoes_1'] = 14,   ['shoes_2'] = 2,
  ['helmet_1'] = -1,  ['helmet_2'] = 0,
  ['chain_1'] = 0,    ['chain_2'] = 0,
  ['ears_1'] = 0,     ['ears_2'] = 0
}
},
admin4 = { -- مكافحة المخدرات
male = {
	tshirt_1 = 122,  tshirt_2 = 0,
	torso_1 = 98,   torso_2 = 0,
	decals_1 = 0,   decals_2 = 0,
	arms = 31, arms_2 = 0,
	pants_1 = 33,   pants_2 = 0,
	shoes_1 = 1,   shoes_2 = 0,
	helmet_1 = 58,  helmet_2 = 2,
	chain_1 = 0,    chain_2 = 0,
	ears_1 = 0,     ears_2 = 0
},
female = {
  ['tshirt_1'] = 130,  ['tshirt_2'] = 0,
  ['torso_1'] = 43,   ['torso_2'] = 0,
  ['decals_1'] = 0,   ['decals_2'] = 0,
  ['arms'] = 11,
  ['pants_1'] = 20,   ['pants_2'] = 2,
  ['shoes_1'] = 14,   ['shoes_2'] = 2,
  ['helmet_1'] = -1,  ['helmet_2'] = 0,
  ['chain_1'] = 0,    ['chain_2'] = 0,
  ['ears_1'] = 0,     ['ears_2'] = 0
}
},
admin5 = { -- مكافحة المخدرات
male = {
	tshirt_1 = 122,  tshirt_2 = 0,
	torso_1 = 10,   torso_2 = 0,
	decals_1 = 0,   decals_2 = 0,
	arms = 31, arms_2 = 0,
	pants_1 = 33,   pants_2 = 0,
	shoes_1 = 1,   shoes_2 = 0,
	helmet_1 = 58,  helmet_2 = 2,
	chain_1 = 0,    chain_2 = 0,
	ears_1 = 0,     ears_2 = 0
},
female = {
  ['tshirt_1'] = 130,  ['tshirt_2'] = 0,
  ['torso_1'] = 43,   ['torso_2'] = 0,
  ['decals_1'] = 0,   ['decals_2'] = 0,
  ['arms'] = 11,
  ['pants_1'] = 20,   ['pants_2'] = 2,
  ['shoes_1'] = 14,   ['shoes_2'] = 2,
  ['helmet_1'] = -1,  ['helmet_2'] = 0,
  ['chain_1'] = 0,    ['chain_2'] = 0,
  ['ears_1'] = 0,     ['ears_2'] = 0
}
},

    empty = { -- مكافحة المخدرات
		male = {
		},
		female = {
		}
	},
	
	mask_remove = { -- إزالة قناع
		male = {
			['mask_1'] = 0,		['mask_2'] = 0,
		},
		female = {
			['mask_1'] = 0,		['mask_2'] = 0,
		}
	},
	helmet_remove = { -- إزالة قناع
		male = {
			['helmet_1'] = -1, ['helmet_2'] = 0
		},
		female = {
			['helmet_1'] = -1, ['helmet_2'] = 0
		}
	},
	
	control_helmet = { -- كاب إدارة الرقابة والتفتيش
		male = {
			['helmet_1'] = 63, ['helmet_2'] = 9
		},
		female = {
			['helmet_1'] = -1, ['helmet_2'] = 0
		}
	},
	
	support = {
		male = {
			tshirt_1 = 122,  tshirt_2 = 0,
			torso_1 = 3,   torso_2 = 1,
			decals_1 = 0,   decals_2 = 0,
			arms = 30, arms_2 = 0,
			pants_1 = 33,   pants_2 = 0,
			shoes_1 = 1,   shoes_2 = 0,
			helmet_1 = 58,  helmet_2 = 2,
			chain_1 = 0,    chain_2 = 0,
			ears_1 = 0,     ears_2 = 0
		},
		female = {
			tshirt_1 = 35,  tshirt_2 = 0,
			torso_1 = 48,   torso_2 = 0,
			decals_1 = 7,   decals_2 = 2,
			arms = 44,
			pants_1 = 34,   pants_2 = 0,
			shoes_1 = 27,   shoes_2 = 0,
			helmet_1 = -1,  helmet_2 = 0,
			chain_1 = 0,    chain_2 = 0,
			ears_1 = 2,     ears_2 = 0
		}
	},

	control_1 = {
		male = {
			tshirt_1 = 122,  tshirt_2 = 0,
			torso_1 = 3,   torso_2 = 0,
			decals_1 = 0,   decals_2 = 0,
			arms = 30, arms_2 = 0,
			pants_1 = 33,   pants_2 = 0,
			shoes_1 = 1,   shoes_2 = 0,
			helmet_1 = 58,  helmet_2 = 2,
			chain_1 = 0,    chain_2 = 0,
			ears_1 = 0,     ears_2 = 0
		},
		female = {
			tshirt_1 = 35,  tshirt_2 = 0,
			torso_1 = 48,   torso_2 = 0,
			decals_1 = 7,   decals_2 = 2,
			arms = 44,
			pants_1 = 34,   pants_2 = 0,
			shoes_1 = 27,   shoes_2 = 0,
			helmet_1 = -1,  helmet_2 = 0,
			chain_1 = 0,    chain_2 = 0,
			ears_1 = 2,     ears_2 = 0
		}
	},

	control_1_mndm = {
		male = {
			tshirt_1 = 15,  tshirt_2 = 0,
			torso_1 = 95,   torso_2 = 0,
			decals_1 = 0,   decals_2 = 0,
			arms = 31, arms_2 = 0,
			pants_1 = 33,   pants_2 = 0,
			shoes_1 = 1,   shoes_2 = 0,
			helmet_1 = 58,  helmet_2 = 2,
			chain_1 = 0,    chain_2 = 0,
			ears_1 = 0,     ears_2 = 0
		},
		female = {
			tshirt_1 = 35,  tshirt_2 = 0,
			torso_1 = 48,   torso_2 = 0,
			decals_1 = 7,   decals_2 = 2,
			arms = 44,
			pants_1 = 34,   pants_2 = 0,
			shoes_1 = 27,   shoes_2 = 0,
			helmet_1 = -1,  helmet_2 = 0,
			chain_1 = 0,    chain_2 = 0,
			ears_1 = 2,     ears_2 = 0
		}
	},

	boss = {
		male = {
			tshirt_1 = 122,  tshirt_2 = 0,
			torso_1 = 98,   torso_2 = 0,
			decals_1 = 0,   decals_2 = 0,
			arms = 31, arms_2 = 0,
			pants_1 = 33,   pants_2 = 0,
			shoes_1 = 1,   shoes_2 = 0,
			helmet_1 = 58,  helmet_2 = 2,
			chain_1 = 0,    chain_2 = 0,
			ears_1 = 0,     ears_2 = 0
		},
		female = {
			tshirt_1 = 35,  tshirt_2 = 0,
			torso_1 = 48,   torso_2 = 0,
			decals_1 = 7,   decals_2 = 2,
			arms = 44,
			pants_1 = 34,   pants_2 = 0,
			shoes_1 = 27,   shoes_2 = 0,
			helmet_1 = -1,  helmet_2 = 0,
			chain_1 = 1,    chain_2 = 0,
			ears_1 = 2,     ears_2 = 0
		}
	}, --]]
	
	miner = {        	 --شركة الأخشاب المحلية 
        male = {
			['tshirt_1'] = 59,  ['tshirt_2'] = 1,
			['torso_1'] = 22,   ['torso_2'] = 1,
			['decals_1'] = 0,   ['decals_2'] = 0,
			['arms'] = 52,
			['pants_1'] = 0,   ['pants_2'] = 12,
			['shoes_1'] = 12,   ['shoes_2'] = 6,
			['helmet_1'] = 60,  ['helmet_2'] = 7,
			['chain_1'] = 0,    ['chain_2'] = 0,
			['ears_1'] = 5,     ['ears_2'] = 3,
			['mask_1'] = 0,     ['mask_2'] = 0
		},
	    female = {
			['tshirt_1'] = 59,  ['tshirt_2'] = 1,
			['torso_1'] = 71,   ['torso_2'] = 0,
			['decals_1'] = 0,   ['decals_2'] = 0,
			['arms'] = 2,
			['pants_1'] = 75,   ['pants_2'] = 7,
			['shoes_1'] = 51,   ['shoes_2'] = 1,
			['helmet_1'] = 0,  ['helmet_2'] = 0,
			['chain_1'] = 0,    ['chain_2'] = 0,
			['ears_1'] = 0,     ['ears_2'] = 0,
			['mask_1'] = 0,     ['mask_2'] = 0
		}
	},
	sluaghter = {       -- شركة نايف للدواجن
        male = {
			['tshirt_1'] = 1,  ['tshirt_2'] = 0,
			['torso_1'] = 1,   ['torso_2'] = 0,
			['decals_1'] = 0,   ['decals_2'] = 0,
			['arms'] = 63,
			['pants_1'] = 36,   ['pants_2'] = 0,
			['shoes_1'] = 12,   ['shoes_2'] = 0,
			['helmet_1'] = 0,  ['helmet_2'] = 0,
			['chain_1'] = 0,    ['chain_2'] = 0,
			['mask_1'] = 0,     ['mask_2'] = 0
		},
	    female = {
			['tshirt_1'] = 57,  ['tshirt_2'] = 0,
			['torso_1'] = 56,   ['torso_2'] = 0,
			['decals_1'] = 0,   ['decals_2'] = 0,
			['arms'] = 63,
			['pants_1'] = 36,   ['pants_2'] = 0,
			['shoes_1'] = 12,   ['shoes_2'] = 0,
			['helmet_1'] = 0,  ['helmet_2'] = 0,
			['chain_1'] = 0,    ['chain_2'] = 0,
			['mask_1'] = 0,     ['mask_2'] = 0
		}
	},
	tailor = { 			-- شركة فاشنستا
        male = {
			['tshirt_1'] = 0,  ['tshirt_2'] = 0,
			['torso_1'] = 24,   ['torso_2'] = 0,
			['decals_1'] = 0,   ['decals_2'] = 0,
			['arms'] = 1,
			['pants_1'] = 48,   ['pants_2'] = 0,
			['shoes_1'] = 36,   ['shoes_2'] = 0,
			['chain_1'] = 0,    ['chain_2'] = 0,
			['helmet_1'] = -1,  ['helmet_2'] = 0,
			['mask_1'] = 0,     ['mask_2'] = 0
		},
	    female = {
			['tshirt_1'] = 0,  ['tshirt_2'] = 0,
			['torso_1'] = 24,   ['torso_2'] = 0,
			['decals_1'] = 0,   ['decals_2'] = 0,
			['arms'] = 1,
			['pants_1'] = 48,   ['pants_2'] = 0,
			['shoes_1'] = 36,   ['shoes_2'] = 0,
			['chain_1'] = 0,    ['chain_2'] = 0,
			['helmet_1'] = -1,  ['helmet_2'] = 0,
			['mask_1'] = 0,     ['mask_2'] = 0
		}
	},
	vigne = { 			-- شركة المرطبات
        male = {
			['tshirt_1'] = 59,  ['tshirt_2'] = 0,
			['torso_1']  = 12,   ['torso_2']  = 5,
			['decals_1'] = 0,   ['decals_2'] = 0,
			['arms'] 	= 11,
			['pants_1'] = 9,    ['pants_2'] = 7,
			['shoes_1'] = 7,    ['shoes_2'] = 2,
			['chain_1'] = 0,    ['chain_2'] = 0,
			['helmet_1'] = 11,  ['helmet_2'] = 0,
			['mask_1'] = 0,     ['mask_2'] = 0
		},
	    female = {
			['tshirt_1'] = 59,  ['tshirt_2'] = 0,
			['torso_1']  = 12,   ['torso_2']  = 5,
			['decals_1'] = 0,   ['decals_2'] = 0,
			['arms'] 	= 11,
			['pants_1'] = 9,    ['pants_2'] = 7,
			['shoes_1'] = 7,    ['shoes_2'] = 2,
			['chain_1'] = 0,    ['chain_2'] = 0,
			['helmet_1'] = 11,  ['helmet_2'] = 0,
			['mask_1'] = 0,     ['mask_2'] = 0
		}
	},
	vigne2 = { 			-- شركة المرطبات
        male = {
			['tshirt_1'] = 15,  ['tshirt_2'] = 0,
			['torso_1']  = 13,   ['torso_2']  = 5,
			['decals_1'] = 0,   ['decals_2'] = 0,
			['arms'] 	= 11,
			['pants_1'] = 9,    ['pants_2'] = 7,
			['shoes_1'] = 7,    ['shoes_2'] = 2,
			['chain_1'] = 0,    ['chain_2'] = 0,
			['helmet_1'] = 11,  ['helmet_2'] = 0,
			['mask_1'] = 0,     ['mask_2'] = 0
		},
	    female = {
			['tshirt_1'] = 57,  ['tshirt_2'] = 0,
			['torso_1']  = 13,   ['torso_2']  = 5,
			['decals_1'] = 0,   ['decals_2'] = 0,
			['arms'] 	= 11,
			['pants_1'] = 9,    ['pants_2'] = 7,
			['shoes_1'] = 7,    ['shoes_2'] = 2,
			['chain_1'] = 0,    ['chain_2'] = 0,
			['helmet_1'] = 11,  ['helmet_2'] = 0,
			['mask_1'] = 0,     ['mask_2'] = 0
		}
	},

	bullet_wear = {
		male = {
			bproof_1 = 11,  bproof_2 = 1
		},
		female = {
			bproof_1 = 13,  bproof_2 = 1
		}
	},

	gilet_wear = {
		male = {
			bproof_1 = 1,  bproof_2 = 0
		},
		female = {
			bproof_1 = 1,  bproof_2 = 0
		}
	}
}

return Config



