-- تم انشاء هاذي الوظيفة من قبل      من قبل مجتمع esx العربي       dev_esx  https://discord.gg/TnuKAfUkSV
-- تم انشاء هاذي الوظيفة من قبل      من قبل مجتمع esx العربي       dev_esx  https://discord.gg/TnuKAfUkSV
-- تم انشاء هاذي الوظيفة من قبل      من قبل مجتمع esx العربي       dev_esx  https://discord.gg/TnuKAfUkSV
-- تم انشاء هاذي الوظيفة من قبل      من قبل مجتمع esx العربي       dev_esx  https://discord.gg/TnuKAfUkSV

local PlayersWorking = {}

function sendToDiscord (name,message,color)
  local DiscordWebHook = ""

local embeds = {
    {
        ["title"]=message,
        ["type"]="rich",
        ["color"] =color,
        ["footer"]=  { ["text"]= "الوظايف العامة", },
    }
}

  if message == nil or message == '' then return FALSE end
  PerformHttpRequest(Discord, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

ESX = nil

TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

local function Work(source, item)

	SetTimeout(item[1].time, function()

		if PlayersWorking[source] == true then

			local xPlayer = ESX.GetPlayerFromId(source)
			if xPlayer == nil then
				return
			end

			for i=1, #item, 1 do
				local itemQtty, requiredItemQtty = 0, 0
				if item[i].name ~= _U('delivery') then
					itemQtty = xPlayer.getInventoryItem(item[i].db_name).count
				end
				if item[1].requires ~= "nothing" then
					requiredItemQtty = xPlayer.getInventoryItem(item[1].requires).count
				end
				if item[i].name ~= _U('delivery') and itemQtty >= item[i].max then	
				TriggerClientEvent('esx:showNotification', source, _U('max_limit', item[i].name))
                PlayersWorking[source] = "no"	
				elseif item[i].requires ~= "nothing" and requiredItemQtty <= 0 then
				TriggerClientEvent('esx:showNotification', source, _U('not_enough', item[1].requires_name))
				PlayersWorking[source] = false
				else
					if item[i].name ~= _U('delivery') then
						-- Chances to drop the item
						if item[i].drop == 100 and PlayersWorking[source] ~= "no" then
							xPlayer.addInventoryItem(item[i].db_name, item[i].add)
						else
							local chanceToDrop = math.random(100)
							if chanceToDrop <= item[i].drop and PlayersWorking[source] ~= "no" then
								xPlayer.addInventoryItem(item[i].db_name, item[i].add)
							end
						end
					else
					if Config.miner and xPlayer.job.name == 'miner' then 
                        xPlayer.addMoney(item[i].price*2)
					elseif Config.lumberjack and xPlayer.job.name == 'lumberjack' then 
                        xPlayer.addMoney(item[i].price*2)	
					elseif Config.slaughterer and xPlayer.job.name == 'slaughterer' then 
                        xPlayer.addMoney(item[i].price*2)
					elseif Config.tailor and xPlayer.job.name == 'tailor' then 
                        xPlayer.addMoney(item[i].price*2)
					elseif Config.milker and xPlayer.job.name == 'milker' then 
                        xPlayer.addMoney(item[i].price*2)
					elseif Config.fueler and xPlayer.job.name == 'fueler' then 
                        xPlayer.addMoney(item[i].price*2)
					elseif Config.vegetables and xPlayer.job.name == 'vegetables' then 
                        xPlayer.addMoney(item[i].price*2)
					elseif Config.farmer and xPlayer.job.name == 'farmer' then 
                        xPlayer.addMoney(item[i].price*1.8)
                TriggerClientEvent('pNotify:SendNotification',source, {
					text = "<font size=5><center><b>انت كسبت: <font size=5 color=00EE4F>$<font color=white>" .. item[i].price*1.8,
					type = "alert",
					queue = left,
					timeout = 1000,
					killer = false,
					theme = "gta",
					layout = "centerLeft" 
				})
                Citizen.Wait(500)
                TriggerClientEvent('pNotify:SendNotification',source, {
					text = "<font size=5 color=gray><center><b>نسبة الشركة: <font size=5 color=FFAE00>$<font color=white>" .. item[i].price*0.2,
					type = "alert",
					queue = left,
					timeout = 1000,
					killer = false,
					theme = "gta",
					layout = "centerLeft" 
				})						
					elseif xPlayer.job.name == 'farmer' and Config.double ~= 6 or Config.farmer == 1 then 
                 xPlayer.addMoney(item[i].price*0.9)	
                TriggerClientEvent('pNotify:SendNotification',source, {
					text = "<font size=5><center><b>انت كسبت: <font size=5 color=00EE4F>$<font color=white>" .. item[i].price*0.9,
					type = "alert",
					queue = left,
					timeout = 1000,
					killer = false,
					theme = "gta",
					layout = "centerLeft" 
				})
                Citizen.Wait(500)
                TriggerClientEvent('pNotify:SendNotification',source, {
					text = "<font size=5 color=gray><center><b>نسبة الشركة: <font size=5 color=FFAE00>$<font color=white>" .. item[i].price*0.1,
					type = "alert",
					queue = left,
					timeout = 1000,
					killer = false,
					theme = "gta",
					layout = "centerLeft" 
				})				
					elseif Config.fisherman and xPlayer.job.name == 'fisherman' then
						if item[i].blackMoney then 
                        	xPlayer.addAccountMoney('black_money',item[i].price*2)			
						else
							xPlayer.addMoney(item[i].price*2)
						end			
					else
						if item[i].blackMoney then 
							xPlayer.addAccountMoney('black_money',item[i].price)
						else
							xPlayer.addMoney(item[i].price)						
						end
                    end
						--TriggerEvent('esx_xp:addXP', source , item[i].xp)	
						local mejob = xPlayer.job.label
						TriggerEvent(Config.XpScript..':updateCurrentPlayerXP', source, 'add', item[i].xp, ' بيع منتج في وظيفة '..mejob)
						sendToDiscord((' الوظايف '), GetPlayerName(source) .. " باع  ".. item[1].requires .. " مقابل ".. item[i].price.."دولار",56108)
					end
				end
			end

			if item[1].requires ~= "nothing" then
				local itemToRemoveQtty = xPlayer.getInventoryItem(item[1].requires).count
				if itemToRemoveQtty > 0 then
					xPlayer.removeInventoryItem(item[1].requires, item[1].remove)

				end
			end

			Work(source, item)

		end
	end)
end

RegisterServerEvent('devesx_milkerjob:A_start_2KNK2_Work') -- startWork
AddEventHandler('devesx_milkerjob:A_start_2KNK2_Work', function(item, pas)
    if pas == Config.pas then
	if not PlayersWorking[source] then
		PlayersWorking[source] = true
		Work(source, item)
	else
		print(('devesx_milkerjob: %s attempted to exploit the marker!'):format(GetPlayerIdentifiers(source)[1]))
	end
	end
end)

RegisterServerEvent('devesx_milkerjob:stopWork')
AddEventHandler('devesx_milkerjob:stopWork', function()
	PlayersWorking[source] = false
end)

function RemoveOwnedVehicle(plate)
	MySQL.Async.execute('DELETE FROM owned_vehicles WHERE plate = @plate', {
		['@plate'] = plate
	})
end

RegisterServerEvent('devesx_milkerjob:A_K2dRcaution_v63j') -- devesx_milkerjob:caution
AddEventHandler('devesx_milkerjob:A_K2dRcaution_v63j', function(pas, cautionType, cautionAmount, spawnPoint, vehicle, plate)
    if pas == Config.pas then
	local xPlayer = ESX.GetPlayerFromId(source)
	if cautionType == "take" then
		TriggerEvent('esx_addonaccount:getAccount', 'caution', xPlayer.identifier, function(account)
			xPlayer.removeAccountMoney('bank', cautionAmount)
			account.addMoney(cautionAmount)
		end)

	--	TriggerClientEvent('esx:showNotification', source, _U('bank_deposit_taken', ESX.Math.GroupDigits(cautionAmount)))
                TriggerClientEvent('pNotify:SendNotification',source, {
                    text = _U('bank_deposit_taken'), 
                    type = "info", 
                    timeout = 10000, 
                    layout = "centerLeft"
                })		
		local plate = 'WRK ' .. math.random(1000, 9999)
		TriggerClientEvent('devesx_milkerjob:spawnJobVehicle', source, spawnPoint, vehicle,plate)
		TriggerEvent('esx:addnewVeh',source,plate)
	elseif cautionType == "give_back" then

		if cautionAmount > 1 then
			print(('devesx_milkerjob: %s is using cheat engine!'):format(xPlayer.identifier))
			return
		end

		TriggerEvent('esx_addonaccount:getAccount', 'caution', xPlayer.identifier, function(account)
			local caution = account.money
			local toGive = ESX.Math.Round(caution * cautionAmount)

			xPlayer.addAccountMoney('bank', toGive)
			RemoveOwnedVehicle(plate)
			account.removeMoney(toGive)
                TriggerClientEvent('pNotify:SendNotification',source, {
                    text = _U('bank_deposit_returned'), 
                    type = "info", 
                    timeout = 10000, 
                    layout = "centerLeft"
                })					
			--TriggerClientEvent('esx:showNotification', source, _U('bank_deposit_returned', ESX.Math.GroupDigits(toGive)))
		end)
	end
	end
end)

function GetCharacterName(source)
	local xPlayer = ESX.GetPlayerFromId(source)
	
	if xPlayer then
		if true then
			return xPlayer.getName()
		end
	else
		return GetPlayerName(source)
	end
end

function doublemoney_milker(name, steamIdentifiers, discordIdentifiers, ingamename)
	if not Config.milker then
		Config.milker = true
		TriggerClientEvent("esx_misc:watermark_promotion", -1, 'milker', true)
		TriggerClientEvent("SalvzTurki:client:setmoneymultiple_cl_kjad37aj", -1, 'milker', true)
		TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"بدأ ضعف أجر الحليب", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",********)
	else
		Config.milker = false
		TriggerClientEvent("esx_misc:watermark_promotion", -1, 'milker', false)
		TriggerClientEvent("SalvzTurki:client:setmoneymultiple_cl_kjad37aj", -1, 'milker', false)
		TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"إنتهاء ضعف أجر الحليب", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",15158332)
	end
end

RegisterServerEvent("devesx_milkerjob:togglePromotion_duble") -- ضعف الأجر
AddEventHandler("devesx_milkerjob:togglePromotion_duble", function(job)
    local xPlayer = ESX.GetPlayerFromId(source)
    local name = GetPlayerName(source)
    local steamIdentifiers = GetPlayerIdentifiers(source)[1]
    local discordIdentifiers = GetPlayerIdentifiers(source)[5]
    local ingamename = xPlayer.getName()
	if xPlayer.job.name == 'admin' and xPlayer.getGroup() == "superadmin" or xPlayer.getGroup() == "admin" or xPlayer.getGroup() == "aplus" or xPlayer.getGroup() == "a" or xPlayer.getGroup() == "modplus" then
		if job == 'milker' then
			doublemoney_milker(name, steamIdentifiers, discordIdentifiers, ingamename)
		end
  	else
  		print(('devesx_milkerjob: %s attempted to toggle Promotion (not admin!)!'):format(xPlayer.identifier))
  	end
end)

RegisterServerEvent('esx_misc:GetCache')
AddEventHandler('esx_misc:GetCache', function()
	TriggerClientEvent("esx_misc:updatePromotionStatus", source, 'milker', Config.milker)
end)