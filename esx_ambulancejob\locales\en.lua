Locales['en'] = {
  -- Cloakroom
  --[[
  ['cloakroom'] = 'غرفة تبديل الملابس',
  ['ems_clothes_civil'] = 'تسجيل خروج',
  ['ems_clothes_ems'] = 'تسجيل دخول',
]]-- Cloakroom main and new
  ['cloakroom'] = ':::: غرفة تبديل ::::',
  ['ems_clothes_civil'] = '<span  style="color:#FF0E0E">::: تسجيل خروج :::</span>',
  ['ems_clothes_ems'] = '<span  style="color:#00EE4F">تسجيل دخول</span>',
  ['ems_0'] = '<span  style="color:#00EE4F">مسعف 1</span>',
  ['ems_1'] = '<span  style="color:#00EE4F">مسعف 2</span>',
  ['ems_2'] = '<span  style="color:#00EE4F">مسعف 3</span>',
  ['ems_3'] = '<span  style="color:#00EE4F">مسعف 4</span>',
  ['ems_4'] = '<span  style="color:#00EE4F">مسعف 5</span>',
  ['ems_5'] = '<span  style="color:#00EE4F">مسعف 6</span>',
  ['fire_0'] = '<span  style="color:#FF0E0E">إطفاء 1</span>',
  
  ['Accessories'] = '<span  style="color:orange">اكسسوارات</span>',
  ['Accessories_unavailable'] = '<span  style="color:gray">اكسسوارات غير متاحة للمتدرب</span>',
  ['badge'] = 'كرنيه مسعف',
  ['sma3a'] = 'سماعة طبيب',
  ['radio_belt'] = 'حزام راديو',
  ['radio_belt2'] = 'حزام راديو على الكتف',
  ['mask2'] = 'كمام',
  ['bag_cbr'] = 'حقيبة',
  
  ['chain_remove'] = 'إزالة اكسسوار العنق',
  ['mask_remove'] = 'إزالة قناع',
  ['bag_remove'] = 'إزالة الحقيبة',
  ['belt_remove'] = 'إزالة الحزام',
  
  ['EMSCloakroomMenu'] = '<span  style="color:green">الإٍسعاف</span>',
  ['FireCloakroomMenu'] = '<span  style="color:#970000">الإطفاء</span>',
  
  ['service_in'] = '<span  style="color:green">تسجيل دخول في الهلال الاحمر</span>',
  ['service_out'] = '<span  style="color:red">تسجيل خروج من الهلال الاحمر</span>',
  -- Vehicles
  ['ambulance'] = 'الهلال الاحمر',
  ['helicopter_prompt'] = '<FONT FACE="sharlock">ﺕﺍﺮﺋﺎﻄﻟﺍ ﺔﻤﺋﺎﻗ ﺢﺘﻔﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['garage_prompt'] = '<FONT FACE="sharlock">ﺕﺎﺒﻛﺮﻤﻟﺍ ﺔﻤﺋﺎﻗ ﺢﺘﻔﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['garage_title'] = 'كراج | الهلال الاحمر',
  ['garage_stored'] = 'في الكراج',
  ['garage_notstored'] = 'ليست في الكراج',
  ['garage_storing'] = 'ﻞﻴﻗﺍﺮﻋ ﺩﻮﺟﻭ ﻡﺪﻋ ﻦﻣ ﺪﻛﺄﺗ',
  ['garage_has_stored'] = 'ﺏﺃﺮﻤﻟﺍ ﻲﻓ ﺔﺒﻛﺮﻤﻟﺍ ﻦﻛﺭ ﻢﺗ',
  ['garage_has_notstored'] = 'ﺏﺮﻘﻟﺎﺑ ﺎﻬﻜﻠﺘﻤﺗ ﺔﺒﻛﺮﻣ ﺪﺟﻮﺗﻻ',
  ['garage_notavailable'] = 'ﺝﺍﺮﻜﻟﺍ ﻲﻓ ﺖﺴﻴﻟ ﻚﺘﺒﻛﺮﻣ',
  ['garage_blocked'] = 'ﺔﻴﻓﺎﻛ ﻦﻛﺭ ﻁﺎﻘﻧ ﺪﺟﻮﺗﻻ',
  ['garage_empty'] = 'ﺝﺍﺮﻜﻟﺍ ﻲﻓ ﺔﺒﻛﺮﻣ ﻱﺍ ﻚﻠﺘﻤﺗﻻ',
  ['garage_released'] = 'ﺔﺒﻛﺮﻤﻟﺍ ﺝﺍﺮﺧﺇ ﻢﺗ',
  ['garage_store_nearby'] = 'ﻚﻨﻣ ﺏﺮﻘﻟﺎﺑ ﺕﺎﺒﻛﺮﻣ ﺪﺟﻮﺗﻻ',
  ['garage_storeditem'] = 'مركباتي',
  ['garage_storeitem'] = 'تخزين المركبة',
  ['garage_buyitem'] = 'محل المركبات',
  ['shop_item'] = '$%s',
  ['vehicleshop_title'] = 'محل المركبات',
  ['vehicleshop_confirm'] = 'هل تود شراء المركبة',
  ['vehicleshop_bought'] = '$ ~y~%s~s~ ﻞﺑﺎﻘﻣ ~r~%s~s~ ﺔﺒﻛﺮﻤﻟﺍ ﺖﻳﺮﺘﺷﺇ ﺪﻘﻟ',
  ['vehicleshop_money'] = 'ﺔﺒﻛﺮﻤﻠﻟ ﻲﻓﺎﻜﻟﺍ ﻝﺎﻤﻟﺍ ﻚﻠﻤﺗﻻ',
  ['vehicleshop_awaiting_model'] = 'the vehicle is currently ~g~DOWNLOADING & LOADING~s~ please wait',
  ['confirm_no'] = 'لا',
  ['confirm_yes'] = 'تأكيد',
  -- Action Menu
  ['revive_inprogress'] = 'جاري الإنعاش',
  ['revive_complete'] = 'لقد أنعشت %s',
  ['revive_complete_award'] = 'تم انعاش'..' %s'..'وحصلت على مكافأة'..' $%s!',
  ['revive_fail_offline'] = 'الشخص خارج الخدمة',
  ['heal_inprogress'] = 'جاري المعالجة',
  ['heal_complete'] = 'لقد عالجت %s',
  ['no_players'] = 'لايوجد لاعب بالقرب',
  ['player_not_unconscious'] = 'الشخص ليس فاقد الوعي',
  ['player_not_conscious'] = 'الشخص فاقد الوعي',
  ['remove_prop'] = '~r~G~w~ ﻑﺎﻌﺳﻷﺍ ﺮﻳﺮﺳ ﻑﺬﺣ',
  -- Boss Menu
  ['boss_actions'] = 'قائمة إدارة الوظيفة',
  -- Misc
  ['invalid_amount'] = '~r~Invalid amount',
  ['actions_prompt'] = '<FONT FACE="sharlock">ﺔﻴﺼﺨﺸﻟﺍ ﺔﻤﺋﺎﻘﻠﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['deposit_amount'] = 'deposit Amount',
  ['money_withdraw'] = 'amount withdrawn',
  ['fast_travel'] = '~g~E~w~ ﻝﺎﻘﺘﻧﺃ',
  ['fast_travel_gotogarage'] = '~g~E~w~ ﻰﻔﺸﺘﺴﻤﻠﻟ ﻉﻮﻠﻄﻟﺍ',
  ['fast_travel_gotopillpox'] = '~g~E~w~ ﺝﺍﺮﻜﻠﻟ ﻝﻭﺰﻨﻟﺍ',
  ['open_pharmacy'] = '<FONT FACE="sharlock">ﺔﻴﻟﺪﻴﺼﻟﺍ ﺢﺘﻔﻟ ~y~E~w~ ﻂﻐﺿﺍ',
  ['pharmacy_menu_title'] = 'الصيدلية',
  ['pharmacy_take'] = '<span style="color:white;">%s</span> أخذ',
  ['medikit'] = 'حقيبة إسعافات أولية',
  ['bandage'] = 'ضمادات',
  ['max_item'] = 'حقيبتك ممتلئة',
  ['service_not'] = '<font color=red>انت غير مسجل دخول بالخدمة<font color=white>, سجل دخول في الخدمة وحاول مرة اخرى.',
  -- F6 Menu
  ['ems_menu'] = 'قائمة الإسعاف',
  ['ems_bed_menu'] = 'سرير الاسعاف',
  ['ems_menu_title'] = '<font color=#FF0000>قائمة الإسعاف</font>',
  ['ems_menu_revive'] = '<font color=#33FF33>إنعاش لاعب</font>',
  ['ems_menu_putincar'] = '<font color=#FF9999>وضع في المركبة</font>',
  ['ems_menu_small'] = '<font color=#FFFF33>علاج أصابات خفيفة</font>',
  ['ems_menu_big'] = 'علاج أصابات خطيرة ',
  ['ems_menu_search'] = 'لايوجد مريض',
  -- Phone
  ['alert_ambulance'] = 'تطبيق الطوارئ الطبية',
  -- Death
  ['respawn_available_in'] = '~b~%s : ~b~%s ~w~ﺪﻌﺑ ﺡﺎﺘﻣ ﻰﻔﺸﺘﺴﻤﻠﻟ ﻝﺎﻘﺘﻧﺍ',
  ['respawn_bleedout_in'] = '~r~%s : ~r~ %sﺕﻮﻤﻟﺍ ﻰﺘﺣ ﻑﺰﻨﺗ ﺖﻧﺍ',
  ['respawn_bleedout_prompt'] = '~g~ ﻰﻔﺸﺘﺴﻤﻠﻟ ﻝﺎﻘﺘﻧﻼﻟ E ﻂﻐﺿ ﺖﺒﺛ',
  --['respawn_bleedout_prompt'] = 'Hold ~y~E to respawn',
  ['respawn_bleedout_fine'] = '~w~ %s ﻞﺑﺎﻘﻣ ﻰﻔﺸﺘﺴﻣ ﻝﺎﻘﺘﻧﺍ ~b~E~s~ ﻰﻠﻋ ﺮﻤﺘﺳﺇ',
  ['respawn_bleedout_fine_msg'] = 'تم دفع $ %s رسوم نقلك للمستشفى',
  ['distress_send'] = 'ﺔﻴﺒﻄﻟﺍ ﺉﺭﺍﻮﻄﻟﺍ ﺐﻠﻄﻟ ~r~G~s~ ﻂﻐﺿﺍ',
  ['police_send_shash'] = 'ﺔﻴﺒﻄﻟﺍ ﺉﺭﺍﻮﻄﻟﺍ ﻩﺪﺣﻭ ﺐﻠﻄﻟ~r~G~s~ ﻂﻐﺿﺍ',
  ['agent_send_shash'] = 'ﺔﻴﻨﻣﻷﺍ تﺎﺳﺍﺮﺤﻠﻟ ﻮﻜﻨﻣﺃ ﺔﻛﺮﺷ ﻦﻣ شﺎﻌﻧﺃ ةﺪﺣﻭ ﺐﻠﻄﻟ ~r~G~s~ ﻂﻐﺿﺍ',
  ['distress_sent'] = 'تم ارسال بلاغ <font color=#FF0E0E>نزيف حتى الموت <font color=#FFAE00>لوحدة الطوارئ الطبية في غرفة العمليات',
  ['distress_message'] = 'حالة طوارئ... يوجد شخص بحالة اغماء او ينزف حتى الموت بحاجة إلى إسعاف فورا',
  ['combatlog_message'] = 'تم نقلك للمستشفى بسبب فصلك من المحافظة وأنت في حالة نزيف حتى الموت',
  ['down_alert'] = 'بلاغ شخص مسقط',
  -- Revive
  ['revive_help'] = 'إنعاش لاعب',
  -- Item
  ['used_medikit'] = 'لقد استخدمت اسعافات اولية',
  ['used_bandage'] = 'ﺓﺩﺎﻤﺿ ﺖﻣﺪﺨﺘﺳﺇ ﺪﻘﻟ',
  ['not_enough_medikit'] = 'لاتمتلك حقيبة اسعافات أولية',
  ['not_enough_bandage'] = 'ﺓﺩﺎﻤﺿ ﻚﻠﺘﻤﺗﻻ',
  ['healed'] = 'ﻚﺘﺠﻟﺎﻌﻣ ﻢﺗ ﺪﻘﻟ',
  -- Blips
  ['blip_hospital'] = '<FONT FACE="sharlock">ﻰﻔﺸﺘﺴﻤﻟﺍ',
  ['blip_dead'] = '<FONT FACE="sharlock">ﻲﻋﻮﻟﺍ ﺪﻗﺎﻓ ﺺﺨﺷ',
}
