if not ESX then

	ESX = nil

	Citizen.CreateThread(function()

		while ESX == nil do

			TriggerEvent(m3gon_or_defcon_config.shared, function(obj) ESX = obj end)

			Citizen.Wait(1000)

		end

		while ESX.GetPlayerData().job == nil do

			Citizen.Wait(1000)

		end

		ESX.PlayerData = ESX.GetPlayerData()

	end)

else

	Citizen.CreateThread(function()

		while ESX.GetPlayerData().job == nil do

			Citizen.Wait(1000)

		end

		ESX.PlayerData = ESX.GetPlayerData()

	end)

end

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)

	ESX.PlayerData.job = job

end)

Citizen.CreateThread(function()

	while true do

		local ped = PlayerPedId()

		if IsPedArmed(ped, 4) then
			
			local _,pedWeapon = GetCurrentPedWeapon(ped, true)

			if pedWeapon == 487013001 then

				for k,v in ipairs(ESX.PlayerData.loadout) do

					if v.name == "WEAPON_PUMPSHOTGUN_MK2" and ESX.PlayerData.job.name ~= "admin" and ESX.PlayerData.job.name ~= "police" and ESX.PlayerData.job.name ~= "agent" and ESX.PlayerData.job.name ~= "mgahden" then

						SetCurrentPedWeapon(ped, GetHashKey("WEAPON_UNARMED"), true)
			
						ESX.ShowNotification("لايمكنك استخدام <font color=FF0000>شوزن عسكري</font>")
										
					end

				end

			end

		end

		Citizen.Wait(1000)
		
	end

end)