-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Sep 06, 2024 at 11:52 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `RT_v3`
--

-- --------------------------------------------------------

--
-- Table structure for table `addon_account`
--

CREATE TABLE `addon_account` (
  `name` varchar(60) NOT NULL,
  `label` varchar(100) NOT NULL,
  `shared` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `addon_account`
--

INSERT INTO `addon_account` (`name`, `label`, `shared`) VALUES
('caution', 'Caution', 0),
('gang_1', 'Gang_1', 1),
('gang_2', 'Gang_2', 1),
('property_black_money', 'خزنة أموال قذرة للعقار', 0),
('society_admin', 'خزنة الرقابة و التفتيش', 1),
('society_agent', 'خزنة الرائد للحراسة', 1),
('society_ambulance', 'خزنة الطوارئ الطبية', 1),
('society_cardealer', 'خزنة معرض ميلانو', 1),
('society_gotur', 'Götür', 1),
('society_mechanic', 'خزنة كراج الميكانيك', 1),
('society_police', 'خزنة إدارة الشرطة', 1),
('society_taxi', 'خزنة شركة التاكسي', 1);

-- --------------------------------------------------------

--
-- Table structure for table `addon_account_data`
--

CREATE TABLE `addon_account_data` (
  `id` int(11) NOT NULL,
  `account_name` varchar(100) DEFAULT NULL,
  `money` int(11) NOT NULL,
  `owner` varchar(40) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `addon_account_data`
--

INSERT INTO `addon_account_data` (`id`, `account_name`, `money`, `owner`) VALUES
(599, 'society_admin', 245000, NULL),
(600, 'society_agent', 0, NULL),
(601, 'society_ambulance', 0, NULL),
(602, 'society_cardealer', 0, NULL),
(603, 'society_gotur', 0, NULL),
(604, 'society_mechanic', 0, NULL),
(605, 'society_police', 0, NULL),
(606, 'society_taxi', 0, NULL),
(607, 'property_black_money', 0, '241c0b24a5a116ef2f535cf37d83e17675b2ff15'),
(608, 'caution', 0, '241c0b24a5a116ef2f535cf37d83e17675b2ff15'),
(609, 'caution', 0, '64bf93f4ae55cf7b5cd8f42e413d535088fa5196'),
(610, 'property_black_money', 0, '64bf93f4ae55cf7b5cd8f42e413d535088fa5196'),
(611, 'property_black_money', 0, '4b5f3d01b6520c7f3ce37700ca11b06789073460'),
(612, 'caution', 0, '4b5f3d01b6520c7f3ce37700ca11b06789073460'),
(613, 'caution', 0, '370af5c1f03b439803d21d19b79427f9707f46ee'),
(614, 'property_black_money', 0, '370af5c1f03b439803d21d19b79427f9707f46ee'),
(615, 'property_black_money', 0, '9eef030b5f84df9eefcd3154fdac29df4fa63eb7'),
(616, 'caution', 0, '9eef030b5f84df9eefcd3154fdac29df4fa63eb7'),
(617, 'caution', 0, '83e2aa1acb1704d4c62ce7ff82d05a429e2cecff'),
(618, 'property_black_money', 0, '83e2aa1acb1704d4c62ce7ff82d05a429e2cecff'),
(619, 'property_black_money', 0, '5f664dbb1d3ed719fe2f587b0dfa1b62cf9c0be6'),
(620, 'caution', 0, '5f664dbb1d3ed719fe2f587b0dfa1b62cf9c0be6'),
(621, 'property_black_money', 0, '88f87e1ede7d8340d70efe9a3b6f48939687a4bf'),
(622, 'caution', 0, '88f87e1ede7d8340d70efe9a3b6f48939687a4bf'),
(623, 'caution', 0, '14a3a51bb7d566d4e53a43da49700c5e98692adc'),
(624, 'property_black_money', 0, '14a3a51bb7d566d4e53a43da49700c5e98692adc'),
(625, 'gang_1', 100000, NULL),
(626, 'gang_2', 0, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `addon_inventory`
--

CREATE TABLE `addon_inventory` (
  `name` varchar(60) NOT NULL,
  `label` varchar(100) NOT NULL,
  `shared` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `addon_inventory`
--

INSERT INTO `addon_inventory` (`name`, `label`, `shared`) VALUES
('gang_1', 'Gang_1', 1),
('gang_2', 'Gang_2', 1),
('housing', 'Housing', 0),
('property', 'Propriété', 0),
('society_admin', 'Admin', 1),
('society_agent', 'Agent', 1),
('society_ambulance', 'Ambulance', 1),
('society_cardealer', 'Concesionnaire', 1),
('society_gotur', 'Götür', 1),
('society_mechanic', 'Mécano', 1),
('society_police', 'Police', 1),
('society_taxi', 'Taxi', 1);

-- --------------------------------------------------------

--
-- Table structure for table `addon_inventory_items`
--

CREATE TABLE `addon_inventory_items` (
  `id` int(11) NOT NULL,
  `inventory_name` varchar(100) NOT NULL,
  `name` varchar(100) NOT NULL,
  `count` int(11) NOT NULL,
  `owner` varchar(40) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `aircrafts`
--

CREATE TABLE `aircrafts` (
  `name` varchar(60) NOT NULL,
  `model` varchar(60) NOT NULL,
  `price` int(11) NOT NULL,
  `category` varchar(60) DEFAULT NULL,
  `level` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- Table structure for table `aircrafts_categories`
--

CREATE TABLE `aircrafts_categories` (
  `name` varchar(60) NOT NULL,
  `label` varchar(60) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci ROW_FORMAT=DYNAMIC;

--
-- Dumping data for table `aircrafts_categories`
--

INSERT INTO `aircrafts_categories` (`name`, `label`) VALUES
('bigplane', 'طائرة كبيرة'),
('chi', 'طائرة شراعية'),
('heli', 'هيليكوبتر');

-- --------------------------------------------------------

--
-- Table structure for table `aircrafts_for_sale`
--

CREATE TABLE `aircrafts_for_sale` (
  `id` int(11) NOT NULL,
  `seller` varchar(50) NOT NULL,
  `vehicleProps` longtext NOT NULL,
  `price` int(11) NOT NULL DEFAULT 0,
  `info` text NOT NULL DEFAULT 'لاشيء',
  `name` longtext NOT NULL,
  `priceold` int(11) NOT NULL DEFAULT 0,
  `levelold` int(11) NOT NULL DEFAULT 0,
  `category` longtext NOT NULL,
  `modelname` longtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- Table structure for table `antharat_admin`
--

CREATE TABLE `antharat_admin` (
  `id` int(11) NOT NULL,
  `identifier` varchar(40) NOT NULL,
  `sender` varchar(40) NOT NULL,
  `target_type` varchar(50) NOT NULL,
  `target` varchar(40) NOT NULL,
  `label` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `antharat_admin`
--

INSERT INTO `antharat_admin` (`id`, `identifier`, `sender`, `target_type`, `target`, `label`) VALUES
(0, '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', 'society', 'society_admin', '1'),
(0, '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', 'society', 'society_admin', 'nil'),
(0, '9eef030b5f84df9eefcd3154fdac29df4fa63eb7', '9eef030b5f84df9eefcd3154fdac29df4fa63eb7', 'society', 'society_admin', '10'),
(0, '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', '370af5c1f03b439803d21d19b79427f9707f46ee', 'society', 'society_admin', 'ب'),
(0, '14a3a51bb7d566d4e53a43da49700c5e98692adc', '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'society', 'society_admin', 'توجه الدعم'),
(0, '14a3a51bb7d566d4e53a43da49700c5e98692adc', '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'society', 'society_admin', 'يش');

-- --------------------------------------------------------

--
-- Table structure for table `bank_transfer`
--

CREATE TABLE `bank_transfer` (
  `id` int(11) NOT NULL,
  `type` int(11) NOT NULL,
  `identifier` varchar(50) NOT NULL,
  `price` varchar(50) NOT NULL,
  `name` varchar(50) NOT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `bans`
--

CREATE TABLE `bans` (
  `license` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `time` int(20) NOT NULL,
  `reason` varchar(255) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `billing`
--

CREATE TABLE `billing` (
  `id` int(11) NOT NULL,
  `identifier` varchar(40) NOT NULL,
  `sender` varchar(40) NOT NULL,
  `target_type` varchar(50) NOT NULL,
  `target` varchar(40) NOT NULL,
  `label` varchar(255) NOT NULL,
  `amount` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `billing`
--

INSERT INTO `billing` (`id`, `identifier`, `sender`, `target_type`, `target`, `label`, `amount`) VALUES
(673, '88f87e1ede7d8340d70efe9a3b6f48939687a4bf', '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', 'society', 'society_agent', 'غرامة: حيازة ممنوعات / 10 شهر', 5000);

-- --------------------------------------------------------

--
-- Table structure for table `billinglog`
--

CREATE TABLE `billinglog` (
  `id` int(11) NOT NULL,
  `identifier` varchar(40) NOT NULL,
  `sender` varchar(40) NOT NULL,
  `target_type` varchar(50) NOT NULL,
  `target` varchar(40) NOT NULL,
  `label` varchar(255) NOT NULL,
  `amount` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `billinglog`
--

INSERT INTO `billinglog` (`id`, `identifier`, `sender`, `target_type`, `target`, `label`, `amount`) VALUES
(0, '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', 'society', 'society_admin', 'غرامة: الاعتداء على كراج التعديل والتزويد - المره الثالثة', 35000),
(0, '370af5c1f03b439803d21d19b79427f9707f46ee', '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', 'society', 'society_admin', 'غرامة: الاعتداء على كراج التعديل والتزويد - المره الثالثة', 35000),
(0, '14a3a51bb7d566d4e53a43da49700c5e98692adc', '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'society', 'society_admin', 'غرامة: قيادة غير واقعية - المره الاولى', 10000),
(0, '14a3a51bb7d566d4e53a43da49700c5e98692adc', '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'society', 'society_admin', 'يصش', 200000);

-- --------------------------------------------------------

--
-- Table structure for table `boats`
--

CREATE TABLE `boats` (
  `name` varchar(60) NOT NULL,
  `model` varchar(60) NOT NULL,
  `price` int(11) NOT NULL,
  `category` varchar(60) DEFAULT NULL,
  `level` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci ROW_FORMAT=DYNAMIC;

--
-- Dumping data for table `boats`
--

INSERT INTO `boats` (`name`, `model`, `price`, `category`, `level`) VALUES
('قارب دينقي', 'dinghy', 275000, '4seat', 25),
('قارب دينقي', 'dinghy2', 175000, '2seat', 2),
('دباب بحر', 'seashark', 115000, '2seat', 2),
('دباب بحر', 'seashark2', 90000, '2seat', 5),
('غواصة', 'submersible', 1050000, 'ghawasa', 70),
('غواصة كراكن', 'submersible2', 850000, 'ghawasa', 65);

-- --------------------------------------------------------

--
-- Table structure for table `boats_categories`
--

CREATE TABLE `boats_categories` (
  `name` varchar(60) NOT NULL,
  `label` varchar(60) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci ROW_FORMAT=DYNAMIC;

--
-- Dumping data for table `boats_categories`
--

INSERT INTO `boats_categories` (`name`, `label`) VALUES
('2seat', '2 ركاب'),
('4seat', '4 ركاب'),
('ghawasa', 'غواصات');

-- --------------------------------------------------------

--
-- Table structure for table `boats_for_sale`
--

CREATE TABLE `boats_for_sale` (
  `id` int(11) NOT NULL,
  `seller` varchar(50) NOT NULL,
  `vehicleProps` longtext NOT NULL,
  `price` int(11) NOT NULL DEFAULT 0,
  `info` text NOT NULL DEFAULT 'لاشيء',
  `name` longtext NOT NULL,
  `priceold` int(11) NOT NULL DEFAULT 0,
  `levelold` int(11) NOT NULL DEFAULT 0,
  `category` longtext NOT NULL,
  `modelname` longtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- Table structure for table `bought_houses`
--

CREATE TABLE `bought_houses` (
  `houseid` int(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `bought_houses`
--

INSERT INTO `bought_houses` (`houseid`) VALUES
(10),
(27),
(123),
(169),
(216),
(250);

-- --------------------------------------------------------

--
-- Table structure for table `businesses`
--

CREATE TABLE `businesses` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `address` varchar(255) NOT NULL,
  `description` varchar(75) NOT NULL,
  `blipname` varchar(75) DEFAULT NULL,
  `owner` varchar(255) DEFAULT NULL,
  `price` int(11) NOT NULL,
  `earnings` int(11) NOT NULL,
  `position` text NOT NULL,
  `stock` int(11) NOT NULL DEFAULT 0,
  `stock_price` int(11) NOT NULL DEFAULT 100,
  `employees` text NOT NULL,
  `taxrate` float DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `bwh_bans`
--

CREATE TABLE `bwh_bans` (
  `id` int(11) NOT NULL,
  `receiver` text NOT NULL,
  `sender` varchar(60) NOT NULL,
  `length` datetime DEFAULT NULL,
  `reason` text NOT NULL,
  `unbanned` tinyint(4) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `bwh_identifiers`
--

CREATE TABLE `bwh_identifiers` (
  `steam` varchar(60) NOT NULL,
  `license` varchar(60) NOT NULL,
  `ip` varchar(60) NOT NULL,
  `name` varchar(128) NOT NULL,
  `xbl` varchar(60) DEFAULT NULL,
  `live` varchar(60) DEFAULT NULL,
  `discord` varchar(60) DEFAULT NULL,
  `fivem` varchar(60) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `bwh_identifiers`
--

INSERT INTO `bwh_identifiers` (`steam`, `license`, `ip`, `name`, `xbl`, `live`, `discord`, `fivem`) VALUES
('steam:110000147fa5a03', '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ip:***********', 'dkstr', 'xbl:2535442617083942', 'live:1055519115231005', 'discord:890604148419100764', 'fivem:5218251'),
('steam:11000015b13d4b2', '370af5c1f03b439803d21d19b79427f9707f46ee', 'ip:*************', 'Adjusted Playername', NULL, NULL, 'discord:1080426615449854023', NULL),
('steam:11000015b6d9376', '4b5f3d01b6520c7f3ce37700ca11b06789073460', 'ip:**************', 'B A', 'xbl:2535444203099711', 'live:844426035447478', 'discord:1025728017017872395', 'fivem:9808534'),
('steam:110000164f8409e', '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', 'ip:**************', 'SSSSSS', NULL, 'live:1055520292353227', 'discord:877717221659512872', 'fivem:13608827'),
('steam:11000016a3f0291', '88f87e1ede7d8340d70efe9a3b6f48939687a4bf', 'ip:*************', 'saifplus400', NULL, NULL, 'discord:947531711221223434', 'fivem:13971624');

-- --------------------------------------------------------

--
-- Table structure for table `bwh_warnings`
--

CREATE TABLE `bwh_warnings` (
  `id` int(11) NOT NULL,
  `receiver` text NOT NULL,
  `sender` varchar(60) NOT NULL,
  `message` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cardealer_vehicles`
--

CREATE TABLE `cardealer_vehicles` (
  `id` int(11) NOT NULL,
  `vehicle` varchar(255) NOT NULL,
  `price` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `codem-hud-data`
--

CREATE TABLE `codem-hud-data` (
  `identifier` varchar(65) DEFAULT NULL,
  `data` longtext DEFAULT NULL,
  `stress` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `codem-hud-data`
--

INSERT INTO `codem-hud-data` (`identifier`, `data`, `stress`) VALUES
('241c0b24a5a116ef2f535cf37d83e17675b2ff15', '{\"maptype\":\"rectangle\",\"showHideBox\":false,\"hud\":\"text\",\"hideBoxData\":{\"stress\":100,\"health\":100,\"armor\":100,\"hunger\":100,\"stamina\":100,\"water\":100},\"speedtype\":\"kmh\",\"refreshRate\":200,\"showCompass\":true,\"hide\":false,\"hudColors\":{\"classic\":{\"stress\":\"#AA35A6\",\"thirst\":\"#2F549C\",\"armor\":\"#2E3893\",\"hunger\":\"#B3743A\",\"stamina\":\"#c4ff48\",\"health\":\"#9F2929\",\"oxy\":\"#48A7FFac\",\"altitude\":\"#48deff\",\"nitro\":\"#8eff48\",\"parachute\":\"#48ffde\"},\"text\":{\"stress\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"armor\":\"#FFFFFFac\",\"hunger\":\"#FFA048ac\",\"stamina\":\"#C4FF48ac\",\"health\":\"#FF4848ac\",\"oxy\":\"#48A7FFac\",\"altitude\":\"#00FFF0ac\",\"nitro\":\"#AFFF48ac\",\"parachute\":\"#48FFBDac\"},\"radial\":{\"stress\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"armor\":\"#FFFFFFac\",\"hunger\":\"#FFA048ac\",\"stamina\":\"#C4FF48ac\",\"health\":\"#FF4848ac\",\"oxy\":\"#48A7FFac\",\"altitude\":\"#00FFF0ac\",\"nitro\":\"#AFFF48ac\",\"parachute\":\"#48FFBDac\"}},\"speedometerSize\":0.7,\"hudSize\":0.8,\"positionsData\":[]}', 0),
('64bf93f4ae55cf7b5cd8f42e413d535088fa5196', '{\"maptype\":\"rectangle\",\"showHideBox\":false,\"hud\":\"text\",\"hideBoxData\":{\"stress\":100,\"health\":100,\"armor\":100,\"hunger\":100,\"stamina\":100,\"water\":100},\"speedtype\":\"kmh\",\"refreshRate\":200,\"showCompass\":true,\"hide\":false,\"hudColors\":{\"classic\":{\"stress\":\"#AA35A6\",\"thirst\":\"#2F549C\",\"armor\":\"#2E3893\",\"hunger\":\"#B3743A\",\"stamina\":\"#c4ff48\",\"health\":\"#9F2929\",\"oxy\":\"#48A7FFac\",\"altitude\":\"#48deff\",\"nitro\":\"#8eff48\",\"parachute\":\"#48ffde\"},\"text\":{\"stress\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"armor\":\"#FFFFFFac\",\"hunger\":\"#FFA048ac\",\"stamina\":\"#C4FF48ac\",\"health\":\"#FF4848ac\",\"oxy\":\"#48A7FFac\",\"altitude\":\"#00FFF0ac\",\"nitro\":\"#AFFF48ac\",\"parachute\":\"#48FFBDac\"},\"radial\":{\"stress\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"armor\":\"#FFFFFFac\",\"hunger\":\"#FFA048ac\",\"stamina\":\"#C4FF48ac\",\"health\":\"#FF4848ac\",\"oxy\":\"#48A7FFac\",\"altitude\":\"#00FFF0ac\",\"nitro\":\"#AFFF48ac\",\"parachute\":\"#48FFBDac\"}},\"speedometerSize\":0.7,\"hudSize\":0.8,\"positionsData\":[]}', 0),
('4b5f3d01b6520c7f3ce37700ca11b06789073460', '{\"maptype\":\"rectangle\",\"showHideBox\":false,\"hud\":\"text\",\"hideBoxData\":{\"stress\":100,\"health\":100,\"armor\":100,\"hunger\":100,\"stamina\":100,\"water\":100},\"speedtype\":\"kmh\",\"refreshRate\":200,\"showCompass\":true,\"hide\":false,\"hudColors\":{\"classic\":{\"stress\":\"#AA35A6\",\"thirst\":\"#2F549C\",\"armor\":\"#2E3893\",\"hunger\":\"#B3743A\",\"stamina\":\"#c4ff48\",\"health\":\"#9F2929\",\"oxy\":\"#48A7FFac\",\"altitude\":\"#48deff\",\"nitro\":\"#8eff48\",\"parachute\":\"#48ffde\"},\"text\":{\"stress\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"armor\":\"#FFFFFFac\",\"hunger\":\"#FFA048ac\",\"stamina\":\"#C4FF48ac\",\"health\":\"#FF4848ac\",\"oxy\":\"#48A7FFac\",\"altitude\":\"#00FFF0ac\",\"nitro\":\"#AFFF48ac\",\"parachute\":\"#48FFBDac\"},\"radial\":{\"stress\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"armor\":\"#FFFFFFac\",\"hunger\":\"#FFA048ac\",\"stamina\":\"#C4FF48ac\",\"health\":\"#FF4848ac\",\"oxy\":\"#48A7FFac\",\"altitude\":\"#00FFF0ac\",\"nitro\":\"#AFFF48ac\",\"parachute\":\"#48FFBDac\"}},\"speedometerSize\":0.7,\"hudSize\":0.8,\"positionsData\":[]}', 0),
('370af5c1f03b439803d21d19b79427f9707f46ee', '{\"hide\":false,\"hideBoxData\":{\"hunger\":100,\"health\":100,\"armor\":100,\"stamina\":100,\"water\":100,\"stress\":100},\"hud\":\"text\",\"hudSize\":0.8,\"showCompass\":true,\"speedometerSize\":0.7,\"speedtype\":\"kmh\",\"maptype\":\"rectangle\",\"positionsData\":[],\"hudColors\":{\"text\":{\"hunger\":\"#FFA048ac\",\"health\":\"#FF4848ac\",\"armor\":\"#FFFFFFac\",\"parachute\":\"#48FFBDac\",\"nitro\":\"#AFFF48ac\",\"altitude\":\"#00FFF0ac\",\"oxy\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"stress\":\"#48A7FFac\",\"stamina\":\"#C4FF48ac\"},\"classic\":{\"hunger\":\"#B3743A\",\"health\":\"#9F2929\",\"armor\":\"#2E3893\",\"parachute\":\"#48ffde\",\"nitro\":\"#8eff48\",\"altitude\":\"#48deff\",\"stamina\":\"#c4ff48\",\"thirst\":\"#2F549C\",\"stress\":\"#AA35A6\",\"oxy\":\"#48A7FFac\"},\"radial\":{\"hunger\":\"#FFA048ac\",\"health\":\"#FF4848ac\",\"armor\":\"#FFFFFFac\",\"parachute\":\"#48FFBDac\",\"nitro\":\"#AFFF48ac\",\"altitude\":\"#00FFF0ac\",\"oxy\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"stress\":\"#48A7FFac\",\"stamina\":\"#C4FF48ac\"}},\"showHideBox\":false,\"refreshRate\":200}', 0),
('9eef030b5f84df9eefcd3154fdac29df4fa63eb7', '{\"hide\":false,\"hideBoxData\":{\"hunger\":100,\"health\":100,\"armor\":100,\"stamina\":100,\"water\":100,\"stress\":100},\"hud\":\"text\",\"hudSize\":0.8,\"showCompass\":true,\"speedometerSize\":0.7,\"speedtype\":\"kmh\",\"maptype\":\"rectangle\",\"positionsData\":[],\"hudColors\":{\"text\":{\"hunger\":\"#FFA048ac\",\"health\":\"#FF4848ac\",\"armor\":\"#FFFFFFac\",\"parachute\":\"#48FFBDac\",\"nitro\":\"#AFFF48ac\",\"altitude\":\"#00FFF0ac\",\"oxy\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"stress\":\"#48A7FFac\",\"stamina\":\"#C4FF48ac\"},\"classic\":{\"hunger\":\"#B3743A\",\"health\":\"#9F2929\",\"armor\":\"#2E3893\",\"parachute\":\"#48ffde\",\"nitro\":\"#8eff48\",\"altitude\":\"#48deff\",\"stamina\":\"#c4ff48\",\"thirst\":\"#2F549C\",\"stress\":\"#AA35A6\",\"oxy\":\"#48A7FFac\"},\"radial\":{\"hunger\":\"#FFA048ac\",\"health\":\"#FF4848ac\",\"armor\":\"#FFFFFFac\",\"parachute\":\"#48FFBDac\",\"nitro\":\"#AFFF48ac\",\"altitude\":\"#00FFF0ac\",\"oxy\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"stress\":\"#48A7FFac\",\"stamina\":\"#C4FF48ac\"}},\"showHideBox\":false,\"refreshRate\":200}', 0),
('83e2aa1acb1704d4c62ce7ff82d05a429e2cecff', '{\"hide\":false,\"hideBoxData\":{\"hunger\":100,\"health\":100,\"armor\":100,\"stamina\":100,\"water\":100,\"stress\":100},\"hud\":\"text\",\"hudSize\":0.8,\"showCompass\":true,\"speedometerSize\":0.7,\"speedtype\":\"kmh\",\"maptype\":\"rectangle\",\"positionsData\":[],\"hudColors\":{\"text\":{\"hunger\":\"#FFA048ac\",\"health\":\"#FF4848ac\",\"armor\":\"#FFFFFFac\",\"parachute\":\"#48FFBDac\",\"nitro\":\"#AFFF48ac\",\"altitude\":\"#00FFF0ac\",\"oxy\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"stress\":\"#48A7FFac\",\"stamina\":\"#C4FF48ac\"},\"classic\":{\"hunger\":\"#B3743A\",\"health\":\"#9F2929\",\"armor\":\"#2E3893\",\"parachute\":\"#48ffde\",\"nitro\":\"#8eff48\",\"altitude\":\"#48deff\",\"stamina\":\"#c4ff48\",\"thirst\":\"#2F549C\",\"stress\":\"#AA35A6\",\"oxy\":\"#48A7FFac\"},\"radial\":{\"hunger\":\"#FFA048ac\",\"health\":\"#FF4848ac\",\"armor\":\"#FFFFFFac\",\"parachute\":\"#48FFBDac\",\"nitro\":\"#AFFF48ac\",\"altitude\":\"#00FFF0ac\",\"oxy\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"stress\":\"#48A7FFac\",\"stamina\":\"#C4FF48ac\"}},\"showHideBox\":false,\"refreshRate\":200}', 0),
('5f664dbb1d3ed719fe2f587b0dfa1b62cf9c0be6', '{\"hide\":false,\"hideBoxData\":{\"hunger\":100,\"health\":100,\"armor\":100,\"stamina\":100,\"water\":100,\"stress\":100},\"hud\":\"text\",\"hudSize\":0.8,\"showCompass\":true,\"speedometerSize\":0.7,\"speedtype\":\"kmh\",\"maptype\":\"rectangle\",\"positionsData\":[],\"hudColors\":{\"text\":{\"hunger\":\"#FFA048ac\",\"health\":\"#FF4848ac\",\"armor\":\"#FFFFFFac\",\"parachute\":\"#48FFBDac\",\"nitro\":\"#AFFF48ac\",\"altitude\":\"#00FFF0ac\",\"oxy\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"stress\":\"#48A7FFac\",\"stamina\":\"#C4FF48ac\"},\"classic\":{\"hunger\":\"#B3743A\",\"health\":\"#9F2929\",\"armor\":\"#2E3893\",\"parachute\":\"#48ffde\",\"nitro\":\"#8eff48\",\"altitude\":\"#48deff\",\"stamina\":\"#c4ff48\",\"thirst\":\"#2F549C\",\"stress\":\"#AA35A6\",\"oxy\":\"#48A7FFac\"},\"radial\":{\"hunger\":\"#FFA048ac\",\"health\":\"#FF4848ac\",\"armor\":\"#FFFFFFac\",\"parachute\":\"#48FFBDac\",\"nitro\":\"#AFFF48ac\",\"altitude\":\"#00FFF0ac\",\"oxy\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"stress\":\"#48A7FFac\",\"stamina\":\"#C4FF48ac\"}},\"showHideBox\":false,\"refreshRate\":200}', 0),
('88f87e1ede7d8340d70efe9a3b6f48939687a4bf', '{\"hide\":false,\"hideBoxData\":{\"hunger\":100,\"health\":100,\"armor\":100,\"stamina\":100,\"water\":100,\"stress\":100},\"hud\":\"text\",\"hudSize\":0.8,\"showCompass\":true,\"speedometerSize\":0.7,\"speedtype\":\"kmh\",\"maptype\":\"rectangle\",\"positionsData\":[],\"hudColors\":{\"text\":{\"hunger\":\"#FFA048ac\",\"health\":\"#FF4848ac\",\"armor\":\"#FFFFFFac\",\"parachute\":\"#48FFBDac\",\"nitro\":\"#AFFF48ac\",\"altitude\":\"#00FFF0ac\",\"oxy\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"stress\":\"#48A7FFac\",\"stamina\":\"#C4FF48ac\"},\"classic\":{\"hunger\":\"#B3743A\",\"health\":\"#9F2929\",\"armor\":\"#2E3893\",\"parachute\":\"#48ffde\",\"nitro\":\"#8eff48\",\"altitude\":\"#48deff\",\"stamina\":\"#c4ff48\",\"thirst\":\"#2F549C\",\"stress\":\"#AA35A6\",\"oxy\":\"#48A7FFac\"},\"radial\":{\"hunger\":\"#FFA048ac\",\"health\":\"#FF4848ac\",\"armor\":\"#FFFFFFac\",\"parachute\":\"#48FFBDac\",\"nitro\":\"#AFFF48ac\",\"altitude\":\"#00FFF0ac\",\"oxy\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"stress\":\"#48A7FFac\",\"stamina\":\"#C4FF48ac\"}},\"showHideBox\":false,\"refreshRate\":200}', 0),
('14a3a51bb7d566d4e53a43da49700c5e98692adc', '{\"hide\":false,\"hideBoxData\":{\"hunger\":100,\"health\":100,\"armor\":100,\"stamina\":100,\"water\":100,\"stress\":100},\"hud\":\"text\",\"hudSize\":0.8,\"showCompass\":true,\"speedometerSize\":0.7,\"speedtype\":\"kmh\",\"maptype\":\"rectangle\",\"positionsData\":[],\"hudColors\":{\"text\":{\"hunger\":\"#FFA048ac\",\"health\":\"#FF4848ac\",\"armor\":\"#FFFFFFac\",\"parachute\":\"#48FFBDac\",\"nitro\":\"#AFFF48ac\",\"altitude\":\"#00FFF0ac\",\"oxy\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"stress\":\"#48A7FFac\",\"stamina\":\"#C4FF48ac\"},\"classic\":{\"hunger\":\"#B3743A\",\"health\":\"#9F2929\",\"armor\":\"#2E3893\",\"parachute\":\"#48ffde\",\"nitro\":\"#8eff48\",\"altitude\":\"#48deff\",\"stamina\":\"#c4ff48\",\"thirst\":\"#2F549C\",\"stress\":\"#AA35A6\",\"oxy\":\"#48A7FFac\"},\"radial\":{\"hunger\":\"#FFA048ac\",\"health\":\"#FF4848ac\",\"armor\":\"#FFFFFFac\",\"parachute\":\"#48FFBDac\",\"nitro\":\"#AFFF48ac\",\"altitude\":\"#00FFF0ac\",\"oxy\":\"#48A7FFac\",\"thirst\":\"#4886FFac\",\"stress\":\"#48A7FFac\",\"stamina\":\"#C4FF48ac\"}},\"showHideBox\":false,\"refreshRate\":200}', 0);

-- --------------------------------------------------------

--
-- Table structure for table `commend`
--

CREATE TABLE `commend` (
  `ID` int(255) NOT NULL,
  `license` varchar(255) NOT NULL,
  `reason` varchar(1024) NOT NULL,
  `staff_name` varchar(255) NOT NULL,
  `staff_steamid` varchar(255) NOT NULL,
  `time` varchar(255) NOT NULL,
  `community` varchar(255) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `communities`
--

CREATE TABLE `communities` (
  `ID` int(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `time` int(11) NOT NULL,
  `uniqueid` varchar(255) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `communityservice`
--

CREATE TABLE `communityservice` (
  `identifier` varchar(100) NOT NULL,
  `actions_remaining` int(10) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `config`
--

CREATE TABLE `config` (
  `ID` int(255) NOT NULL,
  `community_name` varchar(255) NOT NULL DEFAULT 'Community Name',
  `discord_webhook` varchar(1024) DEFAULT NULL,
  `joinmessages` enum('true','false') NOT NULL DEFAULT 'false',
  `chatcommands` enum('true','false') NOT NULL DEFAULT 'true',
  `checktimeout` int(255) NOT NULL DEFAULT 15,
  `trustscore` int(255) NOT NULL DEFAULT 75,
  `tswarn` int(255) NOT NULL DEFAULT 3,
  `tskick` int(255) NOT NULL DEFAULT 6,
  `tsban` int(255) NOT NULL DEFAULT 10,
  `tscommend` int(255) NOT NULL DEFAULT 2,
  `tstime` int(255) NOT NULL DEFAULT 1,
  `recent_time` int(255) NOT NULL DEFAULT 10,
  `permissions` varchar(20480) NOT NULL,
  `serveractions` varchar(20480) NOT NULL,
  `debug` enum('false','true') NOT NULL DEFAULT 'false',
  `community` varchar(255) NOT NULL,
  `plugin_extendeduserinfo` tinyint(1) NOT NULL DEFAULT 0,
  `plugin_globaluserinfo` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `criminal_record`
--

CREATE TABLE `criminal_record` (
  `id` int(11) NOT NULL,
  `sender` varchar(250) DEFAULT NULL,
  `owner` varchar(250) DEFAULT NULL,
  `record` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `daily_free`
--

CREATE TABLE `daily_free` (
  `id` int(11) NOT NULL,
  `identifier` varchar(60) NOT NULL,
  `next_collect` int(15) NOT NULL,
  `times_collected` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `darkchat_messages`
--

CREATE TABLE `darkchat_messages` (
  `id` int(11) NOT NULL,
  `owner` varchar(50) DEFAULT NULL,
  `name` varchar(50) DEFAULT '',
  `messages` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `datastore`
--

CREATE TABLE `datastore` (
  `name` varchar(60) NOT NULL,
  `label` varchar(100) NOT NULL,
  `shared` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `datastore`
--

INSERT INTO `datastore` (`name`, `label`, `shared`) VALUES
('gang_1', 'Gang_1', 1),
('gang_2', 'Gang_2', 1),
('housing', 'Housing', 0),
('property', 'Propriété', 0),
('society_admin', 'Admin', 1),
('society_agent', 'Agent', 1),
('society_ambulance', 'Ambulance', 1),
('society_mechanic', 'Mécano', 1),
('society_police', 'Police', 1),
('society_taxi', 'Taxi', 1),
('user_ears', 'Ears', 0),
('user_glasses', 'Glasses', 0),
('user_helmet', 'Helmet', 0),
('user_mask', 'Mask', 0);

-- --------------------------------------------------------

--
-- Table structure for table `datastore_data`
--

CREATE TABLE `datastore_data` (
  `id` int(11) NOT NULL,
  `name` varchar(60) NOT NULL,
  `owner` varchar(40) DEFAULT NULL,
  `data` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `datastore_data`
--

INSERT INTO `datastore_data` (`id`, `name`, `owner`, `data`) VALUES
(1636, 'society_admin', NULL, '{\"weapons\":[{\"name\":\"WEAPON_PISTOL\",\"count\":1}]}'),
(1637, 'society_agent', NULL, '{}'),
(1638, 'society_ambulance', NULL, '{}'),
(1639, 'society_mechanic', NULL, '{}'),
(1640, 'society_police', NULL, '{}'),
(1641, 'society_taxi', NULL, '{}'),
(1642, 'housing', '241c0b24a5a116ef2f535cf37d83e17675b2ff15', '{}'),
(1643, 'property', '241c0b24a5a116ef2f535cf37d83e17675b2ff15', '{}'),
(1644, 'user_mask', '241c0b24a5a116ef2f535cf37d83e17675b2ff15', '{}'),
(1645, 'user_helmet', '241c0b24a5a116ef2f535cf37d83e17675b2ff15', '{}'),
(1646, 'user_ears', '241c0b24a5a116ef2f535cf37d83e17675b2ff15', '{}'),
(1647, 'user_glasses', '241c0b24a5a116ef2f535cf37d83e17675b2ff15', '{}'),
(1648, 'user_ears', '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', '{}'),
(1649, 'user_glasses', '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', '{}'),
(1650, 'housing', '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', '{}'),
(1651, 'property', '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', '{\"dressing\":[{\"skin\":{\"lipstick_2\":0,\"bproof_2\":0,\"sex\":0,\"lipstick_4\":0,\"ears_2\":0,\"bproof_1\":0,\"lipstick_1\":0,\"hair_color_2\":0,\"tshirt_1\":92,\"hair_2\":0,\"tshirt_2\":0,\"eyebrows_1\":0,\"makeup_3\":0,\"face\":0,\"shoes_1\":1,\"torso_1\":189,\"beard_1\":0,\"chain_2\":10,\"beard_2\":0,\"beard_4\":0,\"eyebrows_3\":0,\"lipstick_3\":0,\"decals_1\":0,\"age_1\":0,\"arms\":31,\"shoes_2\":0,\"helmet_2\":0,\"bags_1\":0,\"chain_1\":37,\"makeup_2\":0,\"hair_1\":0,\"eyebrows_4\":0,\"glasses_1\":24,\"mask_2\":0,\"mask_1\":5,\"ears_1\":-1,\"beard_3\":0,\"eyebrows_2\":0,\"helmet_1\":118,\"pants_1\":66,\"decals_2\":0,\"makeup_4\":0,\"skin\":0,\"torso_2\":0,\"age_2\":0,\"bags_2\":0,\"pants_2\":3,\"hair_color_1\":0,\"makeup_1\":0,\"glasses_2\":2},\"label\":\"3\"}]}'),
(1652, 'user_helmet', '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', '{}'),
(1653, 'user_mask', '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', '{}'),
(1654, 'housing', '4b5f3d01b6520c7f3ce37700ca11b06789073460', '{}'),
(1655, 'property', '4b5f3d01b6520c7f3ce37700ca11b06789073460', '{}'),
(1656, 'user_ears', '4b5f3d01b6520c7f3ce37700ca11b06789073460', '{}'),
(1657, 'user_glasses', '4b5f3d01b6520c7f3ce37700ca11b06789073460', '{}'),
(1658, 'user_mask', '4b5f3d01b6520c7f3ce37700ca11b06789073460', '{}'),
(1659, 'user_helmet', '4b5f3d01b6520c7f3ce37700ca11b06789073460', '{}'),
(1660, 'user_glasses', '370af5c1f03b439803d21d19b79427f9707f46ee', '{}'),
(1661, 'user_ears', '370af5c1f03b439803d21d19b79427f9707f46ee', '{}'),
(1662, 'property', '370af5c1f03b439803d21d19b79427f9707f46ee', '{}'),
(1663, 'housing', '370af5c1f03b439803d21d19b79427f9707f46ee', '{}'),
(1664, 'user_helmet', '370af5c1f03b439803d21d19b79427f9707f46ee', '{}'),
(1665, 'user_mask', '370af5c1f03b439803d21d19b79427f9707f46ee', '{}'),
(1666, 'user_ears', '9eef030b5f84df9eefcd3154fdac29df4fa63eb7', '{}'),
(1667, 'property', '9eef030b5f84df9eefcd3154fdac29df4fa63eb7', '{}'),
(1668, 'housing', '9eef030b5f84df9eefcd3154fdac29df4fa63eb7', '{}'),
(1669, 'user_glasses', '9eef030b5f84df9eefcd3154fdac29df4fa63eb7', '{}'),
(1670, 'user_helmet', '9eef030b5f84df9eefcd3154fdac29df4fa63eb7', '{}'),
(1671, 'user_mask', '9eef030b5f84df9eefcd3154fdac29df4fa63eb7', '{}'),
(1672, 'property', '83e2aa1acb1704d4c62ce7ff82d05a429e2cecff', '{}'),
(1673, 'user_glasses', '83e2aa1acb1704d4c62ce7ff82d05a429e2cecff', '{}'),
(1674, 'user_ears', '83e2aa1acb1704d4c62ce7ff82d05a429e2cecff', '{}'),
(1675, 'user_mask', '83e2aa1acb1704d4c62ce7ff82d05a429e2cecff', '{}'),
(1676, 'housing', '83e2aa1acb1704d4c62ce7ff82d05a429e2cecff', '{}'),
(1677, 'user_helmet', '83e2aa1acb1704d4c62ce7ff82d05a429e2cecff', '{}'),
(1678, 'housing', '5f664dbb1d3ed719fe2f587b0dfa1b62cf9c0be6', '{}'),
(1679, 'user_ears', '5f664dbb1d3ed719fe2f587b0dfa1b62cf9c0be6', '{}'),
(1680, 'property', '5f664dbb1d3ed719fe2f587b0dfa1b62cf9c0be6', '{}'),
(1681, 'user_glasses', '5f664dbb1d3ed719fe2f587b0dfa1b62cf9c0be6', '{}'),
(1682, 'user_helmet', '5f664dbb1d3ed719fe2f587b0dfa1b62cf9c0be6', '{}'),
(1683, 'user_mask', '5f664dbb1d3ed719fe2f587b0dfa1b62cf9c0be6', '{}'),
(1684, 'user_ears', '88f87e1ede7d8340d70efe9a3b6f48939687a4bf', '{}'),
(1685, 'property', '88f87e1ede7d8340d70efe9a3b6f48939687a4bf', '{}'),
(1686, 'housing', '88f87e1ede7d8340d70efe9a3b6f48939687a4bf', '{}'),
(1687, 'user_glasses', '88f87e1ede7d8340d70efe9a3b6f48939687a4bf', '{}'),
(1688, 'user_mask', '88f87e1ede7d8340d70efe9a3b6f48939687a4bf', '{}'),
(1689, 'user_helmet', '88f87e1ede7d8340d70efe9a3b6f48939687a4bf', '{}'),
(1690, 'user_glasses', '14a3a51bb7d566d4e53a43da49700c5e98692adc', '{}'),
(1691, 'housing', '14a3a51bb7d566d4e53a43da49700c5e98692adc', '{}'),
(1692, 'user_ears', '14a3a51bb7d566d4e53a43da49700c5e98692adc', '{}'),
(1693, 'property', '14a3a51bb7d566d4e53a43da49700c5e98692adc', '{}'),
(1694, 'user_helmet', '14a3a51bb7d566d4e53a43da49700c5e98692adc', '{}'),
(1695, 'user_mask', '14a3a51bb7d566d4e53a43da49700c5e98692adc', '{}'),
(1696, 'gang_1', NULL, '{}'),
(1697, 'gang_2', NULL, '{}');

-- --------------------------------------------------------

--
-- Table structure for table `doublexpusers`
--

CREATE TABLE `doublexpusers` (
  `identifier` varchar(40) NOT NULL,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(255) NOT NULL,
  `remainingtime` varchar(50) NOT NULL,
  `packname` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- Table structure for table `fine_types`
--

CREATE TABLE `fine_types` (
  `id` int(11) NOT NULL,
  `label` varchar(255) DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  `category` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `fine_types`
--

INSERT INTO `fine_types` (`id`, `label`, `amount`, `category`) VALUES
(1, 'تظليل مركبة', 5000, 0),
(2, 'عدم وجود رخصه', 10000, 0),
(3, 'قيادة بسرعة ', 5000, 0),
(4, 'عكس السير / حجز المركبة', 2500, 0),
(5, 'وقوف خاطئ', 1500, 0),
(6, 'قطع اشارة', 7000, 0),
(7, 'هروب من الشرطة /10 اعمال شاقة', 10000, 0),
(8, 'عدم انصياع لاوامر الشرطة / 10 اعمال شاقة', 3000, 0),
(9, 'مخالفة ذوق العام', 2000, 1),
(10, 'القيادة اثناء تضرر المركبة ', 1000, 0),
(11, 'ازعاج سلطات', 2500, 1),
(12, 'ترك المركبة  على الطريق العام / حجز مركبة', 2500, 0),
(13, 'تفحيط / حجز مركبة / 15 اعمال شاقة', 5500, 0),
(14, 'سرقة صرافة - 20 أعمال شاقة', 8000, 1),
(15, 'سرقة اليخت - 30 شهر', 15000, 1),
(16, 'سرقة مركبة / 10 اعمال شاقة', 4500, 1),
(17, 'تهديد رجل امن /  10 اعمال شاقة', 10000, 1),
(18, 'حيازة سلاح بدون رخصة ', 5000, 1),
(19, 'قتل رجل امن  / 15شهر', 15000, 1),
(20, 'تهديد مواطن  /  20 شهر', 10000, 1),
(21, 'اهانة رجل امن  / 5شهر', 5000, 1),
(22, 'صدم مركبة', 10000, 1),
(23, 'مساعدة مجرم / 10 شهر', 6500, 1),
(24, 'بلاغ كاذب', 3500, 1),
(25, 'خطف مواطن / سجن15 شهر', 8000, 1),
(26, 'سرقة بقالة / سجن 15 شهر', 10000, 2),
(27, 'سرقة محل مجوهرات / 20 شهر', 20000, 2),
(28, 'سرقة بنك / سجن 20شهر', 20000, 2),
(29, 'ارهاب / سجن 10 شهر', 15000, 2),
(30, 'قتل مواطن / 15 شهر', 15000, 2),
(31, 'سرقة محل أسلحة / 10شهر', 10000, 2),
(32, 'تجارة ممنوعات / سجن 15 شهر', 15000, 2),
(33, 'حيازة ممنوعات / 10 شهر', 5000, 2),
(34, 'رسوم دخول الميناء 40 كيلو', 500, 3),
(35, 'رسوم دخول الميناء 60 كليو', 900, 3),
(36, 'رسوم دخول الميناء 80 كيلو', 2500, 3),
(37, 'رسوم دخول الميناء 120 كيلو ', 3500, 3),
(38, 'رسوم دخول الميناء 120 كيلو ', 4900, 3),
(39, 'رسوم دخول الميناء 250 كيلو ', 5500, 3),
(40, 'رسوم دخول الميناء 200 كيلو ', 12000, 3),
(41, 'رسوم الحراسة في مناطق العمل', 2000, 3),
(42, 'رسوم إستعمال مستودع المتاجر', 3000, 3),
(43, 'رسوم دخول الميناء', 500, 3),
(44, 'حراسة شخصية - قيمة 5 ألاف', 5000, 4),
(45, 'حراسة شخصية - قيمة 15 ألف', 15000, 4),
(46, 'حراسة شخصية - قيمة 25 ألف', 25000, 4),
(47, 'مخالفة عامة من الرقابة', 1000, 5),
(49, 'مخالفة عامة من الرقابة', 3000, 5),
(50, 'مخالفة عامة من الرقابة', 5000, 5),
(51, 'مخالفة عامة من الرقابة', 7000, 5),
(52, 'مخالفة عامة من الرقابة', 10000, 5),
(53, 'مخالفة عامة من الرقابة', 15000, 5),
(54, 'مخالفة عامة من الرقابة', 20000, 5),
(55, 'مخالفة عامة من الرقابة', 25000, 5),
(56, 'مخالفة عامة من الرقابة', 35000, 5),
(57, 'مخالفة عامة من الرقابة', 50000, 5),
(58, 'مخالفة عامة من الرقابة', 75000, 5),
(59, 'مخالفة عامة من الرقابة', 100000, 5),
(60, 'مخالفة عامة من الرقابة', 125000, 5),
(61, 'مخالفة عامة من الرقابة', 150000, 5),
(62, 'مخالفة عامة من الرقابة', 200000, 5),
(63, 'مخالفة عامة من الرقابة', 250000, 5),
(64, 'مخالفة عامة من الرقابة', 500000, 5),
(65, 'مخالفة عامة من الرقابة', 750000, 5),
(66, 'مخالفة عامة من الرقابة', 1000000, 5);

-- --------------------------------------------------------

--
-- Table structure for table `foodtrucks`
--

CREATE TABLE `foodtrucks` (
  `plate` varchar(12) NOT NULL,
  `src` varchar(50) NOT NULL,
  `count` int(11) NOT NULL,
  `item` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `price` int(11) NOT NULL,
  `label` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- Table structure for table `gangs`
--

CREATE TABLE `gangs` (
  `id` int(11) NOT NULL,
  `gang_id` varchar(11) NOT NULL,
  `gang_title` varchar(90) DEFAULT NULL,
  `gang_owner` varchar(90) DEFAULT NULL,
  `total_emps` int(11) NOT NULL,
  `gang_emps` longtext DEFAULT NULL,
  `buy_date` int(11) DEFAULT 0,
  `auto_renew` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `gangs`
--

INSERT INTO `gangs` (`id`, `gang_id`, `gang_title`, `gang_owner`, `total_emps`, `gang_emps`, `buy_date`, `auto_renew`) VALUES
(1, 'gang_1', 'سيف', '88f87e1ede7d8340d70efe9a3b6f48939687a4bf', 13, '[\"64bf93f4ae55cf7b5cd8f42e413d535088fa5196\"]', 1725575605, 1),
(2, 'gang_2', NULL, NULL, 4, NULL, 0, 1),
(3, 'gang_5', NULL, NULL, 4, NULL, 0, 0),
(4, 'gang_3', NULL, NULL, 4, NULL, 0, 0),
(5, 'gang_8', NULL, NULL, 4, NULL, 0, 0),
(6, 'gang_10', NULL, NULL, 4, NULL, 0, 0),
(7, 'gang_15', NULL, NULL, 4, NULL, 0, 0),
(8, 'gang_7', NULL, NULL, 4, NULL, 0, 0),
(9, 'gang_11', NULL, NULL, 4, NULL, 0, 0),
(10, 'gang_6', NULL, NULL, 4, NULL, 0, 0),
(11, 'gang_13', NULL, NULL, 4, NULL, 0, 0),
(12, 'gang_14', NULL, NULL, 4, NULL, 0, 0),
(13, 'gang_4', NULL, NULL, 4, NULL, 0, 0),
(14, 'gang_12', NULL, NULL, 4, NULL, 0, 0),
(15, 'gang_9', NULL, NULL, 4, NULL, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `gas_station_balance`
--

CREATE TABLE `gas_station_balance` (
  `id` int(10) UNSIGNED NOT NULL,
  `gas_station_id` varchar(50) NOT NULL,
  `income` bit(1) NOT NULL,
  `title` varchar(255) NOT NULL,
  `amount` int(10) UNSIGNED NOT NULL,
  `date` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gas_station_business`
--

CREATE TABLE `gas_station_business` (
  `gas_station_id` varchar(50) NOT NULL DEFAULT '',
  `user_id` varchar(50) NOT NULL,
  `stock` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `price` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `stock_upgrade` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `truck_upgrade` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `relationship_upgrade` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `money` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `total_money_earned` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `total_money_spent` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `gas_bought` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `gas_sold` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `distance_traveled` double UNSIGNED NOT NULL DEFAULT 0,
  `total_visits` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `customers` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `timer` int(10) UNSIGNED NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gas_station_jobs`
--

CREATE TABLE `gas_station_jobs` (
  `id` int(10) UNSIGNED NOT NULL,
  `gas_station_id` varchar(50) NOT NULL DEFAULT '',
  `name` varchar(50) NOT NULL,
  `reward` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `amount` int(11) NOT NULL DEFAULT 0,
  `progress` bit(1) NOT NULL DEFAULT b'0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_app_chat`
--

CREATE TABLE `gksphone_app_chat` (
  `id` int(11) NOT NULL,
  `channel` varchar(20) NOT NULL,
  `message` varchar(255) NOT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_bank_transfer`
--

CREATE TABLE `gksphone_bank_transfer` (
  `id` int(11) NOT NULL,
  `type` int(11) NOT NULL,
  `identifier` longtext DEFAULT NULL,
  `price` longtext NOT NULL,
  `name` longtext NOT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_blockednumber`
--

CREATE TABLE `gksphone_blockednumber` (
  `id` int(11) NOT NULL,
  `identifier` longtext NOT NULL,
  `hex` longtext NOT NULL,
  `number` longtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_calls`
--

CREATE TABLE `gksphone_calls` (
  `id` int(11) NOT NULL,
  `owner` longtext NOT NULL COMMENT 'Num tel proprio',
  `num` longtext NOT NULL COMMENT 'Num reférence du contact',
  `incoming` int(11) NOT NULL COMMENT 'Défini si on est à l''origine de l''appels',
  `time` timestamp NOT NULL DEFAULT current_timestamp(),
  `accepts` int(11) NOT NULL COMMENT 'Appels accepter ou pas'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_ebay`
--

CREATE TABLE `gksphone_ebay` (
  `id` int(11) NOT NULL,
  `label` longtext NOT NULL,
  `price` int(11) DEFAULT 0,
  `count` int(11) NOT NULL,
  `item` longtext NOT NULL,
  `kapat` varchar(50) DEFAULT 'false',
  `adet` int(11) DEFAULT 0,
  `cid` varchar(9999) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_gallery`
--

CREATE TABLE `gksphone_gallery` (
  `id` int(11) NOT NULL,
  `hex` longtext NOT NULL,
  `image` longtext NOT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_gps`
--

CREATE TABLE `gksphone_gps` (
  `id` int(11) NOT NULL,
  `hex` longtext NOT NULL,
  `nott` longtext DEFAULT NULL,
  `gps` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_group_message`
--

CREATE TABLE `gksphone_group_message` (
  `id` int(11) NOT NULL,
  `groupid` int(11) NOT NULL,
  `owner` longtext NOT NULL,
  `ownerphone` varchar(50) NOT NULL,
  `groupname` varchar(255) NOT NULL,
  `messages` longtext NOT NULL,
  `contacts` longtext NOT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_instocomment`
--

CREATE TABLE `gksphone_instocomment` (
  `id` int(11) NOT NULL,
  `messageid` int(11) NOT NULL DEFAULT 0,
  `userid` int(11) NOT NULL DEFAULT 0,
  `message` longtext NOT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_insto_accounts`
--

CREATE TABLE `gksphone_insto_accounts` (
  `id` int(11) NOT NULL,
  `forename` longtext NOT NULL,
  `surname` longtext NOT NULL,
  `username` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `password` longtext NOT NULL,
  `avatar_url` longtext DEFAULT NULL,
  `takip` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

--
-- Dumping data for table `gksphone_insto_accounts`
--

INSERT INTO `gksphone_insto_accounts` (`id`, `forename`, `surname`, `username`, `password`, `avatar_url`, `takip`) VALUES
(12, 'شتلان', 'pzxe', 'pzxe', '112233', '', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_insto_instas`
--

CREATE TABLE `gksphone_insto_instas` (
  `id` int(11) NOT NULL,
  `authorId` int(11) NOT NULL,
  `realUser` longtext DEFAULT NULL,
  `message` longtext NOT NULL,
  `image` longtext NOT NULL,
  `filters` longtext NOT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp(),
  `likes` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_insto_likes`
--

CREATE TABLE `gksphone_insto_likes` (
  `id` int(11) NOT NULL,
  `authorId` int(11) DEFAULT NULL,
  `inapId` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_insto_story`
--

CREATE TABLE `gksphone_insto_story` (
  `id` int(11) NOT NULL,
  `authorId` int(11) NOT NULL,
  `realUser` longtext DEFAULT NULL,
  `stories` longtext NOT NULL,
  `isRead` varchar(256) NOT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp(),
  `likes` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_job_message`
--

CREATE TABLE `gksphone_job_message` (
  `id` int(11) NOT NULL,
  `name` longtext NOT NULL,
  `number` varchar(50) NOT NULL,
  `message` longtext NOT NULL,
  `photo` longtext DEFAULT NULL,
  `gps` varchar(255) NOT NULL,
  `owner` int(11) NOT NULL DEFAULT 0,
  `jobm` varchar(255) NOT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_lapraces`
--

CREATE TABLE `gksphone_lapraces` (
  `id` int(11) NOT NULL,
  `name` varchar(50) DEFAULT NULL,
  `checkpoints` text DEFAULT NULL,
  `records` text DEFAULT NULL,
  `creator` varchar(50) DEFAULT NULL,
  `distance` int(11) DEFAULT NULL,
  `raceid` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_mails`
--

CREATE TABLE `gksphone_mails` (
  `id` int(11) NOT NULL,
  `citizenid` varchar(255) NOT NULL DEFAULT '0',
  `sender` varchar(255) NOT NULL DEFAULT '0',
  `subject` varchar(255) NOT NULL DEFAULT '0',
  `image` text DEFAULT NULL,
  `message` text NOT NULL,
  `button` text DEFAULT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_messages`
--

CREATE TABLE `gksphone_messages` (
  `id` int(11) NOT NULL,
  `transmitter` varchar(50) NOT NULL,
  `receiver` varchar(50) NOT NULL,
  `message` longtext NOT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp(),
  `isRead` int(11) NOT NULL DEFAULT 0,
  `owner` int(11) NOT NULL DEFAULT 0
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_messages_group`
--

CREATE TABLE `gksphone_messages_group` (
  `id` int(11) NOT NULL,
  `owner` longtext NOT NULL,
  `ownerphone` varchar(50) NOT NULL,
  `groupname` varchar(255) NOT NULL,
  `gimage` longtext NOT NULL,
  `contacts` longtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_news`
--

CREATE TABLE `gksphone_news` (
  `id` int(11) NOT NULL,
  `hex` longtext DEFAULT NULL,
  `haber` longtext DEFAULT NULL,
  `baslik` longtext DEFAULT NULL,
  `resim` longtext DEFAULT NULL,
  `video` longtext DEFAULT NULL,
  `zaman` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_settings`
--

CREATE TABLE `gksphone_settings` (
  `id` int(11) NOT NULL,
  `identifier` longtext NOT NULL,
  `crypto` varchar(535) DEFAULT '{}',
  `phone_number` varchar(50) DEFAULT NULL,
  `avatar_url` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `gksphone_settings`
--

INSERT INTO `gksphone_settings` (`id`, `identifier`, `crypto`, `phone_number`, `avatar_url`) VALUES
(175, '241c0b24a5a116ef2f535cf37d83e17675b2ff15', '{}', '7431121', NULL),
(176, '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', '{}', '2360931', NULL),
(177, '4b5f3d01b6520c7f3ce37700ca11b06789073460', '{}', '6934539', NULL),
(178, '370af5c1f03b439803d21d19b79427f9707f46ee', '{}', '5144317', NULL),
(179, '9eef030b5f84df9eefcd3154fdac29df4fa63eb7', '{}', '4374725', NULL),
(180, '83e2aa1acb1704d4c62ce7ff82d05a429e2cecff', '{}', '1439178', NULL),
(181, '5f664dbb1d3ed719fe2f587b0dfa1b62cf9c0be6', '{}', '2393341', NULL),
(182, '88f87e1ede7d8340d70efe9a3b6f48939687a4bf', '{}', '4638397', NULL),
(183, '14a3a51bb7d566d4e53a43da49700c5e98692adc', '{}', '3036865', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_tinderacc`
--

CREATE TABLE `gksphone_tinderacc` (
  `id` int(11) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `passaword` varchar(255) DEFAULT NULL,
  `date` varchar(255) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `gender` int(11) DEFAULT NULL,
  `identifier` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_tindermatch`
--

CREATE TABLE `gksphone_tindermatch` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL DEFAULT 0,
  `friend_id` int(11) NOT NULL DEFAULT 0,
  `is_match` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `gksphone_tindermatch`
--

INSERT INTO `gksphone_tindermatch` (`id`, `user_id`, `friend_id`, `is_match`) VALUES
(3, 4, 3, 1),
(4, 5, 4, 1),
(5, 6, 4, 0),
(6, 7, 4, 1);

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_tindermessage`
--

CREATE TABLE `gksphone_tindermessage` (
  `id` int(11) NOT NULL,
  `message` text NOT NULL,
  `tinderes` text NOT NULL,
  `owner` int(11) NOT NULL DEFAULT 0,
  `time` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_twitter_accounts`
--

CREATE TABLE `gksphone_twitter_accounts` (
  `id` int(11) NOT NULL,
  `username` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0',
  `password` varchar(64) NOT NULL DEFAULT '0',
  `avatar_url` longtext DEFAULT NULL,
  `profilavatar` longtext DEFAULT NULL,
  `identifier` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

--
-- Dumping data for table `gksphone_twitter_accounts`
--

INSERT INTO `gksphone_twitter_accounts` (`id`, `username`, `password`, `avatar_url`, `profilavatar`, `identifier`) VALUES
(11, 'شتلان', '112233', 'https://e.top4top.io/p_2485xxlrb1.jpg', 'https://cdn.discordapp.com/attachments/857729495007232071/1013783160263290920/IMG_8494.jpg', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_twitter_likes`
--

CREATE TABLE `gksphone_twitter_likes` (
  `id` int(11) NOT NULL,
  `authorId` int(11) DEFAULT NULL,
  `tweetId` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_twitter_tweets`
--

CREATE TABLE `gksphone_twitter_tweets` (
  `id` int(11) NOT NULL,
  `authorId` int(11) NOT NULL,
  `realUser` varchar(50) DEFAULT NULL,
  `message` varchar(256) NOT NULL,
  `image` longtext DEFAULT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp(),
  `likes` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_users_contacts`
--

CREATE TABLE `gksphone_users_contacts` (
  `id` int(11) NOT NULL,
  `identifier` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `number` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `display` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `avatar` longtext DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_vehicle_sales`
--

CREATE TABLE `gksphone_vehicle_sales` (
  `id` int(11) NOT NULL,
  `owner` longtext NOT NULL,
  `ownerphone` varchar(255) NOT NULL,
  `plate` varchar(255) NOT NULL,
  `model` varchar(255) NOT NULL,
  `price` int(11) NOT NULL,
  `image` longtext NOT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gksphone_yellow`
--

CREATE TABLE `gksphone_yellow` (
  `id` int(11) NOT NULL,
  `phone_number` varchar(50) DEFAULT NULL,
  `firstname` varchar(256) DEFAULT NULL,
  `lastname` varchar(256) DEFAULT NULL,
  `message` longtext NOT NULL,
  `image` longtext DEFAULT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `h_impounded_vehicles`
--

CREATE TABLE `h_impounded_vehicles` (
  `plate` varchar(12) NOT NULL,
  `officer` varchar(255) DEFAULT NULL,
  `mechanic` varchar(255) DEFAULT NULL,
  `releasedate` varchar(25) DEFAULT NULL,
  `fee` double NOT NULL,
  `reason` text NOT NULL,
  `notes` text DEFAULT NULL,
  `vehicle` text NOT NULL,
  `identifier` varchar(30) NOT NULL,
  `hold_o` tinyint(1) DEFAULT 0,
  `hold_m` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `instagram_account`
--

CREATE TABLE `instagram_account` (
  `id` varchar(90) NOT NULL,
  `name` varchar(50) NOT NULL,
  `phone` varchar(50) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(50) NOT NULL,
  `avatar` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `verify` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `instagram_followers`
--

CREATE TABLE `instagram_followers` (
  `username` varchar(50) NOT NULL,
  `followed` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `instagram_posts`
--

CREATE TABLE `instagram_posts` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `image` text NOT NULL,
  `description` varchar(255) NOT NULL,
  `location` varchar(50) NOT NULL,
  `filter` varchar(50) NOT NULL,
  `created` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `likes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `instagram_stories`
--

CREATE TABLE `instagram_stories` (
  `owner` varchar(50) NOT NULL,
  `data` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=armscii8 COLLATE=armscii8_bin;

-- --------------------------------------------------------

--
-- Table structure for table `insta_stories`
--

CREATE TABLE `insta_stories` (
  `username` varchar(50) DEFAULT NULL,
  `location` varchar(50) DEFAULT NULL,
  `filter` varchar(50) DEFAULT NULL,
  `description` varchar(50) DEFAULT NULL,
  `image` text DEFAULT NULL,
  `created` time DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `insto_accounts`
--

CREATE TABLE `insto_accounts` (
  `id` int(11) NOT NULL,
  `forename` varchar(50) NOT NULL DEFAULT '0',
  `surname` varchar(50) NOT NULL DEFAULT '0',
  `username` varchar(50) NOT NULL DEFAULT '0',
  `password` varchar(50) NOT NULL DEFAULT '0',
  `avatar_url` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `insto_instas`
--

CREATE TABLE `insto_instas` (
  `id` int(11) NOT NULL,
  `authorId` int(11) NOT NULL,
  `realUser` varchar(50) DEFAULT NULL,
  `message` varchar(256) NOT NULL,
  `image` varchar(256) NOT NULL,
  `filters` varchar(256) NOT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp(),
  `likes` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `insto_likes`
--

CREATE TABLE `insto_likes` (
  `id` int(11) NOT NULL,
  `authorId` int(11) DEFAULT NULL,
  `inapId` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `items`
--

CREATE TABLE `items` (
  `name` varchar(50) NOT NULL,
  `label` varchar(50) NOT NULL,
  `weight` int(11) NOT NULL DEFAULT 1,
  `rare` tinyint(4) NOT NULL DEFAULT 0,
  `can_remove` tinyint(4) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `items`
--

INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
('alive_chicken', 'دجاج حي', 1, 0, 1),
('bandage', 'ضماد جروح', 2, 0, 1),
('bandage_box', 'صندوق ضماد جروح', 10, 0, 1),
('batato', 'بطاطس', 1, 0, 1),
('batatobox', 'أكياس بطاطس', 10, 0, 1),
('beer', 'خمر', 1, 0, 1),
('bergrkb', 'وجبة برجر كبير', 1, 0, 1),
('bergrkbbox', 'صندوق وجبة برجر كبير', 10, 0, 1),
('bergrul', 'وجبة برجر وسط', 1, 0, 1),
('bergrulbox', 'صندوق برجر وسط', 10, 0, 1),
('binoculars', 'منظار يدوي', 1, 0, 1),
('binoculars_box', 'صندوق منظار يدوي', 10, 0, 1),
('blowpipe', 'Chalumeaux', 2, 0, 1),
('boxbig', 'تعبئة طلقات', 5, 0, 1),
('boxbig_box', 'صندوق تعبئة طلقات', 10, 0, 1),
('boxsmall', 'علبة ذخيرة صغيرة', 5, 0, 1),
('boxv_tool', 'صندوق عدة عمل خضروات', 1, 0, 1),
('bread', 'برجر', 1, 0, 1),
('breadbox', 'صندوق برجر', 1, 0, 1),
('bulletpolice', 'سترة مضادة للرصاص حكومية', 3, 0, 1),
('bulletproof', 'سترة مضادة للرصاص', 3, 0, 1),
('bulletproof_box', 'صندوق سترة مضادة للرصاص', 10, 0, 1),
('c4box', 'صندوق C4', 1, 0, 1),
('carokit', 'عدة سمكرة', 3, 0, 1),
('carotool', 'ادوات سمكرة', 2, 0, 1),
('casinochips', 'كوينز كازينو', 0, 0, 1),
('cement_1', 'خام مكسر', 1, 0, 1),
('cement_2', 'خام مخلوط', 1, 0, 1),
('cement_3', 'خلطة أسمنت', 1, 0, 1),
('cement_kit', 'عدة عمل أسمنت', 4, 0, 1),
('cement_kit_box', 'صندوق عدة أسمنت', 10, 0, 1),
('brickworkkit', 'عدة عمل أسمنت', 1, 0, 1),
('cement', 'أسمنت', 1, 0, 1),
('concretemix', 'خلطة خرسانية', 1, 0, 1),
('bricks', 'أسمنت', 1, 0, 1),
('mnahel_toolbox', 'صندوق عدة النحال', 20, 0, 1),
('mnahel_bottle', 'عسل طبيعي', 3, 0, 1),
('mnahel_box', 'علبة عسل جاهزة للبيع', 2, 0, 1),
('mnahel_bucket', 'عسل خام', 4, 0, 1),
('mnahel_tool', 'عدة النحال', 4, 0, 1),
('tomoterToolbox', 'صندوق عدة تمور', 20, 0, 1),
('tomot_bottle', 'تمور طازجة', 4, 0, 1),
('tomot_box', 'سلة تمور طازجة', 1, 0, 1),
('tomot_bucket', 'تمور غير طازجة', 4, 0, 1),
('tomot_tool', 'عدة تمور', 4, 0, 1),
('waterrerToolbox', 'صندوق عدة مياه', 20, 0, 1),
('wa_ghermohla', 'مياة غير معالجة', 4, 0, 1),
('wa_mohlaa', 'مياة محلاة', 1, 0, 1),
('wa_mloth', 'مياة ملوثة', 4, 0, 1),
('wa_tool', 'عدة عمل المياة', 4, 0, 1),
('milkerToolbox', 'صندوق عدة حليب', 20, 0, 1),
('milk_bottle', 'قنينة حليب', 4, 0, 1),
('milk_box', 'صندوق قنينات حليب', 1, 0, 1),
('milk_bucket', 'سطل حليب', 4, 0, 1),
('milk_tool', 'عدة حليب', 4, 0, 1),
('cheps', 'كيس شيبس', 1, 0, 1),
('chepsbox', 'صندوق شيبس', 1, 0, 1),
('chocolate', 'شوكلاتة', 1, 0, 1),
('chocolatebox', 'صندوق شوكولاتة', 1, 0, 1),
('cigarette', 'سجائر', 1, 0, 1),
('clip', 'علبة ذخيرة', 3, 0, 1),
('clippolice', 'علبة ذخيرة للشرطة', 3, 0, 1),
('clothe', 'ملابس', 1, 0, 1),
('cocacola', 'كولا', 1, 0, 1),
('cocacolabox', 'صندوق كولا', 1, 0, 1),
('coke', 'كوكايين', 1, 0, 1),
('coke_pooch', 'شدة كوكايين', 5, 0, 1),
('copper', 'نحاس', 1, 0, 1),
('coshe', 'سوشي', 1, 0, 1),
('coshebox', 'كرتون سوشي', 10, 0, 1),
('cupcake', 'فطيرة', 1, 0, 1),
('cupcakebox', 'صندوق فطائر', 1, 0, 1),
('cutted_wood', 'قطع خشب', 1, 0, 1),
('diamond', 'ألماس', 1, 0, 1),
('dia_box', 'سبيكة ألماس', 5, 0, 1),
('drill', 'دريل', 1, 0, 1),
('essence', 'بنزين', 1, 0, 1),
('fabric', 'قماش', 1, 0, 1),
('firstaidkit', 'عدة إسعاف', 1, 0, 1),
('fish', 'سمك', 1, 0, 1),
('fishbait', 'طعم سمك', 1, 0, 1),
('fishbaitbox', 'صندوق طعم سمك', 1, 0, 1),
('fishbaitboxX', 'صندوق سنارة صيد', 1, 0, 1),
('fishingrod', 'سنارة صيد', 1, 0, 1),
('fixkit', 'عدة تصليح', 3, 0, 1),
('fixkitbox', 'صندوق عدة تصليح', 1, 0, 1),
('fixtool', 'عدة تصليح (لاتعمل)', 2, 0, 1),
('f_tool', 'عدة عمل النفط والغاز', 1, 0, 1),
('gas', 'غاز', 1, 0, 1),
('gas_raffin', 'غاز معالج', 1, 0, 1),
('gazbottle', 'اسطوانة غاز', 2, 0, 1),
('gold', 'ذهب', 1, 0, 1),
('gold_bar', 'سبيكة ذهب', 10, 0, 1),
('grape', 'عنب', 1, 0, 1),
('grape_juice', 'عصير العنب', 1, 0, 1),
('grape_wine', 'خمر', 1, 0, 1),
('headbag', 'خيشه', 1, 0, 1),
('headbagbox', 'صندوق خيشه', 1, 0, 1),
('id_card_f', 'جهاز تهكير البنك الصغير', 20, 3, 1),
('iron', 'حديد', 1, 0, 1),
('jewels', 'مجوهرات', 1, 0, 1),
('laptop_h', 'لاب توب', 1, 0, 1),
('lettuce', 'خس', 1, 0, 1),
('lighter', 'ولاعة', 1, 0, 1),
('lockpick', 'مفك أقفال', 1, 0, 1),
('lumberjackToolbox', 'صندوق عدة أخشاب', 1, 0, 1),
('l_tool', 'عدة عمل الأخشاب', 1, 0, 1),
('medikit', 'عدة إسعافات أولية', 2, 0, 1),
('medikit_box', 'صندوق إسعافات أولية', 10, 0, 1),
('meth', 'شبو', 1, 0, 1),
('meth_pooch', 'شدة شبو', 5, 0, 1),
('minerToolbox', 'صندوق عدة معادن', 1, 0, 1),
('m_tool', 'عدة عمل معادن', 1, 0, 1),
('opium', 'أفيون', 1, 0, 1),
('opium_pooch', 'شدة أفيون', 5, 0, 1),
('oxygen_mask', 'قناع أكسجين', 1, 0, 1),
('packaged_chicken', 'دجاج مغلف', 1, 0, 1),
('packaged_plank', 'حزمة خشب', 1, 0, 1),
('pepsi', 'بيبسي', 10, 0, 1),
('pepsibox', 'كرتون بيبسي', 10, 0, 1),
('petrol', 'نفط', 1, 0, 1),
('petrol_raffin', 'نفط مكرر', 1, 0, 1),
('phone', 'جوال', 1, 0, 1),
('phone_box', 'صندوق جوالات', 1, 0, 1),
('radio', 'راديو', 1, 0, 1),
('radiobox', 'صندوق راديو', 1, 0, 1),
('radionot', 'راديو ', 1, 0, 1),
('raisin', 'عنب', 1, 0, 1),
('secure_card', 'Secure ID Card', 1, 3, 1),
('shark', 'قرش', 1, 0, 1),
('slaughtered_chicken', 'دجاج ذبح حلال', 1, 0, 1),
('slaughtererToolbox', 'صندوق دواجن', 1, 0, 1),
('speedcamera_detector', 'كاشف رادار', 1, 0, 1),
('stone', 'حجر', 1, 0, 1),
('s_tool', 'عدة عمل الدواجن', 1, 0, 1),
('tailorToolbox', 'صندوق عدة أقمشة', 1, 0, 1),
('thermal_charge', 'متفجرات', 10, 0, 1),
('tomato', 'طماطم', 1, 0, 1),
('turtle', 'سلحفاة', 1, 0, 1),
('turtlebait', 'طعم سلحفاة', 1, 0, 1),
('turtlebaitbox', 'صندوق طعم سلاحف', 1, 0, 1),
('t_tool', 'عدة عمل ملابس', 1, 0, 1),
('v_ready', 'سلة خضروات', 1, 0, 1),
('v_tool', 'عدة عمل خضروات', 1, 0, 1),
('washed_stone', 'حجر نظيف', 1, 0, 1),
('water', 'مياه معدنية', 1, 0, 1),
('waterbox', 'صندوق مياه', 1, 0, 1),
('weakit', 'أدوات أسلحة', 1, 0, 1),
('weakit_box', 'صندوق أدوات أسلحة', 10, 0, 1),
('WEAPON_BATTLEAXE_box', 'صندوق فأس', 10, 0, 1),
('WEAPON_FLASHLIGHT_box', 'صندوق كشاف', 10, 0, 1),
('WEAPON_MACHETE_box', 'صندوق ساطور', 10, 0, 1),
('WEAPON_MICROSMG_box', 'صندوق مايكرو', 10, 0, 1),
('WEAPON_PISTOL_box', 'صندوق مسدس', 10, 0, 1),
('WEAPON_PUMPSHOTGUN_box', 'صندوق شوزن', 10, 0, 1),
('WEAPON_SWITCHBLADE_box', 'صندوق سكين', 10, 0, 1),
('weed', 'حشيش', 1, 0, 1),
('weed_pooch', 'شدة حشيش', 5, 0, 1),
('wood', 'خشب', 1, 0, 1),
('wool', 'صوف', 1, 0, 1),
('xanax', 'دواء زانكس - لعلاج الإدمان', 1, 0, 1),
('xanax_box', 'صندوق دواء زانكس', 10, 0, 1);

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `name` varchar(50) NOT NULL,
  `label` varchar(50) DEFAULT NULL,
  `whitelisted` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `jobs`
--

INSERT INTO `jobs` (`name`, `label`, `whitelisted`) VALUES
('admin', 'الرقابة و التفتيش', 1),
('agent', 'أمن المنشآت', 1),
('ambulance', 'الهلال الاحمر', 1),
('mujahideen', 'المجاهدين', 1),
('bread', 'شركة ابو بشير الخباز للخبز', 1),
('farmer', 'شركة المزارع ', 0),
('fisherman', 'شركة الأسماك المتحدة', 0),
('fork', 'شركة أمن الموانئ ', 1),
('fueler', 'شركة النفط والغاز الدولية', 0),
('lumberjack', 'شركة الأخشاب المحلية', 0),
('mechanic', 'كراج الميكانيك', 1),
('miner', 'الشركة الدولية للمعادن', 0),
('police', 'إدارة الشرطة', 1),
('reporter', 'الحدث للصحافة والإعلام', 1),
('slaughterer', 'الشركة الوطنية للدواجن', 0),
('tailor', 'شركة الأقمشة', 0),
('taxi', 'تاكسي', 0),
('unemployed', 'عاطل', 0),
('vegetables', 'شركة الخضروات', 0),
('bricks', 'شركة الأسمنت', 0),
('pizzajob', 'شركة أدم للتوصيل السريع', 0),
('mnahel', 'مناحل العسل الذهبي', 0),
('tomot', 'شركة ابو علي للتمور', 0),
('waterr', 'شركة حمني للمياه', 0),
('milker', 'شركة المراعي للحليب', 0);

-- --------------------------------------------------------

--
-- Table structure for table `job_grades`
--

CREATE TABLE `job_grades` (
  `id` int(11) NOT NULL,
  `job_name` varchar(50) DEFAULT NULL,
  `grade` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `label` varchar(50) NOT NULL,
  `salary` int(11) NOT NULL,
  `skin_male` longtext NOT NULL,
  `skin_female` longtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `job_grades`
--

INSERT INTO `job_grades` (`id`, `job_name`, `grade`, `name`, `label`, `salary`, `skin_male`, `skin_female`) VALUES
(1, 'unemployed', 0, 'unemployed', 'غير موظف', 750, '{}', '{}'),
(2, 'cardealer', 0, 'recruit', 'Recrue', 10, '{}', '{}'),
(3, 'cardealer', 1, 'novice', 'Novice', 25, '{}', '{}'),
(4, 'cardealer', 2, 'experienced', 'Experimente', 40, '{}', '{}'),
(5, 'cardealer', 3, 'boss', 'Patron', 0, '{}', '{}'),
(6, 'lumberjack', 0, 'employee', 'موظف', 1100, '{\"tshirt_2\":1,\"ears_1\":5,\"glasses_1\":0,\"torso_2\":1,\"ears_2\":3,\"glasses_0\":,\"shoes_2\":6,\"pants_1\":0,\"shoes_1\":12,\"bags_1\":0,\"helmet_2\":7,\"pants_2\":12,\"torso_1\":22,\"tshirt_1\":59,\"arms\":1,\"bags_2\":0,\"helmet_1\":0}', '{}'),
(7, 'fisherman', 0, 'employee', 'موظف', 1100, '{\"tshirt_2\":0,\"ears_1\":5,\"glasses_1\":0,\"torso_2\":4,\"ears_2\":3,\"glasses_2\":0,\"shoes_2\":0,\"pants_1\":15,\"shoes_1\":16,\"bags_1\":0,\"helmet_2\":1,\"pants_2\":15,\"torso_1\":237,\"tshirt_1\":15,\"arms\":45,\"bags_2\":0,\"helmet_1\":14}', '{}'),
(8, 'fueler', 0, 'employee', 'موظف', 1100, '{\"tshirt_2\":1,\"ears_1\":8,\"glasses_1\":16,\"torso_2\":0,\"ears_2\":2,\"glasses_2\":0,\"shoes_2\":1,\"pants_1\":75,\"shoes_1\":51,\"bags_1\":0,\"helmet_2\":0,\"pants_2\":7,\"torso_1\":89,\"tshirt_1\":59,\"arms\":1,\"bags_2\":0,\"helmet_1\":5}', '{}'),
(9, 'reporter', 0, 'employee', 'موظف', 1100, '{\"tshirt_2\":0,\"shoes_1\":25,\"torso_2\":7,\"pants_1\":31,\"tshirt_1\":15,\"helmet_1\":63,\"helmet_2\":9,\"arms\":12,\"torso_1\":118,\"pants_2\":0}', '{}'),
(10, 'tailor', 0, 'employee', 'موظف', 1100, '{\"mask_1\":0,\"arms\":1,\"glasses_1\":0,\"hair_color_2\":4,\"makeup_1\":0,\"face\":19,\"glasses\":0,\"mask_2\":0,\"makeup_3\":0,\"skin\":29,\"helmet_2\":0,\"lipstick_4\":0,\"sex\":0,\"torso_1\":24,\"makeup_2\":0,\"bags_2\":0,\"chain_2\":0,\"ears_1\":-1,\"bags_1\":0,\"bproof_1\":0,\"shoes_2\":0,\"lipstick_2\":0,\"chain_1\":0,\"tshirt_1\":0,\"eyebrows_3\":0,\"pants_2\":0,\"beard_4\":0,\"torso_2\":0,\"beard_2\":6,\"ears_2\":0,\"hair_2\":0,\"shoes_1\":36,\"tshirt_2\":0,\"beard_3\":0,\"hair_1\":2,\"hair_color_1\":0,\"pants_1\":48,\"helmet_1\":-1,\"bproof_2\":0,\"eyebrows_4\":0,\"eyebrows_2\":0,\"decals_1\":0,\"age_2\":0,\"beard_1\":5,\"shoes\":10,\"lipstick_1\":0,\"eyebrows_1\":0,\"glasses_2\":0,\"makeup_4\":0,\"decals_2\":0,\"lipstick_3\":0,\"age_1\":0}', '{\"mask_1\":0,\"arms\":5,\"glasses_1\":5,\"hair_color_2\":4,\"makeup_1\":0,\"face\":19,\"glasses\":0,\"mask_2\":0,\"makeup_3\":0,\"skin\":29,\"helmet_2\":0,\"lipstick_4\":0,\"sex\":1,\"torso_1\":52,\"makeup_2\":0,\"bags_2\":0,\"chain_2\":0,\"ears_1\":-1,\"bags_1\":0,\"bproof_1\":0,\"shoes_2\":1,\"lipstick_2\":0,\"chain_1\":0,\"tshirt_1\":23,\"eyebrows_3\":0,\"pants_2\":0,\"beard_4\":0,\"torso_2\":0,\"beard_2\":6,\"ears_2\":0,\"hair_2\":0,\"shoes_1\":42,\"tshirt_2\":4,\"beard_3\":0,\"hair_1\":2,\"hair_color_1\":0,\"pants_1\":36,\"helmet_1\":-1,\"bproof_2\":0,\"eyebrows_4\":0,\"eyebrows_2\":0,\"decals_1\":0,\"age_2\":0,\"beard_1\":5,\"shoes\":10,\"lipstick_1\":0,\"eyebrows_1\":0,\"glasses_2\":0,\"makeup_4\":0,\"decals_2\":0,\"lipstick_3\":0,\"age_1\":0}'),
(11, 'miner', 0, 'employee', 'موظف', 1100, '{\"tshirt_2\":1,\"ears_1\":8,\"glasses_1\":15,\"torso_2\":3,\"ears_2\":2,\"glasses_2\":3,\"shoes_2\":1,\"pants_1\":75,\"shoes_1\":51,\"bags_1\":0,\"helmet_2\":0,\"pants_2\":7,\"torso_1\":0,\"tshirt_1\":59,\"arms\":2,\"bags_2\":0,\"helmet_1\":0}', '{}'),
(12, 'slaughterer', 0, 'employee', 'موظف', 1100, '{\"age_1\":0,\"glasses_2\":0,\"beard_1\":5,\"decals_2\":0,\"beard_4\":0,\"shoes_2\":0,\"tshirt_2\":0,\"lipstick_2\":0,\"hair_2\":0,\"arms\":67,\"pants_1\":36,\"skin\":29,\"eyebrows_2\":0,\"shoes\":10,\"helmet_1\":-1,\"lipstick_1\":0,\"helmet_2\":0,\"hair_color_1\":0,\"glasses\":0,\"makeup_4\":0,\"makeup_1\":0,\"hair_1\":2,\"bproof_1\":0,\"bags_1\":0,\"mask_1\":0,\"lipstick_3\":0,\"chain_1\":0,\"eyebrows_4\":0,\"sex\":0,\"torso_1\":56,\"beard_2\":6,\"shoes_1\":12,\"decals_1\":0,\"face\":19,\"lipstick_4\":0,\"tshirt_1\":15,\"mask_2\":0,\"age_2\":0,\"eyebrows_3\":0,\"chain_2\":0,\"glasses_1\":0,\"ears_1\":-1,\"bags_2\":0,\"ears_2\":0,\"torso_2\":0,\"bproof_2\":0,\"makeup_2\":0,\"eyebrows_1\":0,\"makeup_3\":0,\"pants_2\":0,\"beard_3\":0,\"hair_color_2\":4}', '{\"age_1\":0,\"glasses_2\":0,\"beard_1\":5,\"decals_2\":0,\"beard_4\":0,\"shoes_2\":0,\"tshirt_2\":0,\"lipstick_2\":0,\"hair_2\":0,\"arms\":72,\"pants_1\":75,\"skin\":29,\"eyebrows_2\":0,\"shoes\":10,\"helmet_1\":-1,\"lipstick_1\":0,\"helmet_2\":0,\"hair_color_1\":0,\"glasses\":0,\"makeup_4\":0,\"makeup_1\":0,\"hair_1\":2,\"bproof_1\":0,\"bags_1\":0,\"mask_1\":0,\"lipstick_3\":0,\"chain_1\":0,\"eyebrows_4\":0,\"sex\":1,\"torso_1\":49,\"beard_2\":6,\"shoes_1\":24,\"decals_1\":0,\"face\":19,\"lipstick_4\":0,\"tshirt_1\":9,\"mask_2\":0,\"age_2\":0,\"eyebrows_3\":0,\"chain_2\":0,\"glasses_1\":5,\"ears_1\":-1,\"bags_2\":0,\"ears_2\":0,\"torso_2\":0,\"bproof_2\":0,\"makeup_2\":0,\"eyebrows_1\":0,\"makeup_3\":0,\"pants_2\":0,\"beard_3\":0,\"hair_color_2\":4}'),
(13, 'ambulance', 0, 'ambulance', 'متدرب', 5050, '{\"tshirt_2\":0,\"hair_color_1\":5,\"glasses_2\":3,\"shoes_1\":1,\"torso_2\":3,\"hair_color_2\":0,\"pants_1\":24,\"glasses_1\":4,\"hair_1\":2,\"sex\":0,\"decals_2\":0,\"tshirt_1\":15,\"helmet_1\":7,\"helmet_2\":0,\"arms\":92,\"face\":19,\"decals_1\":60,\"torso_1\":13,\"hair_2\":0,\"skin\":34,\"pants_2\":5}', '{\"tshirt_2\":3,\"decals_2\":0,\"glasses\":0,\"hair_1\":2,\"torso_1\":73,\"shoes\":1,\"hair_color_2\":0,\"glasses_1\":19,\"skin\":13,\"face\":6,\"pants_2\":5,\"tshirt_1\":75,\"pants_1\":37,\"helmet_1\":57,\"torso_2\":0,\"arms\":14,\"sex\":1,\"glasses_2\":0,\"decals_1\":0,\"hair_2\":0,\"helmet_2\":0,\"hair_color_1\":0}'),
(14, 'ambulance', 1, 'doctor', 'مستوى 1', 5500, '{\"tshirt_2\":0,\"hair_color_1\":5,\"glasses_2\":3,\"shoes_1\":1,\"torso_2\":3,\"hair_color_2\":0,\"pants_1\":24,\"glasses_1\":4,\"hair_1\":2,\"sex\":0,\"decals_2\":0,\"tshirt_1\":15,\"helmet_1\":7,\"helmet_2\":0,\"arms\":92,\"face\":19,\"decals_1\":60,\"torso_1\":13,\"hair_2\":0,\"skin\":34,\"pants_2\":5}', '{\"tshirt_2\":3,\"decals_2\":0,\"glasses\":0,\"hair_1\":2,\"torso_1\":73,\"shoes\":1,\"hair_color_2\":0,\"glasses_1\":19,\"skin\":13,\"face\":6,\"pants_2\":5,\"tshirt_1\":75,\"pants_1\":37,\"helmet_1\":57,\"torso_2\":0,\"arms\":14,\"sex\":1,\"glasses_2\":0,\"decals_1\":0,\"hair_2\":0,\"helmet_2\":0,\"hair_color_1\":0}'),
(15, 'ambulance', 2, 'chief_doctor', 'مستوى 2', 6500, '{\"tshirt_2\":0,\"hair_color_1\":5,\"glasses_2\":3,\"shoes_1\":1,\"torso_2\":3,\"hair_color_2\":0,\"pants_1\":24,\"glasses_1\":4,\"hair_1\":2,\"sex\":0,\"decals_2\":0,\"tshirt_1\":15,\"helmet_1\":7,\"helmet_2\":0,\"arms\":92,\"face\":19,\"decals_1\":60,\"torso_1\":13,\"hair_2\":0,\"skin\":34,\"pants_2\":5}', '{\"tshirt_2\":3,\"decals_2\":0,\"glasses\":0,\"hair_1\":2,\"torso_1\":73,\"shoes\":1,\"hair_color_2\":0,\"glasses_1\":19,\"skin\":13,\"face\":6,\"pants_2\":5,\"tshirt_1\":75,\"pants_1\":37,\"helmet_1\":57,\"torso_2\":0,\"arms\":14,\"sex\":1,\"glasses_2\":0,\"decals_1\":0,\"hair_2\":0,\"helmet_2\":0,\"hair_color_1\":0}'),
(16, 'ambulance', 12, 'boss', 'قائد الهلال الاحمر', 15000, '{\"tshirt_2\":0,\"hair_color_1\":5,\"glasses_2\":3,\"shoes_1\":1,\"torso_2\":3,\"hair_color_2\":0,\"pants_1\":24,\"glasses_1\":4,\"hair_1\":2,\"sex\":0,\"decals_2\":0,\"tshirt_1\":15,\"helmet_1\":7,\"helmet_2\":0,\"arms\":92,\"face\":19,\"decals_1\":60,\"torso_1\":13,\"hair_2\":0,\"skin\":34,\"pants_2\":5}', '{\"tshirt_2\":3,\"decals_2\":0,\"glasses\":0,\"hair_1\":2,\"torso_1\":73,\"shoes\":1,\"hair_color_2\":0,\"glasses_1\":19,\"skin\":13,\"face\":6,\"pants_2\":5,\"tshirt_1\":75,\"pants_1\":37,\"helmet_1\":57,\"torso_2\":0,\"arms\":14,\"sex\":1,\"glasses_2\":0,\"decals_1\":0,\"hair_2\":0,\"helmet_2\":0,\"hair_color_1\":0}'),
(17, 'mechanic', 0, 'mechanic0', 'متدرب', 750, '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":15,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', '{}'),
(18, 'mechanic', 1, 'mechanic1', 'مستوى 1', 1000, '{\"torso_2\":0,\"pants_1\":87,\"shoes_2\":0,\"pants_2\":9,\"torso_1\":11,\"tshirt_1\":143,\"arms_2\":0,\"arms\":19,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', '{}'),
(19, 'mechanic', 2, 'mechanic2', 'مستوى 2', 2000, '{\"torso_2\":1,\"pants_1\":87,\"shoes_2\":0,\"pants_2\":10,\"torso_1\":11,\"tshirt_1\":143,\"arms_2\":0,\"arms\":19,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', '{}'),
(20, 'mechanic', 3, 'mechanic3', 'مستوى 3', 3000, '{\"torso_2\":2,\"pants_1\":87,\"shoes_2\":0,\"pants_2\":11,\"torso_1\":11,\"tshirt_1\":143,\"arms_2\":0,\"arms\":19,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', '{}'),
(21, 'mechanic', 4, 'mechanic4', 'مستوى 4', 4000, '{\"torso_2\":3,\"pants_1\":87,\"shoes_2\":0,\"pants_2\":12,\"torso_1\":11,\"tshirt_1\":143,\"arms_2\":0,\"arms\":19,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', '{}'),
(22, 'admin', 1, 'low_admin', 'مشرف', 10000, '{}', '{}'),
(23, 'admin', 2, 'meduim_admin', '+مشرف', 12000, '{}', '{}'),
(24, 'admin', 3, 'high_admin', 'ادمن', 15000, '{}', '{}'),
(25, 'admin', 4, 'boss', '+ادمن', 15000, '{}', '{}'),
(26, 'farmer', 0, 'employee', 'موظف', 500, '{\"tshirt_2\":0,\"ears_1\":5,\"glasses_1\":0,\"torso_2\":8,\"ears_2\":3,\"glasses_2\":0,\"shoes_2\":0,\"pants_1\":90,\"shoes_1\":24,\"bags_1\":0,\"helmet_2\":10,\"pants_2\":8,\"torso_1\":0,\"tshirt_1\":15,\"arms\":41,\"bags_2\":0,\"helmet_1\":13}', '{}'),
(27, 'taxi', 0, 'recrue', 'موظف', 12, '{\"hair_2\":0,\"hair_color_2\":0,\"torso_1\":32,\"bags_1\":0,\"helmet_2\":0,\"chain_2\":0,\"eyebrows_3\":0,\"makeup_3\":0,\"makeup_2\":0,\"tshirt_1\":31,\"makeup_1\":0,\"bags_2\":0,\"makeup_4\":0,\"eyebrows_4\":0,\"chain_1\":0,\"lipstick_4\":0,\"bproof_2\":0,\"hair_color_1\":0,\"decals_2\":0,\"pants_2\":0,\"age_2\":0,\"glasses_2\":0,\"ears_2\":0,\"arms\":27,\"lipstick_1\":0,\"ears_1\":-1,\"mask_2\":0,\"sex\":0,\"lipstick_3\":0,\"helmet_1\":-1,\"shoes_2\":0,\"beard_2\":0,\"beard_1\":0,\"lipstick_2\":0,\"beard_4\":0,\"glasses_1\":0,\"bproof_1\":0,\"mask_1\":0,\"decals_1\":1,\"hair_1\":0,\"eyebrows_2\":0,\"beard_3\":0,\"age_1\":0,\"tshirt_2\":0,\"skin\":0,\"torso_2\":0,\"eyebrows_1\":0,\"face\":0,\"shoes_1\":10,\"pants_1\":24}', '{\"hair_2\":0,\"hair_color_2\":0,\"torso_1\":57,\"bags_1\":0,\"helmet_2\":0,\"chain_2\":0,\"eyebrows_3\":0,\"makeup_3\":0,\"makeup_2\":0,\"tshirt_1\":38,\"makeup_1\":0,\"bags_2\":0,\"makeup_4\":0,\"eyebrows_4\":0,\"chain_1\":0,\"lipstick_4\":0,\"bproof_2\":0,\"hair_color_1\":0,\"decals_2\":0,\"pants_2\":1,\"age_2\":0,\"glasses_2\":0,\"ears_2\":0,\"arms\":21,\"lipstick_1\":0,\"ears_1\":-1,\"mask_2\":0,\"sex\":1,\"lipstick_3\":0,\"helmet_1\":-1,\"shoes_2\":0,\"beard_2\":0,\"beard_1\":0,\"lipstick_2\":0,\"beard_4\":0,\"glasses_1\":5,\"bproof_1\":0,\"mask_1\":0,\"decals_1\":1,\"hair_1\":0,\"eyebrows_2\":0,\"beard_3\":0,\"age_1\":0,\"tshirt_2\":0,\"skin\":0,\"torso_2\":0,\"eyebrows_1\":0,\"face\":0,\"shoes_1\":49,\"pants_1\":11}'),
(28, 'taxi', 1, 'novice', 'Novice', 24, '{\"hair_2\":0,\"hair_color_2\":0,\"torso_1\":32,\"bags_1\":0,\"helmet_2\":0,\"chain_2\":0,\"eyebrows_3\":0,\"makeup_3\":0,\"makeup_2\":0,\"tshirt_1\":31,\"makeup_1\":0,\"bags_2\":0,\"makeup_4\":0,\"eyebrows_4\":0,\"chain_1\":0,\"lipstick_4\":0,\"bproof_2\":0,\"hair_color_1\":0,\"decals_2\":0,\"pants_2\":0,\"age_2\":0,\"glasses_2\":0,\"ears_2\":0,\"arms\":27,\"lipstick_1\":0,\"ears_1\":-1,\"mask_2\":0,\"sex\":0,\"lipstick_3\":0,\"helmet_1\":-1,\"shoes_2\":0,\"beard_2\":0,\"beard_1\":0,\"lipstick_2\":0,\"beard_4\":0,\"glasses_1\":0,\"bproof_1\":0,\"mask_1\":0,\"decals_1\":1,\"hair_1\":0,\"eyebrows_2\":0,\"beard_3\":0,\"age_1\":0,\"tshirt_2\":0,\"skin\":0,\"torso_2\":0,\"eyebrows_1\":0,\"face\":0,\"shoes_1\":10,\"pants_1\":24}', '{\"hair_2\":0,\"hair_color_2\":0,\"torso_1\":57,\"bags_1\":0,\"helmet_2\":0,\"chain_2\":0,\"eyebrows_3\":0,\"makeup_3\":0,\"makeup_2\":0,\"tshirt_1\":38,\"makeup_1\":0,\"bags_2\":0,\"makeup_4\":0,\"eyebrows_4\":0,\"chain_1\":0,\"lipstick_4\":0,\"bproof_2\":0,\"hair_color_1\":0,\"decals_2\":0,\"pants_2\":1,\"age_2\":0,\"glasses_2\":0,\"ears_2\":0,\"arms\":21,\"lipstick_1\":0,\"ears_1\":-1,\"mask_2\":0,\"sex\":1,\"lipstick_3\":0,\"helmet_1\":-1,\"shoes_2\":0,\"beard_2\":0,\"beard_1\":0,\"lipstick_2\":0,\"beard_4\":0,\"glasses_1\":5,\"bproof_1\":0,\"mask_1\":0,\"decals_1\":1,\"hair_1\":0,\"eyebrows_2\":0,\"beard_3\":0,\"age_1\":0,\"tshirt_2\":0,\"skin\":0,\"torso_2\":0,\"eyebrows_1\":0,\"face\":0,\"shoes_1\":49,\"pants_1\":11}'),
(29, 'taxi', 2, 'experimente', 'Experimente', 36, '{\"hair_2\":0,\"hair_color_2\":0,\"torso_1\":26,\"bags_1\":0,\"helmet_2\":0,\"chain_2\":0,\"eyebrows_3\":0,\"makeup_3\":0,\"makeup_2\":0,\"tshirt_1\":57,\"makeup_1\":0,\"bags_2\":0,\"makeup_4\":0,\"eyebrows_4\":0,\"chain_1\":0,\"lipstick_4\":0,\"bproof_2\":0,\"hair_color_1\":0,\"decals_2\":0,\"pants_2\":4,\"age_2\":0,\"glasses_2\":0,\"ears_2\":0,\"arms\":11,\"lipstick_1\":0,\"ears_1\":-1,\"mask_2\":0,\"sex\":0,\"lipstick_3\":0,\"helmet_1\":-1,\"shoes_2\":0,\"beard_2\":0,\"beard_1\":0,\"lipstick_2\":0,\"beard_4\":0,\"glasses_1\":0,\"bproof_1\":0,\"mask_1\":0,\"decals_1\":0,\"hair_1\":0,\"eyebrows_2\":0,\"beard_3\":0,\"age_1\":0,\"tshirt_2\":0,\"skin\":0,\"torso_2\":0,\"eyebrows_1\":0,\"face\":0,\"shoes_1\":10,\"pants_1\":24}', '{\"hair_2\":0,\"hair_color_2\":0,\"torso_1\":57,\"bags_1\":0,\"helmet_2\":0,\"chain_2\":0,\"eyebrows_3\":0,\"makeup_3\":0,\"makeup_2\":0,\"tshirt_1\":38,\"makeup_1\":0,\"bags_2\":0,\"makeup_4\":0,\"eyebrows_4\":0,\"chain_1\":0,\"lipstick_4\":0,\"bproof_2\":0,\"hair_color_1\":0,\"decals_2\":0,\"pants_2\":1,\"age_2\":0,\"glasses_2\":0,\"ears_2\":0,\"arms\":21,\"lipstick_1\":0,\"ears_1\":-1,\"mask_2\":0,\"sex\":1,\"lipstick_3\":0,\"helmet_1\":-1,\"shoes_2\":0,\"beard_2\":0,\"beard_1\":0,\"lipstick_2\":0,\"beard_4\":0,\"glasses_1\":5,\"bproof_1\":0,\"mask_1\":0,\"decals_1\":1,\"hair_1\":0,\"eyebrows_2\":0,\"beard_3\":0,\"age_1\":0,\"tshirt_2\":0,\"skin\":0,\"torso_2\":0,\"eyebrows_1\":0,\"face\":0,\"shoes_1\":49,\"pants_1\":11}'),
(30, 'taxi', 3, 'uber', 'Uber', 48, '{\"hair_2\":0,\"hair_color_2\":0,\"torso_1\":26,\"bags_1\":0,\"helmet_2\":0,\"chain_2\":0,\"eyebrows_3\":0,\"makeup_3\":0,\"makeup_2\":0,\"tshirt_1\":57,\"makeup_1\":0,\"bags_2\":0,\"makeup_4\":0,\"eyebrows_4\":0,\"chain_1\":0,\"lipstick_4\":0,\"bproof_2\":0,\"hair_color_1\":0,\"decals_2\":0,\"pants_2\":4,\"age_2\":0,\"glasses_2\":0,\"ears_2\":0,\"arms\":11,\"lipstick_1\":0,\"ears_1\":-1,\"mask_2\":0,\"sex\":0,\"lipstick_3\":0,\"helmet_1\":-1,\"shoes_2\":0,\"beard_2\":0,\"beard_1\":0,\"lipstick_2\":0,\"beard_4\":0,\"glasses_1\":0,\"bproof_1\":0,\"mask_1\":0,\"decals_1\":0,\"hair_1\":0,\"eyebrows_2\":0,\"beard_3\":0,\"age_1\":0,\"tshirt_2\":0,\"skin\":0,\"torso_2\":0,\"eyebrows_1\":0,\"face\":0,\"shoes_1\":10,\"pants_1\":24}', '{\"hair_2\":0,\"hair_color_2\":0,\"torso_1\":57,\"bags_1\":0,\"helmet_2\":0,\"chain_2\":0,\"eyebrows_3\":0,\"makeup_3\":0,\"makeup_2\":0,\"tshirt_1\":38,\"makeup_1\":0,\"bags_2\":0,\"makeup_4\":0,\"eyebrows_4\":0,\"chain_1\":0,\"lipstick_4\":0,\"bproof_2\":0,\"hair_color_1\":0,\"decals_2\":0,\"pants_2\":1,\"age_2\":0,\"glasses_2\":0,\"ears_2\":0,\"arms\":21,\"lipstick_1\":0,\"ears_1\":-1,\"mask_2\":0,\"sex\":1,\"lipstick_3\":0,\"helmet_1\":-1,\"shoes_2\":0,\"beard_2\":0,\"beard_1\":0,\"lipstick_2\":0,\"beard_4\":0,\"glasses_1\":5,\"bproof_1\":0,\"mask_1\":0,\"decals_1\":1,\"hair_1\":0,\"eyebrows_2\":0,\"beard_3\":0,\"age_1\":0,\"tshirt_2\":0,\"skin\":0,\"torso_2\":0,\"eyebrows_1\":0,\"face\":0,\"shoes_1\":49,\"pants_1\":11}'),
(31, 'taxi', 4, 'boss', 'Patron', 0, '{\"hair_2\":0,\"hair_color_2\":0,\"torso_1\":29,\"bags_1\":0,\"helmet_2\":0,\"chain_2\":0,\"eyebrows_3\":0,\"makeup_3\":0,\"makeup_2\":0,\"tshirt_1\":31,\"makeup_1\":0,\"bags_2\":0,\"makeup_4\":0,\"eyebrows_4\":0,\"chain_1\":0,\"lipstick_4\":0,\"bproof_2\":0,\"hair_color_1\":0,\"decals_2\":0,\"pants_2\":4,\"age_2\":0,\"glasses_2\":0,\"ears_2\":0,\"arms\":1,\"lipstick_1\":0,\"ears_1\":-1,\"mask_2\":0,\"sex\":0,\"lipstick_3\":0,\"helmet_1\":-1,\"shoes_2\":0,\"beard_2\":0,\"beard_1\":0,\"lipstick_2\":0,\"beard_4\":0,\"glasses_1\":0,\"bproof_1\":0,\"mask_1\":0,\"decals_1\":0,\"hair_1\":0,\"eyebrows_2\":0,\"beard_3\":0,\"age_1\":0,\"tshirt_2\":0,\"skin\":0,\"torso_2\":4,\"eyebrows_1\":0,\"face\":0,\"shoes_1\":10,\"pants_1\":24}', '{\"hair_2\":0,\"hair_color_2\":0,\"torso_1\":57,\"bags_1\":0,\"helmet_2\":0,\"chain_2\":0,\"eyebrows_3\":0,\"makeup_3\":0,\"makeup_2\":0,\"tshirt_1\":38,\"makeup_1\":0,\"bags_2\":0,\"makeup_4\":0,\"eyebrows_4\":0,\"chain_1\":0,\"lipstick_4\":0,\"bproof_2\":0,\"hair_color_1\":0,\"decals_2\":0,\"pants_2\":1,\"age_2\":0,\"glasses_2\":0,\"ears_2\":0,\"arms\":21,\"lipstick_1\":0,\"ears_1\":-1,\"mask_2\":0,\"sex\":1,\"lipstick_3\":0,\"helmet_1\":-1,\"shoes_2\":0,\"beard_2\":0,\"beard_1\":0,\"lipstick_2\":0,\"beard_4\":0,\"glasses_1\":5,\"bproof_1\":0,\"mask_1\":0,\"decals_1\":1,\"hair_1\":0,\"eyebrows_2\":0,\"beard_3\":0,\"age_1\":0,\"tshirt_2\":0,\"skin\":0,\"torso_2\":0,\"eyebrows_1\":0,\"face\":0,\"shoes_1\":49,\"pants_1\":11}'),
(32, 'police', 0, 'recruit', 'جندي', 2000, '{}', '{}'),
(33, 'police', 1, 'officer', 'جندي أول', 3000, '{}', '{}'),
(34, 'police', 2, 'sergeant', 'عريف', 3500, '{}', '{}'),
(35, 'police', 3, 'intendent', 'وكيل رقيب', 4000, '{}', '{}'),
(36, 'police', 4, 'lieutenant', 'رقيب', 4500, '{}', '{}'),
(37, 'police', 5, 'chef', 'رقيب اول', 5000, '{}', '{}'),
(38, 'police', 6, 'inspector', 'رئيس رقباء', 5500, '{}', '{}'),
(39, 'police', 7, 'mola', 'ملازم', 6000, '{}', '{}'),
(40, 'police', 8, 'captain', 'ملازم أول', 6500, '{}', '{}'),
(41, 'police', 9, 'deputy', 'نقيب', 7000, '{}', '{}'),
(42, 'police', 10, 'sandy2', 'رائد', 7500, '{}', '{}'),
(43, 'police', 11, 'fbi0', 'مقدم', 8000, '{}', '{}'),
(45, 'police', 12, 'fbi2', 'عقيد', 9000, '{}', '{}'),
(47, 'police', 13, 'special', 'عميد', 10000, '{}', '{}'),
(49, 'police', 14, 'fbi3', 'لواء', 11000, '{}', '{}'),
(51, 'police', 120, 'fbi5', 'فريق', 12000, '{}', '{}'),
(53, 'police', 122, 'sandy0', 'فريق اول', 15000, '{}', '{}'),
(55, 'agent', 0, 'recruit', 'جندي', 1000, '{}', '{}'),
(56, 'agent', 1, 'officer', 'جندي اول', 1500, '{}', '{}'),
(57, 'agent', 2, 'sergeant', 'عريف', 2000, '{}', '{}'),
(58, 'agent', 3, 'intendent', 'رقيب', 2500, '{}', '{}'),
(59, 'agent', 4, 'lieutenant', 'وكيل رقيب', 3000, '{}', '{}'),
(60, 'agent', 5, 'chef', 'رقيب اول', 3500, '{}', '{}'),
(61, 'agent', 6, 'inspector', 'رئيس رقباء', 4000, '{}', '{}'),
(62, 'agent', 7, 'bigboss', 'ملازم', 4500, '{}', '{}'),
(63, 'agent', 8, 'captain', 'ملازم أول', 5000, '{}', '{}'),
(64, 'police', 23, 'bosstwo', 'نائب قائد الشرطة', 15000, '{}', '{}'),
(65, 'police', 24, 'boss', 'قائد الشرطة', 16000, '{}', '{}'),
(66, 'agent', 9, 'sany', 'نقيب', 5500, '{}', '{}'),
(67, 'agent', 10, 'sany1', 'رائد', 6000, '{}', '{}'),
(68, 'agent', 11, 'sany2', 'مقدم', 6500, '{}', '{}'),
(69, 'agent', 12, 'sany3', 'عقيد', 7000, '{}', '{}'),
(70, 'agent', 13, 'sany4', 'عميد', 7500, '{}', '{}'),
(71, 'agent', 14, 'high', 'لواء', 8000, '{}', '{}'),
(72, 'agent', 15, 'bosstwo', 'نائب قائد أمن المنشآت', 8500, '{}', '{}'),
(73, 'agent', 16, 'boss', 'قائد أمن المنشآت', 9000, '{}', '{}'),
(76, 'admin', 0, 'recruit', 'الدعم الفني', 200, '{}', '{}'),
(77, 'ambulance', 3, 'chief', 'مستوى 3', 7000, '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}'),
(78, 'ambulance', 4, 'bigchef', 'مستوى 4', 7550, '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}'),
(79, 'ambulance', 11, 'bosstwo', 'نائب قائد الهلال الاحمر', 14000, '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}'),
(80, 'ambulance', 5, 'bigchef2', 'مستوى 5', 8050, '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}'),
(81, 'ambulance', 6, 'bigchef3', 'مستوى 6', 8500, '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}'),
(82, 'ambulance', 7, 'bigchef4', 'مستوى 7', 9500, '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}'),
(83, 'ambulance', 8, 'bigchef5', 'مستوى 8', 10000, '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}'),
(84, 'ambulance', 9, 'bigchef6', 'مستوى 9', 10800, '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}'),
(85, 'ambulance', 10, 'bigchef7', 'مستوى 10', 11100, '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}'),
(86, 'vegetables', 0, 'employee', 'موظف', 500, '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":90,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', ''),
(87, 'mechanic', 5, 'mechanic5', 'مستوى 5', 5000, '{\"torso_2\":0,\"pants_1\":87,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":14,\"tshirt_1\":143,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\" :27,\"helmet_1\":45,\"helmet_2\":1}', ''),
(88, 'mechanic', 6, 'mechanic6', 'مستوى 6', 6000, '{\"torso_2\":1,\"pants_1\":87,\"shoes_2\":0,\"pants_2\":5,\"torso_1\":14,\"tshirt_1\":143,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\" :27,\"helmet_1\":45,\"helmet_2\":1}', ''),
(89, 'mechanic', 7, 'mechanic7', 'مستوى 7', 7000, '{\"torso_2\":2,\"pants_1\":87,\"shoes_2\":0,\"pants_2\":6,\"torso_1\":14,\"tshirt_1\":143,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\" :27,\"helmet_1\":45,\"helmet_2\":1}', ''),
(90, 'mechanic', 8, 'mechanic8', 'مستوى 8', 8000, '{\"torso_2\":9,\"pants_1\":87,\"shoes_2\":0,\"pants_2\":8,\"torso_1\":14,\"tshirt_1\":143,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\" :27,\"helmet_1\":45,\"helmet_2\":1}', ''),
(91, 'mechanic', 9, 'mechanic9', 'مستوى 9', 9000, '{\"torso_2\":4,\"pants_1\":87,\"shoes_2\":0,\"pants_2\":3,\"torso_1\":14,\"tshirt_1\":143,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\" :27,\"helmet_1\":45,\"helmet_2\":1}', ''),
(92, 'mechanic', 11, 'bosstwo', 'نائب مدير كراج الميكانيكي', 10000, '{\"torso_2\":5,\"pants_1\":87,\"shoes_2\":0,\"pants_2\":2,\"torso_1\":14,\"tshirt_1\":143,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\" :27,\"helmet_1\":45,\"helmet_2\":1}', ''),
(93, 'mechanic', 12, 'boss', 'مدير كراج الميكانيكي', 11500, '{\"torso_2\":6,\"pants_1\":87,\"shoes_2\":0,\"pants_2\":2,\"torso_1\":14,\"tshirt_1\":143,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', ''),
(96, 'dynasty', 0, 'empleado', 'موظف', 0, '', ''),
(97, 'storeary', 0, 'unemployed', 'موظف', 750, '', ''),
(98, 'police', 17, 'bosstwo', 'نائب قائد الشرطة', 20000, '', ''),
(99, 'police', 18, 'boss', 'قائد الشرطة', 22000, '', ''),
(100, 'cement', 0, 'employee', 'موظف', 100, '', ''),
(101, 'fork', 0, 'employee', 'موظف', 100, '', ''),
(103, 'bread', 0, 'bread', 'موظف', 250, '', ''),
(104, 'police', 101, 'm8dmrkn', 'مقدم ركن', 8500, '', ''),
(105, 'police', 102, 'akedrkn', 'عقيد ركن', 9500, '', ''),
(106, 'police', 103, 'amedrkn', 'عميد ركن', 10500, '', ''),
(107, 'police', 103, 'lwarkn', 'لواء ركن', 11500, '', ''),
(108, 'police', 104, 'fre8rkn', 'فريق ركن', 12500, '', ''),
(109, 'police', 105, 'fre8awlrkn', 'فريق اول ركن', 13500, '', ''),
(110, 'mechanic', 10, 'mechanic10', 'مستوى 10', 0, '{\"torso_2\":1,\"pants_1\":39,\"shoes_2\":0,\"pants_2\":1,\"torso_1\":66,\"tshirt_1\":15,\"arms_2\":0,\"arms\":18,\"tshirt_2\":0,\"shoes_1\":27,\"helmet_1\":45,\"helmet_2\":1}', ''),
(111, 'bricks', 0, 'employee', 'عامل أسمنت', 1100, '{\"tshirt_1\":15,\"tshirt_2\":0,\"torso_1\":65,\"torso_2\":3,\"decals_1\":0,\"decals_2\":0,\"arms\":17,\"pants_1\":38,\"pants_2\":3,\"shoes_1\":27,\"shoes_2\":0,\"helmet_1\":60,\"helmet_2\":7,\"chain_1\":0,\"chain_2\":0,\"glasses_1\":0,\"glasses_2\":0,\"ears_1\":0,\"ears_2\":0,\"bproof_1\":0,\"bproof_2\":0}', '{\"tshirt_1\":14,\"tshirt_2\":0,\"torso_1\":59,\"torso_2\":3,\"decals_1\":0,\"decals_2\":0,\"arms\":14,\"pants_1\":34,\"pants_2\":3,\"shoes_1\":27,\"shoes_2\":0,\"helmet_1\":60,\"helmet_2\":7,\"chain_1\":0,\"chain_2\":0,\"glasses_1\":0,\"glasses_2\":0,\"ears_1\":0,\"ears_2\":0,\"bproof_1\":0,\"bproof_2\":0}'),
(112, 'mujahideen', 0, 'recruit', 'جندي', 0, '{}', '{}'),
(113, 'mujahideen', 1, 'officer', 'جندي أول', 0, '{}', '{}'),
(114, 'mujahideen', 2, 'sergeant', 'عريف', 0, '{}', '{}'),
(115, 'mujahideen', 3, 'intendent', 'وكيل رقيب', 0, '{}', '{}'),
(116, 'mujahideen', 4, 'lieutenant', 'رقيب', 0, '{}', '{}'),
(117, 'mujahideen', 5, 'chef', 'رقيب أول', 0, '{}', '{}'),
(118, 'mujahideen', 6, 'inspector', 'رئيس الرقباء', 0, '{}', '{}'),
(119, 'mujahideen', 7, 'mola', 'ملازم', 0, '{}', '{}'),
(120, 'mujahideen', 8, 'captain', 'ملازم أول', 0, '{}', '{}'),
(121, 'mujahideen', 9, 'deputy', 'نقيب', 0, '{}', '{}'),
(122, 'mujahideen', 10, 'sandy2', 'رائد', 0, '{}', '{}'),
(123, 'mujahideen', 11, 'fbi0', 'مقدم', 0, '{}', '{}'),
(124, 'mujahideen', 12, 'fbi2', 'عقيد', 0, '{}', '{}'),
(125, 'mujahideen', 13, 'special', 'عميد', 0, '{}', '{}'),
(126, 'mujahideen', 14, 'fbi3', 'لواء', 0, '{}', '{}'),
(127, 'mujahideen', 15, 'fbi5', 'فريق', 0, '{}', '{}'),
(128, 'mujahideen', 16, 'sandy0', 'فريق أول', 0, '{}', '{}'),
(129, 'mujahideen', 17, 'bosstwo', 'نائب قائد المجاهدين', 0, '{}', '{}'),
(130, 'mujahideen', 18, 'boss', 'قائد المجاهدين', 0, '{}', '{}'),
(131, 'pizzajob', 0, 'employee', 'موظف', 500, '{}', '{}'),
(132, 'mnahel', 0, 'beekeeper', 'نحال', 400, '{"tshirt_2":0,"ears_1":5,"glasses_1":0,"torso_2":8,"ears_2":3,"glasses_2":0,"shoes_2":0,"pants_1":90,"shoes_1":24,"bags_1":0,"helmet_2":1,"pants_2":8,"torso_1":0,"tshirt_1":15,"arms":41,"bags_2":0,"helmet_1":13}', '{"tshirt_2":1,"ears_1":5,"glasses_1":0,"torso_2":1,"ears_2":3,"glasses_2":0,"shoes_2":0,"pants_1":93,"shoes_1":52,"bags_1":0,"helmet_2":1,"pants_2":0,"torso_1":0,"tshirt_1":6,"arms":46,"bags_2":0,"helmet_1":2}'),
(133, 'tomot', 0, 'employee', 'موظف', 0, '{"tshirt_2":0,"ears_1":5,"glasses_1":0,"torso_2":8,"ears_2":3,"glasses_2":0,"shoes_2":0,"pants_1":90,"shoes_1":24,"bags_1":0,"helmet_2":1,"pants_2":8,"torso_1":0,"tshirt_1":15,"arms":41,"bags_2":0,"helmet_1":13}', '{"tshirt_2":1,"ears_1":5,"glasses_1":0,"torso_2":1,"ears_2":3,"glasses_2":0,"shoes_2":0,"pants_1":93,"shoes_1":52,"bags_1":0,"helmet_2":1,"pants_2":0,"torso_1":0,"tshirt_1":6,"arms":46,"bags_2":0,"helmet_1":2}'),
(134, 'waterr', 0, 'employee', 'موظف', 0, '{"tshirt_2":0,"ears_1":5,"glasses_1":0,"torso_2":8,"ears_2":3,"glasses_2":0,"shoes_2":0,"pants_1":90,"shoes_1":24,"bags_1":0,"helmet_2":1,"pants_2":8,"torso_1":0,"tshirt_1":15,"arms":41,"bags_2":0,"helmet_1":13}', '{"tshirt_2":1,"ears_1":5,"glasses_1":0,"torso_2":1,"ears_2":3,"glasses_2":0,"shoes_2":0,"pants_1":93,"shoes_1":52,"bags_1":0,"helmet_2":1,"pants_2":0,"torso_1":0,"tshirt_1":6,"arms":46,"bags_2":0,"helmet_1":2}'),
(135, 'milker', 0, 'employee', 'موظف', 0, '{"tshirt_2":0,"ears_1":5,"glasses_1":0,"torso_2":8,"ears_2":3,"glasses_2":0,"shoes_2":0,"pants_1":90,"shoes_1":24,"bags_1":0,"helmet_2":1,"pants_2":8,"torso_1":0,"tshirt_1":15,"arms":41,"bags_2":0,"helmet_1":13}', '{"tshirt_2":1,"ears_1":5,"glasses_1":0,"torso_2":1,"ears_2":3,"glasses_2":0,"shoes_2":0,"pants_1":93,"shoes_1":52,"bags_1":0,"helmet_2":1,"pants_2":0,"torso_1":0,"tshirt_1":6,"arms":46,"bags_2":0,"helmet_1":2}');

-- --------------------------------------------------------

--
-- Table structure for table `kicks`
--

CREATE TABLE `kicks` (
  `ID` int(255) NOT NULL,
  `license` varchar(1024) NOT NULL,
  `reason` varchar(1024) NOT NULL,
  `staff_name` varchar(255) NOT NULL,
  `staff_steamid` varchar(255) NOT NULL,
  `time` varchar(255) NOT NULL,
  `community` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kofmachines`
--

CREATE TABLE `kofmachines` (
  `dono` longtext DEFAULT '',
  `machine` longtext DEFAULT NULL,
  `coins` double DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `licenses`
--

CREATE TABLE `licenses` (
  `type` varchar(60) NOT NULL,
  `label` varchar(60) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `licenses`
--

INSERT INTO `licenses` (`type`, `label`) VALUES
('dmv', 'رخصة إجتياز اختبار عملي'),
('drive', 'رخصة سيارات'),
('drive_aircraft', 'رخصة قيادة طائرة'),
('drive_bike', 'رخصة قيادة دباب'),
('drive_boat', 'رخصة قيادة قارب'),
('drive_truck', 'رخصة قيادة شاحنة'),
('visa', 'تأشيرة دخول ✅'),
('weapon', 'رخصة حمل سلاح');

-- --------------------------------------------------------

--
-- Table structure for table `notes`
--

CREATE TABLE `notes` (
  `ID` int(255) NOT NULL,
  `license` varchar(255) NOT NULL,
  `reason` varchar(255) NOT NULL,
  `staff_name` varchar(255) NOT NULL,
  `staff_steamid` varchar(255) NOT NULL,
  `time` varchar(255) NOT NULL,
  `community` varchar(255) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `owned_foodtrucks`
--

CREATE TABLE `owned_foodtrucks` (
  `identifier` varchar(250) DEFAULT NULL,
  `plate` varchar(12) NOT NULL,
  `money` int(11) DEFAULT 0,
  `ShopValue` int(11) DEFAULT NULL,
  `LastRobbery` int(11) DEFAULT 0,
  `ShopName` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `totalachat` int(11) DEFAULT 0,
  `totalwithdraw` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- Table structure for table `owned_properties`
--

CREATE TABLE `owned_properties` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `price` double NOT NULL,
  `rented` int(11) NOT NULL,
  `owner` varchar(60) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `owned_properties`
--

INSERT INTO `owned_properties` (`id`, `name`, `price`, `rented`, `owner`) VALUES
(45, 'RichardMajesticApt2', 1700000, 0, '14a3a51bb7d566d4e53a43da49700c5e98692adc');

-- --------------------------------------------------------

--
-- Table structure for table `owned_shops`
--

CREATE TABLE `owned_shops` (
  `identifier` varchar(250) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  `ShopNumber` int(11) DEFAULT 0,
  `money` int(11) DEFAULT 0,
  `ShopValue` int(11) DEFAULT NULL,
  `LastRobbery` int(11) DEFAULT 0,
  `ShopName` varchar(30) DEFAULT NULL,
  `Salvz` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Salvzz` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Salvzzz` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Salvzzzz` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Salvzzzzz` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Salvzzzzzz` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `emps` longtext DEFAULT '{}',
  `buydate` varchar(50) DEFAULT '2122-08-01 19:04:30',
  `enddate` varchar(50) DEFAULT '2222-08-01 19:04:30'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `owned_shops`
--

INSERT INTO `owned_shops` (`identifier`, `ShopNumber`, `money`, `ShopValue`, `LastRobbery`, `ShopName`, `Salvz`, `Salvzz`, `Salvzzz`, `Salvzzzz`, `Salvzzzzz`, `Salvzzzzzz`, `emps`, `buydate`, `enddate`) VALUES
('0', 1, 355618, 280000, 1624457814, '1', 18, 62, 0, 0, 5618, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 2, 357800, 280000, 1647781452, '2', 2, 12, 0, 0, 6000, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 3, 2878560, 280000, 1647780749, '3', 11, 52, 0, 0, 27880, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 4, 965060, 280000, 1666365967, '4', 6, 7, 0, 0, 45400, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 5, 3197800, 280000, 1625018856, '5', 7, 16, 0, 0, 108200, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 6, 378000, 280000, 1624928148, '6', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 7, 654246, 280000, 1661209566, '7', 11, 69, 0, 1, 111750, 3000, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 8, 350000, 280000, 1647541963, '8', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 9, 8178600, 280000, 1648048386, '9', 5, 10, 0, 0, 1500, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 10, 350000, 280000, 1549643682, '10', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 11, 350000, 280000, 1549643682, '11', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 12, 350000, 280000, 1549643682, '12', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 13, 350000, 280000, 1647531361, '13', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 14, 350000, 280000, 1549643682, '14', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 15, 350000, 280000, 1647539364, '15', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 16, 350000, 280000, 1549643682, '16', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 17, 350000, 280000, 1549643682, '17', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 18, 351500, 280000, 1549643682, '18', 1, 10, 0, 0, 1500, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 19, 350050, 280000, 1549643682, '19', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 20, 702550, 280000, 1549643682, '20', 2, 16, 0, 0, 2400, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 21, 350000, 280000, 1621529878, '21', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 22, 350000, 280000, 1621530230, '22', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 23, 350000, 280000, 1624546881, '23', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 24, 717866, 280000, 1624547148, '24', 14, 264, 0, 0, 49036, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 25, 848213, 280000, 1647579202, '25', 29, 60, 0, 11, 135826, 2000, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 26, 533910, 280000, 1624547616, '26', 19, 97, 0, 0, 185435, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 27, 351990, 280000, 1625775756, '27', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 28, 634611, 280000, 1725576393, '28', 36, 760, 0, 0, 235324, 0, '{}', '2022-10-11 17:29:04', '2022-10-25 17:29:04'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 29, 356009, 280000, 1648265523, '29', 21, 165, 0, 0, 41009, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 30, 320365, 280000, 1648257463, '30', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 31, 717036, 280000, 1648257504, '31', 4, 8, 0, 0, 1480, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 32, 0, 280000, 1648265278, '32', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('169270c6c3dad171577ab20c5b193f906bb4d3c8', 33, 86011, 280000, 1549643682, '33', 76, 337, 585000, 0, 317012, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 34, 350000, 280000, 1549643682, '34', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 35, 350000, 280000, 1549643682, '35', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 36, 350000, 280000, 1549643682, '36', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 37, 350000, 280000, 1549643682, '37', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 38, 350000, 280000, 1549643682, '38', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 39, 350000, 280000, 1549643682, '39', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 40, 350000, 280000, 1549643682, '40', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 41, 350000, 280000, 1549643682, '41', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 42, 350000, 280000, 1549643682, '42', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 43, 350000, 280000, 1549643682, '43', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 44, 350000, 280000, 1549643682, '44', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 45, 350000, 280000, 1549643682, '45', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 46, 350000, 280000, 1549643682, '46', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 47, 350000, 280000, 1549643682, '47', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 48, 350000, 280000, 1549643682, '48', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 49, 350000, 280000, 1549643682, '49', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 50, 350000, 280000, 1549643682, '50', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 51, 350000, 280000, 1549643682, '51', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 52, 350000, 280000, 1549643682, '52', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 53, 350000, 280000, 1549643682, '53', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 54, 350000, 280000, 1549643682, '54', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 55, 350000, 280000, 1549643682, '55', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 56, 350000, 280000, 1549643682, '56', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30'),
('749865b68a6a6bf12a8641101848f3c7281337fd', 57, 350000, 280000, 1549643682, '57', 0, 0, 0, 0, 0, 0, '{}', '2122-08-01 19:04:30', '2222-08-01 19:04:30');

-- --------------------------------------------------------

--
-- Table structure for table `owned_vehicles`
--

CREATE TABLE `owned_vehicles` (
  `owner` varchar(40) NOT NULL,
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'State of the car',
  `plate` varchar(12) NOT NULL,
  `vehicle` longtext DEFAULT NULL,
  `type` varchar(20) DEFAULT 'car',
  `job` varchar(20) NOT NULL DEFAULT 'civ',
  `x` varchar(255) DEFAULT '0',
  `y` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0',
  `z` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0',
  `h` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0',
  `stored` tinyint(1) NOT NULL DEFAULT 0,
  `name` varchar(225) NOT NULL DEFAULT '',
  `priceold` longtext NOT NULL,
  `levelold` longtext NOT NULL,
  `category` longtext NOT NULL,
  `modelname` longtext NOT NULL,
  `lasthouse` int(11) DEFAULT 0,
  `carseller` int(11) DEFAULT 0,
  `health` int(11) DEFAULT 0,
  `nitro` varchar(50) NOT NULL DEFAULT 'nao',
  `lockcheck` varchar(50) NOT NULL DEFAULT 'nao',
  `lastid` int(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `owned_vehicles`
--

INSERT INTO `owned_vehicles` (`owner`, `state`, `plate`, `vehicle`, `type`, `job`, `x`, `y`, `z`, `h`, `stored`, `name`, `priceold`, `levelold`, `category`, `modelname`, `lasthouse`, `carseller`, `health`, `nitro`, `lockcheck`, `lastid`) VALUES
('370af5c1f03b439803d21d19b79427f9707f46ee', 0, 'a52', '{\"bodyHealth\":1000.0,\"model\":353883353,\"plate\":\"a52\",\"engineHealth\":1000.0}', 'car', 'police', '0', '0', '0', '0', 1, 'طياره', '1', '5', '1', 'polmav', 0, 0, 0, 'nao', 'nao', NULL),
('14a3a51bb7d566d4e53a43da49700c5e98692adc', 0, 'AAA 111', '{\"bodyHealth\":1000.0,\"plate\":\"AAA 111\",\"model\":-436894885,\"engineHealth\":1000.0}', 'max99', 'CIV', '0', '0', '0', '0', 1, 'مكسيما', '200376420520689660', '100000000000000000', '1', 'max99', 0, 0, 0, 'nao', 'nao', NULL),
('370af5c1f03b439803d21d19b79427f9707f46ee', 0, 'B 52', '{\"plate\":\"B 52\",\"model\":479534920,\"bodyHealth\":996.4441528320313,\"engineHealth\":925.26171875}', 'car', 'civ', '0', '0', '0', '0', 1, 'مرسيدس', '345', '454', '1', 's560m19', 0, 0, 0, 'nao', 'nao', NULL),
('64bf93f4ae55cf7b5cd8f42e413d535088fa5196', 0, 'BAT 000', '{\"bodyHealth\":1000.0,\"model\":-**********,\"plate\":\"BAT 000\",\"engineHealth\":1000.0}', 'car', 'civ', '0', '0', '0', '0', 1, ' هايلكس', '', '', '13', 'hilux1', 0, 0, 0, 'nao', 'nao', NULL),
('64bf93f4ae55cf7b5cd8f42e413d535088fa5196', 0, 'HGX 214', '{\"engineHealth\":1000.0,\"bodyHealth\":1000.0,\"model\":930705492,\"plate\":\"HGX 214\"}', 'car', 'agent', '0', '0', '0', '0', 1, ' تاهو', '', '', '2', 'sk2', 0, 0, 0, 'nao', 'nao', NULL),
('64bf93f4ae55cf7b5cd8f42e413d535088fa5196', 0, 'HQU 816', '{\"bodyHealth\":1000.0,\"model\":**********,\"plate\":\"HQU 816\",\"engineHealth\":1000.0}', 'car', 'admin', '0', '0', '0', '0', 1, 'يوكن', '', '', '2', 'gmc23', 0, 0, 0, 'nao', 'nao', NULL),
('9eef030b5f84df9eefcd3154fdac29df4fa63eb7', 0, 'KJF 837', '{\"bodyHealth\":1000.0,\"model\":**********,\"plate\":\"KJF 837\",\"engineHealth\":1000.0}', 'car', 'civ', '0', '0', '0', '0', 1, 'دباب بانشي - banshee87', '', '', '9', 'banshee87', 0, 0, 0, 'nao', 'nao', NULL),
('14a3a51bb7d566d4e53a43da49700c5e98692adc', 0, 'NLM 010', '{\"bodyHealth\":1000.0,\"model\":**********,\"plate\":\"NLM 010\",\"engineHealth\":1000.0}', 'car', 'police', '0', '0', '0', '0', 1, 'تاهو قيادة', '', '', '4', 'ah-police7', 0, 0, 0, 'nao', 'nao', NULL),
('14a3a51bb7d566d4e53a43da49700c5e98692adc', 0, 'QHK 798', '{\"bodyHealth\":1000.0,\"model\":**********,\"plate\":\"QHK 798\",\"engineHealth\":1000.0}', 'car', 'police', '0', '0', '0', '0', 1, 'كامري سري 2019 ', '', '', '3', 'camrypolice', 0, 0, 0, 'nao', 'nao', NULL),
('9eef030b5f84df9eefcd3154fdac29df4fa63eb7', 0, 'QOZ 434', '{\"bodyHealth\":1000.0,\"model\":-255678177,\"plate\":\"QOZ 434\",\"engineHealth\":1000.0}', 'car', 'civ', '0', '0', '0', '0', 1, 'هوندا هيابوزا - 2000', '', '', '9', 'hakuchou2', 0, 0, 0, 'nao', 'nao', NULL),
('370af5c1f03b439803d21d19b79427f9707f46ee', 0, 'QYC 013', '{\"bodyHealth\":1000.0,\"model\":-**********,\"plate\":\"QYC 013\",\"engineHealth\":1000.0}', 'car', 'civ', '0', '0', '0', '0', 1, 'فولكس فاجن فان - 1962', '', '', '14', 'type262', 0, 0, 0, 'nao', 'nao', NULL),
('9eef030b5f84df9eefcd3154fdac29df4fa63eb7', 0, 'SOQ 192', '{\"bodyHealth\":1000.0,\"model\":885421525,\"plate\":\"SOQ 192\",\"engineHealth\":1000.0}', 'car', 'civ', '0', '0', '0', '0', 1, ' دوج تشالنجر - DEMON', '', '', '5', 'demon', 0, 0, 0, 'nao', 'nao', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `owned_vehicless`
--

CREATE TABLE `owned_vehicless` (
  `owner` varchar(22) NOT NULL,
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'State of the car',
  `plate` varchar(12) NOT NULL,
  `vehicle` longtext DEFAULT NULL,
  `type` varchar(20) NOT NULL DEFAULT 'car',
  `job` varchar(20) NOT NULL,
  `x` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0',
  `y` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0',
  `z` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0',
  `h` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0',
  `health` int(11) DEFAULT 0,
  `nitro` varchar(50) NOT NULL DEFAULT 'nao',
  `lockcheck` varchar(50) NOT NULL DEFAULT 'nao',
  `lastid` int(20) DEFAULT NULL,
  `lasthouse` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_accounts`
--

CREATE TABLE `phone_accounts` (
  `app` varchar(50) NOT NULL,
  `id` varchar(80) NOT NULL,
  `name` varchar(50) NOT NULL,
  `password` varchar(50) NOT NULL,
  `birthdate` varchar(50) NOT NULL,
  `gender` varchar(50) NOT NULL,
  `interested` text NOT NULL,
  `avatar` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_ads`
--

CREATE TABLE `phone_ads` (
  `id` int(11) NOT NULL,
  `owner` varchar(255) DEFAULT NULL,
  `job` varchar(50) DEFAULT 'default',
  `author` varchar(255) DEFAULT NULL,
  `title` varchar(50) DEFAULT NULL,
  `content` varchar(512) DEFAULT NULL,
  `data` varchar(255) DEFAULT NULL,
  `image` varchar(255) NOT NULL DEFAULT '',
  `time` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_alertjobs`
--

CREATE TABLE `phone_alertjobs` (
  `id` int(11) NOT NULL,
  `job` varchar(255) NOT NULL,
  `alerts` text DEFAULT NULL,
  `date` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_app_chat`
--

CREATE TABLE `phone_app_chat` (
  `id` int(11) NOT NULL,
  `channel` varchar(20) NOT NULL,
  `message` varchar(255) NOT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_calls`
--

CREATE TABLE `phone_calls` (
  `id` int(11) NOT NULL,
  `owner` varchar(10) NOT NULL COMMENT 'Num tel proprio',
  `num` varchar(10) NOT NULL COMMENT 'Num reférence du contact',
  `incoming` int(11) NOT NULL COMMENT 'Défini si on est à l''origine de l''appels',
  `time` timestamp NOT NULL DEFAULT current_timestamp(),
  `accepts` int(11) NOT NULL COMMENT 'Appels accepter ou pas'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_chatrooms`
--

CREATE TABLE `phone_chatrooms` (
  `id` int(10) UNSIGNED NOT NULL,
  `room_code` varchar(10) NOT NULL,
  `room_name` varchar(15) NOT NULL,
  `room_owner_id` varchar(50) DEFAULT NULL,
  `room_owner_name` varchar(60) DEFAULT NULL,
  `room_members` text DEFAULT NULL,
  `room_pin` varchar(50) DEFAULT NULL,
  `unpaid_balance` decimal(10,2) DEFAULT 0.00,
  `is_masked` tinyint(1) DEFAULT 0,
  `is_pinned` tinyint(1) DEFAULT 0,
  `created` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_chatroom_messages`
--

CREATE TABLE `phone_chatroom_messages` (
  `id` int(10) UNSIGNED NOT NULL,
  `room_id` int(10) UNSIGNED DEFAULT NULL,
  `member_id` varchar(50) DEFAULT NULL,
  `member_name` varchar(50) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `is_pinned` tinyint(1) DEFAULT 0,
  `created` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_chats`
--

CREATE TABLE `phone_chats` (
  `app` varchar(50) NOT NULL,
  `author` varchar(50) NOT NULL,
  `number` varchar(50) NOT NULL,
  `created` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_ch_reddit`
--

CREATE TABLE `phone_ch_reddit` (
  `id` int(11) NOT NULL,
  `redgkit` varchar(20) NOT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_contacts`
--

CREATE TABLE `phone_contacts` (
  `id` int(11) NOT NULL,
  `owner` varchar(255) DEFAULT NULL,
  `number` varchar(255) DEFAULT NULL,
  `name` varchar(255) NOT NULL DEFAULT 'Unknown',
  `photo` varchar(512) DEFAULT '',
  `tag` varchar(255) DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_darkgroups`
--

CREATE TABLE `phone_darkgroups` (
  `id` int(11) NOT NULL,
  `invitecode` varchar(50) DEFAULT NULL,
  `owner` varchar(255) DEFAULT NULL,
  `name` varchar(50) DEFAULT NULL,
  `photo` varchar(512) NOT NULL DEFAULT '',
  `maxmembers` int(11) DEFAULT 0,
  `members` mediumtext NOT NULL,
  `bannedmembers` mediumtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_darkmessages`
--

CREATE TABLE `phone_darkmessages` (
  `from` varchar(255) DEFAULT NULL,
  `to` int(11) DEFAULT NULL,
  `message` varchar(512) DEFAULT NULL,
  `attachments` mediumtext NOT NULL DEFAULT '[]',
  `time` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_groups`
--

CREATE TABLE `phone_groups` (
  `id` int(11) NOT NULL,
  `owner` varchar(255) DEFAULT NULL,
  `name` varchar(50) DEFAULT NULL,
  `photo` varchar(512) NOT NULL DEFAULT '',
  `members` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_invoices`
--

CREATE TABLE `phone_invoices` (
  `id` int(10) NOT NULL,
  `citizenid` varchar(50) DEFAULT NULL,
  `amount` int(11) NOT NULL DEFAULT 0,
  `society` tinytext DEFAULT NULL,
  `sender` varchar(50) DEFAULT NULL,
  `sendercitizenid` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_mail`
--

CREATE TABLE `phone_mail` (
  `id` int(11) NOT NULL,
  `owner` varchar(128) DEFAULT NULL,
  `subject` varchar(50) DEFAULT NULL,
  `starred` tinyint(1) NOT NULL DEFAULT 0,
  `mail` longtext DEFAULT NULL,
  `trash` tinyint(1) NOT NULL DEFAULT 0,
  `muted` tinyint(1) NOT NULL DEFAULT 0,
  `lastOpened` bigint(20) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_mailaccounts`
--

CREATE TABLE `phone_mailaccounts` (
  `address` varchar(50) DEFAULT NULL,
  `name` varchar(50) DEFAULT NULL,
  `password` varchar(60) DEFAULT NULL,
  `photo` varchar(255) DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_messages`
--

CREATE TABLE `phone_messages` (
  `id` int(11) NOT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `number` varchar(50) NOT NULL,
  `owner` varchar(50) DEFAULT NULL,
  `messages` text NOT NULL,
  `type` varchar(50) DEFAULT NULL,
  `read` int(11) DEFAULT NULL,
  `created` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_notifies`
--

CREATE TABLE `phone_notifies` (
  `id` int(11) NOT NULL,
  `phone` varchar(50) NOT NULL,
  `msg_content` text NOT NULL,
  `msg_head` varchar(50) NOT NULL DEFAULT '',
  `app_name` text NOT NULL,
  `msg_time` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_numbers`
--

CREATE TABLE `phone_numbers` (
  `identifier` varchar(200) NOT NULL DEFAULT '',
  `data` text DEFAULT NULL,
  `defaultNumber` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_reddit`
--

CREATE TABLE `phone_reddit` (
  `id` int(11) NOT NULL,
  `redgkit` varchar(20) DEFAULT NULL,
  `reditsage` varchar(255) DEFAULT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_shops`
--

CREATE TABLE `phone_shops` (
  `id` int(11) NOT NULL,
  `store` varchar(255) NOT NULL,
  `item` varchar(100) NOT NULL,
  `price` int(11) NOT NULL,
  `label` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_transactions`
--

CREATE TABLE `phone_transactions` (
  `id` int(11) NOT NULL,
  `from` varchar(255) DEFAULT NULL,
  `to` varchar(255) DEFAULT NULL,
  `amount` int(11) NOT NULL DEFAULT 0,
  `time` bigint(20) DEFAULT NULL,
  `reason` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_tweets`
--

CREATE TABLE `phone_tweets` (
  `id` int(11) NOT NULL,
  `reply` int(11) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `author` varchar(255) DEFAULT NULL,
  `authorimg` varchar(255) DEFAULT NULL,
  `authorrank` varchar(255) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `content` varchar(255) DEFAULT NULL,
  `image` varchar(255) NOT NULL DEFAULT '',
  `views` int(11) NOT NULL DEFAULT 0,
  `likes` int(11) NOT NULL DEFAULT 0,
  `time` bigint(20) DEFAULT NULL,
  `likers` longtext NOT NULL DEFAULT '[]'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_twitteraccounts`
--

CREATE TABLE `phone_twitteraccounts` (
  `nickname` varchar(50) DEFAULT NULL,
  `email` varchar(50) DEFAULT NULL,
  `password` varchar(60) DEFAULT NULL,
  `picture` varchar(512) DEFAULT NULL,
  `rank` varchar(50) NOT NULL DEFAULT 'default'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `phone_users_contacts`
--

CREATE TABLE `phone_users_contacts` (
  `id` int(11) NOT NULL,
  `identifier` varchar(60) DEFAULT NULL,
  `number` varchar(10) DEFAULT NULL,
  `display` varchar(64) NOT NULL DEFAULT '-1'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `playerhousing`
--

CREATE TABLE `playerhousing` (
  `id` int(32) NOT NULL,
  `owner` varchar(50) DEFAULT NULL,
  `rented` tinyint(1) DEFAULT NULL,
  `price` int(32) DEFAULT NULL,
  `wardrobe` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `players`
--

CREATE TABLE `players` (
  `ID` int(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `license` varchar(255) NOT NULL,
  `steam` varchar(255) NOT NULL,
  `discord` varchar(255) DEFAULT NULL,
  `playtime` int(255) NOT NULL DEFAULT 1,
  `firstjoined` varchar(255) NOT NULL,
  `lastplayed` varchar(255) NOT NULL,
  `community` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `player_contacts`
--

CREATE TABLE `player_contacts` (
  `id` int(11) NOT NULL,
  `identifier` varchar(50) DEFAULT NULL,
  `name` varchar(50) DEFAULT NULL,
  `number` varchar(50) DEFAULT NULL,
  `iban` varchar(50) NOT NULL DEFAULT '0',
  `display` varchar(50) DEFAULT NULL,
  `note` text NOT NULL,
  `pp` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `player_gallery`
--

CREATE TABLE `player_gallery` (
  `id` int(11) NOT NULL,
  `identifier` text NOT NULL,
  `resim` text NOT NULL,
  `data` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `player_mails`
--

CREATE TABLE `player_mails` (
  `id` int(11) NOT NULL,
  `identifier` varchar(50) DEFAULT NULL,
  `sender` varchar(50) DEFAULT NULL,
  `subject` varchar(50) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `read` tinyint(4) DEFAULT 0,
  `mailid` int(11) DEFAULT NULL,
  `date` timestamp NULL DEFAULT current_timestamp(),
  `button` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `player_notes`
--

CREATE TABLE `player_notes` (
  `id` int(11) NOT NULL,
  `identifier` text NOT NULL,
  `baslik` text NOT NULL,
  `aciklama` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `properties`
--

CREATE TABLE `properties` (
  `id` int(11) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `label` varchar(255) DEFAULT NULL,
  `entering` varchar(255) DEFAULT NULL,
  `exit` varchar(255) DEFAULT NULL,
  `inside` varchar(255) DEFAULT NULL,
  `outside` varchar(255) DEFAULT NULL,
  `ipls` varchar(255) DEFAULT '[]',
  `gateway` varchar(255) DEFAULT NULL,
  `is_single` int(11) DEFAULT NULL,
  `is_room` int(11) DEFAULT NULL,
  `is_gateway` int(11) DEFAULT NULL,
  `room_menu` varchar(255) DEFAULT NULL,
  `price` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `properties`
--

INSERT INTO `properties` (`id`, `name`, `label`, `entering`, `exit`, `inside`, `outside`, `ipls`, `gateway`, `is_single`, `is_room`, `is_gateway`, `room_menu`, `price`) VALUES
(1, 'WhispymoundDrive', '2677 Whispymound Drive', '{\"y\":564.89,\"z\":182.959,\"x\":119.384}', '{\"x\":117.347,\"y\":559.506,\"z\":183.304}', '{\"y\":557.032,\"z\":183.301,\"x\":118.037}', '{\"y\":567.798,\"z\":182.131,\"x\":119.249}', '[]', NULL, 1, 1, 0, '{\"x\":118.748,\"y\":566.573,\"z\":175.697}', 1500000),
(2, 'NorthConkerAvenue2045', '2045 North Conker Avenue', '{\"x\":372.796,\"y\":428.327,\"z\":144.685}', '{\"x\":373.548,\"y\":422.982,\"z\":144.907}', '{\"y\":420.075,\"z\":145.904,\"x\":372.161}', '{\"x\":372.454,\"y\":432.886,\"z\":143.443}', '[]', NULL, 1, 1, 0, '{\"x\":377.349,\"y\":429.422,\"z\":137.3}', 1500000),
(3, 'RichardMajesticApt2', 'Richard Majestic, Apt 2', '{\"y\":-379.165,\"z\":37.961,\"x\":-936.363}', '{\"y\":-365.476,\"z\":113.274,\"x\":-913.097}', '{\"y\":-367.637,\"z\":113.274,\"x\":-918.022}', '{\"y\":-382.023,\"z\":37.961,\"x\":-943.626}', '[]', NULL, 1, 1, 0, '{\"x\":-927.554,\"y\":-377.744,\"z\":112.674}', 1700000),
(4, 'NorthConkerAvenue2044', '2044 North Conker Avenue', '{\"y\":440.8,\"z\":146.702,\"x\":346.964}', '{\"y\":437.456,\"z\":148.394,\"x\":341.683}', '{\"y\":435.626,\"z\":148.394,\"x\":339.595}', '{\"x\":350.535,\"y\":443.329,\"z\":145.764}', '[]', NULL, 1, 1, 0, '{\"x\":337.726,\"y\":436.985,\"z\":140.77}', 1500000),
(5, 'WildOatsDrive', '3655 Wild Oats Drive', '{\"y\":502.696,\"z\":136.421,\"x\":-176.003}', '{\"y\":497.817,\"z\":136.653,\"x\":-174.349}', '{\"y\":495.069,\"z\":136.666,\"x\":-173.331}', '{\"y\":506.412,\"z\":135.0664,\"x\":-177.927}', '[]', NULL, 1, 1, 0, '{\"x\":-174.725,\"y\":493.095,\"z\":129.043}', 1500000),
(6, 'HillcrestAvenue2862', '2862 Hillcrest Avenue', '{\"y\":596.58,\"z\":142.641,\"x\":-686.554}', '{\"y\":591.988,\"z\":144.392,\"x\":-681.728}', '{\"y\":590.608,\"z\":144.392,\"x\":-680.124}', '{\"y\":599.019,\"z\":142.059,\"x\":-689.492}', '[]', NULL, 1, 1, 0, '{\"x\":-680.46,\"y\":588.6,\"z\":136.769}', 1500000),
(7, 'LowEndApartment', 'Appartement de base', '{\"y\":-1078.735,\"z\":28.4031,\"x\":292.528}', '{\"y\":-1007.152,\"z\":-102.002,\"x\":265.845}', '{\"y\":-1002.802,\"z\":-100.008,\"x\":265.307}', '{\"y\":-1078.669,\"z\":28.401,\"x\":296.738}', '[]', NULL, 1, 1, 0, '{\"x\":265.916,\"y\":-999.38,\"z\":-100.008}', 562500),
(8, 'MadWayneThunder', '2113 Mad Wayne Thunder', '{\"y\":454.955,\"z\":96.462,\"x\":-1294.433}', '{\"x\":-1289.917,\"y\":449.541,\"z\":96.902}', '{\"y\":446.322,\"z\":96.899,\"x\":-1289.642}', '{\"y\":455.453,\"z\":96.517,\"x\":-1298.851}', '[]', NULL, 1, 1, 0, '{\"x\":-1287.306,\"y\":455.901,\"z\":89.294}', 1500000),
(9, 'HillcrestAvenue2874', '2874 Hillcrest Avenue', '{\"x\":-853.346,\"y\":696.678,\"z\":147.782}', '{\"y\":690.875,\"z\":151.86,\"x\":-859.961}', '{\"y\":688.361,\"z\":151.857,\"x\":-859.395}', '{\"y\":701.628,\"z\":147.773,\"x\":-855.007}', '[]', NULL, 1, 1, 0, '{\"x\":-858.543,\"y\":697.514,\"z\":144.253}', 1500000),
(10, 'HillcrestAvenue2868', '2868 Hillcrest Avenue', '{\"y\":620.494,\"z\":141.588,\"x\":-752.82}', '{\"y\":618.62,\"z\":143.153,\"x\":-759.317}', '{\"y\":617.629,\"z\":143.153,\"x\":-760.789}', '{\"y\":621.281,\"z\":141.254,\"x\":-750.919}', '[]', NULL, 1, 1, 0, '{\"x\":-762.504,\"y\":618.992,\"z\":135.53}', 1500000),
(11, 'TinselTowersApt12', 'Tinsel Towers, Apt 42', '{\"y\":37.025,\"z\":42.58,\"x\":-618.299}', '{\"y\":58.898,\"z\":97.2,\"x\":-603.301}', '{\"y\":58.941,\"z\":97.2,\"x\":-608.741}', '{\"y\":30.603,\"z\":42.524,\"x\":-620.017}', '[]', NULL, 1, 1, 0, '{\"x\":-622.173,\"y\":54.585,\"z\":96.599}', 1700000),
(12, 'MiltonDrive', 'Milton Drive', '{\"x\":-775.17,\"y\":312.01,\"z\":84.658}', NULL, NULL, '{\"x\":-775.346,\"y\":306.776,\"z\":84.7}', '[]', NULL, 0, 0, 1, NULL, 0),
(13, 'Modern1Apartment', 'Appartement Moderne 1', NULL, '{\"x\":-784.194,\"y\":323.636,\"z\":210.997}', '{\"x\":-779.751,\"y\":323.385,\"z\":210.997}', NULL, '[\"apa_v_mp_h_01_a\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-766.661,\"y\":327.672,\"z\":210.396}', 1300000),
(14, 'Modern2Apartment', 'Appartement Moderne 2', NULL, '{\"x\":-786.8663,\"y\":315.764,\"z\":186.913}', '{\"x\":-781.808,\"y\":315.866,\"z\":186.913}', NULL, '[\"apa_v_mp_h_01_c\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-795.735,\"y\":326.757,\"z\":186.313}', 1300000),
(15, 'Modern3Apartment', 'Appartement Moderne 3', NULL, '{\"x\":-774.012,\"y\":342.042,\"z\":195.686}', '{\"x\":-779.057,\"y\":342.063,\"z\":195.686}', NULL, '[\"apa_v_mp_h_01_b\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-765.386,\"y\":330.782,\"z\":195.08}', 1300000),
(16, 'Mody1Apartment', 'Appartement Mode 1', NULL, '{\"x\":-784.194,\"y\":323.636,\"z\":210.997}', '{\"x\":-779.751,\"y\":323.385,\"z\":210.997}', NULL, '[\"apa_v_mp_h_02_a\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-766.615,\"y\":327.878,\"z\":210.396}', 1300000),
(17, 'Mody2Apartment', 'Appartement Mode 2', NULL, '{\"x\":-786.8663,\"y\":315.764,\"z\":186.913}', '{\"x\":-781.808,\"y\":315.866,\"z\":186.913}', NULL, '[\"apa_v_mp_h_02_c\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-795.297,\"y\":327.092,\"z\":186.313}', 1300000),
(18, 'Mody3Apartment', 'Appartement Mode 3', NULL, '{\"x\":-774.012,\"y\":342.042,\"z\":195.686}', '{\"x\":-779.057,\"y\":342.063,\"z\":195.686}', NULL, '[\"apa_v_mp_h_02_b\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-765.303,\"y\":330.932,\"z\":195.085}', 1300000),
(19, 'Vibrant1Apartment', 'Appartement Vibrant 1', NULL, '{\"x\":-784.194,\"y\":323.636,\"z\":210.997}', '{\"x\":-779.751,\"y\":323.385,\"z\":210.997}', NULL, '[\"apa_v_mp_h_03_a\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-765.885,\"y\":327.641,\"z\":210.396}', 1300000),
(20, 'Vibrant2Apartment', 'Appartement Vibrant 2', NULL, '{\"x\":-786.8663,\"y\":315.764,\"z\":186.913}', '{\"x\":-781.808,\"y\":315.866,\"z\":186.913}', NULL, '[\"apa_v_mp_h_03_c\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-795.607,\"y\":327.344,\"z\":186.313}', 1300000),
(21, 'Vibrant3Apartment', 'Appartement Vibrant 3', NULL, '{\"x\":-774.012,\"y\":342.042,\"z\":195.686}', '{\"x\":-779.057,\"y\":342.063,\"z\":195.686}', NULL, '[\"apa_v_mp_h_03_b\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-765.525,\"y\":330.851,\"z\":195.085}', 1300000),
(22, 'Sharp1Apartment', 'Appartement Persan 1', NULL, '{\"x\":-784.194,\"y\":323.636,\"z\":210.997}', '{\"x\":-779.751,\"y\":323.385,\"z\":210.997}', NULL, '[\"apa_v_mp_h_04_a\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-766.527,\"y\":327.89,\"z\":210.396}', 1300000),
(23, 'Sharp2Apartment', 'Appartement Persan 2', NULL, '{\"x\":-786.8663,\"y\":315.764,\"z\":186.913}', '{\"x\":-781.808,\"y\":315.866,\"z\":186.913}', NULL, '[\"apa_v_mp_h_04_c\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-795.642,\"y\":326.497,\"z\":186.313}', 1300000),
(24, 'Sharp3Apartment', 'Appartement Persan 3', NULL, '{\"x\":-774.012,\"y\":342.042,\"z\":195.686}', '{\"x\":-779.057,\"y\":342.063,\"z\":195.686}', NULL, '[\"apa_v_mp_h_04_b\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-765.503,\"y\":331.318,\"z\":195.085}', 1300000),
(25, 'Monochrome1Apartment', 'Appartement Monochrome 1', NULL, '{\"x\":-784.194,\"y\":323.636,\"z\":210.997}', '{\"x\":-779.751,\"y\":323.385,\"z\":210.997}', NULL, '[\"apa_v_mp_h_05_a\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-766.289,\"y\":328.086,\"z\":210.396}', 1300000),
(26, 'Monochrome2Apartment', 'Appartement Monochrome 2', NULL, '{\"x\":-786.8663,\"y\":315.764,\"z\":186.913}', '{\"x\":-781.808,\"y\":315.866,\"z\":186.913}', NULL, '[\"apa_v_mp_h_05_c\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-795.692,\"y\":326.762,\"z\":186.313}', 1300000),
(27, 'Monochrome3Apartment', 'Appartement Monochrome 3', NULL, '{\"x\":-774.012,\"y\":342.042,\"z\":195.686}', '{\"x\":-779.057,\"y\":342.063,\"z\":195.686}', NULL, '[\"apa_v_mp_h_05_b\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-765.094,\"y\":330.976,\"z\":195.085}', 1300000),
(28, 'Seductive1Apartment', 'Appartement Séduisant 1', NULL, '{\"x\":-784.194,\"y\":323.636,\"z\":210.997}', '{\"x\":-779.751,\"y\":323.385,\"z\":210.997}', NULL, '[\"apa_v_mp_h_06_a\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-766.263,\"y\":328.104,\"z\":210.396}', 1300000),
(29, 'Seductive2Apartment', 'Appartement Séduisant 2', NULL, '{\"x\":-786.8663,\"y\":315.764,\"z\":186.913}', '{\"x\":-781.808,\"y\":315.866,\"z\":186.913}', NULL, '[\"apa_v_mp_h_06_c\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-795.655,\"y\":326.611,\"z\":186.313}', 1300000),
(30, 'Seductive3Apartment', 'Appartement Séduisant 3', NULL, '{\"x\":-774.012,\"y\":342.042,\"z\":195.686}', '{\"x\":-779.057,\"y\":342.063,\"z\":195.686}', NULL, '[\"apa_v_mp_h_06_b\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-765.3,\"y\":331.414,\"z\":195.085}', 1300000),
(31, 'Regal1Apartment', 'Appartement Régal 1', NULL, '{\"x\":-784.194,\"y\":323.636,\"z\":210.997}', '{\"x\":-779.751,\"y\":323.385,\"z\":210.997}', NULL, '[\"apa_v_mp_h_07_a\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-765.956,\"y\":328.257,\"z\":210.396}', 1300000),
(32, 'Regal2Apartment', 'Appartement Régal 2', NULL, '{\"x\":-786.8663,\"y\":315.764,\"z\":186.913}', '{\"x\":-781.808,\"y\":315.866,\"z\":186.913}', NULL, '[\"apa_v_mp_h_07_c\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-795.545,\"y\":326.659,\"z\":186.313}', 1300000),
(33, 'Regal3Apartment', 'Appartement Régal 3', NULL, '{\"x\":-774.012,\"y\":342.042,\"z\":195.686}', '{\"x\":-779.057,\"y\":342.063,\"z\":195.686}', NULL, '[\"apa_v_mp_h_07_b\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-765.087,\"y\":331.429,\"z\":195.123}', 1300000),
(34, 'Aqua1Apartment', 'Appartement Aqua 1', NULL, '{\"x\":-784.194,\"y\":323.636,\"z\":210.997}', '{\"x\":-779.751,\"y\":323.385,\"z\":210.997}', NULL, '[\"apa_v_mp_h_08_a\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-766.187,\"y\":328.47,\"z\":210.396}', 1300000),
(35, 'Aqua2Apartment', 'Appartement Aqua 2', NULL, '{\"x\":-786.8663,\"y\":315.764,\"z\":186.913}', '{\"x\":-781.808,\"y\":315.866,\"z\":186.913}', NULL, '[\"apa_v_mp_h_08_c\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-795.658,\"y\":326.563,\"z\":186.313}', 1300000),
(36, 'Aqua3Apartment', 'Appartement Aqua 3', NULL, '{\"x\":-774.012,\"y\":342.042,\"z\":195.686}', '{\"x\":-779.057,\"y\":342.063,\"z\":195.686}', NULL, '[\"apa_v_mp_h_08_b\"]', 'MiltonDrive', 0, 1, 0, '{\"x\":-765.287,\"y\":331.084,\"z\":195.086}', 1300000),
(37, 'IntegrityWay', '4 Integrity Way', '{\"x\":-47.804,\"y\":-585.867,\"z\":36.956}', NULL, NULL, '{\"x\":-54.178,\"y\":-583.762,\"z\":35.798}', '[]', NULL, 0, 0, 1, NULL, 0),
(38, 'IntegrityWay28', '4 Integrity Way - Apt 28', NULL, '{\"x\":-31.409,\"y\":-594.927,\"z\":79.03}', '{\"x\":-26.098,\"y\":-596.909,\"z\":79.03}', NULL, '[]', 'IntegrityWay', 0, 1, 0, '{\"x\":-11.923,\"y\":-597.083,\"z\":78.43}', 1700000),
(39, 'IntegrityWay30', '4 Integrity Way - Apt 30', NULL, '{\"x\":-17.702,\"y\":-588.524,\"z\":89.114}', '{\"x\":-16.21,\"y\":-582.569,\"z\":89.114}', NULL, '[]', 'IntegrityWay', 0, 1, 0, '{\"x\":-26.327,\"y\":-588.384,\"z\":89.123}', 1700000),
(40, 'DellPerroHeights', 'Dell Perro Heights', '{\"x\":-1447.06,\"y\":-538.28,\"z\":33.74}', NULL, NULL, '{\"x\":-1440.022,\"y\":-548.696,\"z\":33.74}', '[]', NULL, 0, 0, 1, NULL, 0),
(41, 'DellPerroHeightst4', 'Dell Perro Heights - Apt 28', NULL, '{\"x\":-1452.125,\"y\":-540.591,\"z\":73.044}', '{\"x\":-1455.435,\"y\":-535.79,\"z\":73.044}', NULL, '[]', 'DellPerroHeights', 0, 1, 0, '{\"x\":-1467.058,\"y\":-527.571,\"z\":72.443}', 1700000),
(42, 'DellPerroHeightst7', 'Dell Perro Heights - Apt 30', NULL, '{\"x\":-1451.562,\"y\":-523.535,\"z\":55.928}', '{\"x\":-1456.02,\"y\":-519.209,\"z\":55.929}', NULL, '[]', 'DellPerroHeights', 0, 1, 0, '{\"x\":-1457.026,\"y\":-530.219,\"z\":55.937}', 1700000);

-- --------------------------------------------------------

--
-- Table structure for table `qalleagent_brottsregister`
--

CREATE TABLE `qalleagent_brottsregister` (
  `id` int(255) NOT NULL,
  `identifier` varchar(50) NOT NULL,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(255) NOT NULL,
  `dateofcrime` varchar(255) NOT NULL,
  `crime` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `qalleagent_brottsregister`
--

INSERT INTO `qalleagent_brottsregister` (`id`, `identifier`, `firstname`, `lastname`, `dateofcrime`, `crime`) VALUES
(26, '88f87e1ede7d8340d70efe9a3b6f48939687a4bf', 'سيف', 'عبدالله', '2024.09.05 | 15:12', 'تبادل اطلاق نار');

-- --------------------------------------------------------

--
-- Table structure for table `qaller9abh_brottsregister`
--

CREATE TABLE `qaller9abh_brottsregister` (
  `id` int(255) NOT NULL,
  `identifier` varchar(50) NOT NULL,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(255) NOT NULL,
  `dateofcrime` varchar(255) NOT NULL,
  `crime` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `qalletebexstore_brottsregister`
--

CREATE TABLE `qalletebexstore_brottsregister` (
  `id` int(255) NOT NULL,
  `identifier` varchar(50) NOT NULL,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(255) NOT NULL,
  `dateofcrime` varchar(255) NOT NULL,
  `crime` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `qalletebexstore_brottsregister`
--

INSERT INTO `qalletebexstore_brottsregister` (`id`, `identifier`, `firstname`, `lastname`, `dateofcrime`, `crime`) VALUES
(104, '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', 'صلاح', 'العنزي', '2024.09.05 | 11:36', 'ضعف خبرة 24 ساعة   '),
(105, '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', 'صلاح', 'العنزي', '2024.09.05 | 11:36', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(106, '370af5c1f03b439803d21d19b79427f9707f46ee', 'ايمن', 'العنزي', '2024.09.05 | 16:18', 'ضعف خبرة 24 ساعة   '),
(107, '370af5c1f03b439803d21d19b79427f9707f46ee', 'ايمن', 'العنزي', '2024.09.05 | 16:19', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(108, '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', 'صلاح', 'العنزي', '2024.09.05 | 16:29', 'ضعف خبرة 24 ساعة   '),
(109, '64bf93f4ae55cf7b5cd8f42e413d535088fa5196', 'صلاح', 'العنزي', '2024.09.05 | 16:35', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(110, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي برونزي مبلغ: 500000  خبرة: 500'),
(111, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي فضي مبلغ: 1000000  خبرة: 1000'),
(112, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي ذهبي  مبلغ: 2000000  خبرة: 3500'),
(113, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي بلاتيني  مبلغ: 3500000  خبرة: 5000'),
(114, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي الماسي  مبلغ: 4000000  خبرة: 6000'),
(115, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي رسمي  مبلغ: 4500000  خبرة: 8000'),
(116, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(117, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(118, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(119, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(120, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(121, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(122, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(123, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(124, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(125, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(126, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(127, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(128, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(129, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(130, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(131, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(132, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(133, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(134, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(135, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(136, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(137, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(138, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(139, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(140, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(141, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(142, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(143, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(144, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(145, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(146, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(147, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(148, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(149, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(150, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(151, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(152, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(153, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(154, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(155, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(156, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(157, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(158, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(159, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(160, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(161, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(162, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(163, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(164, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(165, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(166, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(167, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(168, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(169, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(170, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(171, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(172, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(173, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(174, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(175, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(176, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(177, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(178, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(179, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(180, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(181, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(182, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(183, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(184, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(185, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(186, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(187, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(188, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(189, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(190, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(191, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(192, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(193, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(194, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(195, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(196, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(197, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(198, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(199, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(200, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(201, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(202, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(203, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(204, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(205, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(206, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(207, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(208, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(209, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(210, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(211, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(212, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(213, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(214, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(215, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(216, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(217, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(218, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(219, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(220, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(221, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(222, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(223, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(224, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(225, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(226, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(227, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(228, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(229, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(230, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(231, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(232, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000'),
(233, '14a3a51bb7d566d4e53a43da49700c5e98692adc', 'ابو', 'قايد', '2024.09.05 | 16:51', 'راعي إستراتيجي مبلغ: 5000000  خبرة: 10000');

-- --------------------------------------------------------

--
-- Table structure for table `qalle_brottsregister`
--

CREATE TABLE `qalle_brottsregister` (
  `id` int(255) NOT NULL,
  `identifier` varchar(50) NOT NULL,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(255) NOT NULL,
  `dateofcrime` varchar(255) NOT NULL,
  `crime` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `race_tracks`
--

CREATE TABLE `race_tracks` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `checkpoints` text DEFAULT NULL,
  `records` text DEFAULT NULL,
  `creator` varchar(50) DEFAULT NULL,
  `distance` int(11) DEFAULT NULL,
  `raceid` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `rented_vehicles`
--

CREATE TABLE `rented_vehicles` (
  `vehicle` varchar(60) NOT NULL,
  `plate` varchar(12) NOT NULL,
  `player_name` varchar(255) NOT NULL,
  `base_price` int(11) NOT NULL,
  `rent_price` int(11) NOT NULL,
  `owner` varchar(22) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `salvzbanking_societies`
--

CREATE TABLE `salvzbanking_societies` (
  `society` varchar(255) DEFAULT NULL,
  `society_name` varchar(255) DEFAULT NULL,
  `value` int(50) DEFAULT NULL,
  `iban` varchar(255) NOT NULL,
  `is_withdrawing` int(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `salvzbanking_transactions`
--

CREATE TABLE `salvzbanking_transactions` (
  `id` int(11) NOT NULL,
  `receiver_identifier` varchar(255) NOT NULL,
  `receiver_name` varchar(255) NOT NULL,
  `sender_identifier` varchar(255) NOT NULL,
  `sender_name` varchar(255) NOT NULL,
  `date` varchar(255) NOT NULL,
  `value` int(50) NOT NULL,
  `type` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `salvz_shops`
--

CREATE TABLE `salvz_shops` (
  `ShopNumber` int(11) NOT NULL DEFAULT 0,
  `item` varchar(255) NOT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `reward` int(10) UNSIGNED NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `salvz_shops`
--

INSERT INTO `salvz_shops` (`ShopNumber`, `item`, `name`, `reward`) VALUES
(9, 'bread_box', 'المكافآت', 0),
(2, 'bread_box', 'المكافآت', 0),
(3, 'bread_box', 'المكافآت', 0),
(4, 'bread_box', 'المكافآت', 0),
(5, 'bread_box', 'المكافآت', 0),
(6, 'bread_box', 'المكافآت', 0),
(14, 'bread_box', 'المكافآت', 0),
(8, 'bread_box', 'المكافآت', 0),
(1, 'bread_box', 'المكافآت', 0),
(10, 'bread_box', 'المكافآت', 0),
(15, 'bread_box', 'المكافآت', 0),
(17, 'bread_box', 'المكافآت', 0),
(12, 'bread_box', 'المكافآت', 0),
(13, 'bread_box', 'المكافآت', 0),
(7, 'bread_box', 'المكافآت', 0),
(11, 'bread_box', 'المكافآت', 0),
(18, 'bread_box', 'المكافآت', 0),
(19, 'bread_box', 'المكافآت', 0),
(20, 'bread_box', 'المكافآت', 0),
(21, 'bread_box', 'المكافآت', 0),
(23, 'bread_box', 'المكافآت', 0),
(24, 'bread_box', 'المكافآت', 0),
(26, 'bread_box', 'المكافآت', 0),
(16, 'bread_box', 'المكافآت', 0),
(27, 'bread_box', 'المكافآت', 0),
(25, 'bread_box', 'المكافآت', 0),
(28, 'bread_box', 'المكافآت', 0),
(29, 'bread_box', 'المكافآت', 0),
(30, 'bread_box', 'المكافآت', 0),
(31, 'bread_box', 'المكافآت', 0),
(32, 'bread_box', 'المكافآت', 0),
(33, 'bread_box', 'المكافآت', 0),
(34, 'bread_box', 'المكافآت', 0),
(35, 'bread_box', 'المكافآت', 0),
(36, 'bread_box', 'المكافآت', 0),
(37, 'bread_box', 'المكافآت', 0),
(38, 'bread_box', 'المكافآت', 0),
(39, 'bread_box', 'المكافآت', 0),
(40, 'bread_box', 'المكافآت', 0),
(41, 'bread_box', 'المكافآت', 0),
(42, 'bread_box', 'المكافآت', 0),
(43, 'bread_box', 'المكافآت', 0),
(44, 'bread_box', 'المكافآت', 0),
(45, 'bread_box', 'المكافآت', 0),
(46, 'bread_box', 'المكافآت', 0),
(47, 'bread_box', 'المكافآت', 0),
(48, 'bread_box', 'المكافآت', 0),
(49, 'bread_box', 'المكافآت', 0),
(50, 'bread_box', 'المكافآت', 0),
(51, 'bread_box', 'المكافآت', 0),
(52, 'bread_box', 'المكافآت', 0),
(53, 'bread_box', 'المكافآت', 0),
(54, 'bread_box', 'المكافآت', 0),
(55, 'bread_box', 'المكافآت', 0),
(56, 'bread_box', 'المكافآت', 0),
(57, 'bread_box', 'المكافآت', 0);

-- --------------------------------------------------------

--
-- Table structure for table `selling_cars`
--

CREATE TABLE `selling_cars` (
  `id` int(11) NOT NULL,
  `seller` varchar(50) NOT NULL,
  `vehicleProps` longtext NOT NULL,
  `price` int(11) NOT NULL DEFAULT 0,
  `name` longtext NOT NULL,
  `priceold` int(11) NOT NULL DEFAULT 0,
  `level` int(11) NOT NULL DEFAULT 0,
  `category` longtext NOT NULL,
  `modelname` longtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- Table structure for table `servers`
--

CREATE TABLE `servers` (
  `ID` int(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `connection` varchar(255) NOT NULL,
  `rcon` varchar(255) NOT NULL,
  `community` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `shipments`
--

CREATE TABLE `shipments` (
  `idd` int(11) NOT NULL,
  `id` int(11) DEFAULT NULL,
  `label` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `item` varchar(50) DEFAULT NULL,
  `price` varchar(50) DEFAULT NULL,
  `count` varchar(50) DEFAULT NULL,
  `time` int(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `shops`
--

CREATE TABLE `shops` (
  `ShopNumber` int(11) NOT NULL DEFAULT 0,
  `src` varchar(50) NOT NULL,
  `count` int(11) NOT NULL,
  `item` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `price` int(11) NOT NULL,
  `label` varchar(255) DEFAULT NULL,
  `level` int(11) DEFAULT NULL,
  `ojr` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `shops`
--

INSERT INTO `shops` (`ShopNumber`, `src`, `count`, `item`, `price`, `label`, `level`, `ojr`) VALUES
(24, 'img/s_tool.png', 99581, 's_tool', 210, 'عدة عمل الدواجن', 0, 0),
(24, 'img/l_tool.png', 99999, 'l_tool', 290, 'عدة عمل الأخشاب', 0, 0),
(24, 'img/m_tool.png', 99999, 'm_tool', 310, 'عدة عمل معادن', 0, 0),
(24, 'img/f_tool.png', 99999, 'f_tool', 200, 'عدة عمل النفط والغاز', 0, 0),
(24, 'img/v_tool.png', 99999, 'v_tool', 225, 'عدة عمل خضروات', 0, 0),
(24, 'img/t_tool.png', 99999, 't_tool', 190, 'عدة عمل أقمشة', 0, 0),
(24, 'img/brickworkkit.png', 99999, 'brickworkkit', 250, 'عدة عمل أسمنت', 0, 0),
(24, 'img/mnahel_tool.png', 99999, 'mnahel_tool', 200, 'عدة النحال', 0, 0),
(24, 'img/tomot_tool.png', 99999, 'tomot_tool', 180, 'عدة تمور', 0, 0),
(24, 'img/wa_tool.png', 99999, 'wa_tool', 220, 'عدة عمل المياة', 0, 0),
(24, 'img/milk_tool.png', 99999, 'milk_tool', 210, 'عدة حليب', 0, 0),
(24, 'img/fishingrod.png', 99999, 'fishingrod', 180, 'سنارة صيد', 0, 0),
(24, 'img/turtlebait.png', 99999, 'turtlebait', 165, 'طعم سلحفاة', 0, 0),
(1, 'img/fishbait.png', 99999, 'fishbait', 160, 'طعم سمك', 0, 0),
(24, 'img/laptop_h.png', 99999, 'laptop_h', 15000, 'لاب توب', 0, 0),
(24, 'img/speedcamera_detector.png', 99998, 'speedcamera_detector', 50000, 'كاشف رادار', 0, 0),
(24, 'img/drill.png', 99999, 'drill', 20000, 'دريل', 0, 0),
(24, 'img/cupcake.png', 99979, 'cupcake', 99, 'فطيرة', 0, 0),
(24, 'img/croquettes.png', 99999, 'croquettes', 950, 'طعم حيوانات', 0, 0),
(24, 'img/lighter.png', 99999, 'lighter', 220, 'ولاعة', 0, 0),
(24, 'img/cocacola.png', 99984, 'cocacola', 50, 'كوكاكولا', 0, 0),
(24, 'img/chocolate.png', 99974, 'chocolate', 30, 'شوكلاته', 0, 0),
(24, 'img/water.png', 99984, 'water', 145, 'مياه معدنية', 0, 0),
(24, 'img/oxygen_mask.png', 99999, 'oxygen_mask', 1200, 'قناع أكسجين', 0, 0),
(24, 'img/headbag.png', 99999, 'headbag', 350, 'خشيه', 0, 0),
(24, 'img/radio.png', 99996, 'radio', 75000, 'راديو', 0, 0),
(24, 'img/cheps.png', 99989, 'cheps', 60, 'كيس شيبس', 0, 0),
(24, 'img/cigarette.png', 99989, 'cigarette', 100, 'سجائر', 0, 0),
(24, 'img/fixkit.png', 99995, 'fixkit', 999, 'عدة تصليح', 0, 0),
(25, 'img/s_tool.png', 99998, 's_tool', 220, 'عدة عمل الدواجن', 0, 0),
(25, 'img/l_tool.png', 99998, 'l_tool', 300, 'عدة عمل الأخشاب', 0, 0),
(25, 'img/m_tool.png', 99978, 'm_tool', 330, 'عدة عمل معادن', 0, 0),
(25, 'img/f_tool.png', 99988, 'f_tool', 230, 'عدة عمل النفط والغاز', 0, 0),
(25, 'img/v_tool.png', 99998, 'v_tool', 240, 'عدة عمل خضروات', 0, 0),
(25, 'img/t_tool.png', 99998, 't_tool', 200, 'عدة عمل أقمشة', 0, 0),
(25, 'img/brickworkkit.png', 99998, 'brickworkkit', 260, 'عدة عمل أسمنت', 0, 0),
(25, 'img/mnahel_tool.png', 99998, 'mnahel_tool', 210, 'عدة النحال', 0, 0),
(25, 'img/tomot_tool.png', 99998, 'tomot_tool', 190, 'عدة تمور', 0, 0),
(25, 'img/wa_tool.png', 99998, 'wa_tool', 230, 'عدة عمل المياة', 0, 0),
(25, 'img/milk_tool.png', 99998, 'milk_tool', 220, 'عدة حليب', 0, 0),
(25, 'img/fishingrod.png', 99998, 'fishingrod', 200, 'سنارة صيد', 0, 0),
(25, 'img/turtlebait.png', 99998, 'turtlebait', 180, 'طعم سلحفاة', 0, 0),
(25, 'img/fishbait.png', 99998, 'fishbait', 190, 'طعم سمك', 0, 0),
(25, 'img/laptop_h.png', 99998, 'laptop_h', 18000, 'لاب توب', 0, 0),
(25, 'img/speedcamera_detector.png', 99998, 'speedcamera_detector', 30000, 'كاشف رادار', 0, 0),
(25, 'img/drill.png', 99998, 'drill', 20000, 'دريل', 0, 0),
(25, 'img/cupcake.png', 99987, 'cupcake', 80, 'فطيرة', 0, 0),
(25, 'img/croquettes.png', 99999, 'croquettes', 900, 'طعم حيوانات', 0, 0),
(25, 'img/lighter.png', 99998, 'lighter', 220, 'ولاعة', 0, 0),
(25, 'img/cocacola.png', 99990, 'cocacola', 40, 'كوكاكولا', 0, 0),
(25, 'img/chocolate.png', 99995, 'chocolate', 30, 'شوكلاته', 0, 0),
(25, 'img/water.png', 99990, 'water', 130, 'مياه معدنية', 0, 0),
(25, 'img/oxygen_mask.png', 99998, 'oxygen_mask', 1400, 'قناع أكسجين', 0, 0),
(25, 'img/headbag.png', 99998, 'headbag', 350, 'خشيه', 0, 0),
(25, 'img/radio.png', 99998, 'radio', 50000, 'راديو', 0, 0),
(25, 'img/cheps.png', 99994, 'cheps', 50, 'كيس شيبس', 0, 0),
(25, 'img/cigarette.png', 99998, 'cigarette', 100, 'سجائر', 0, 0),
(25, 'img/fixkit.png', 99988, 'fixkit', 999, 'عدة تصليح', 0, 0),
(26, 'img/s_tool.png', 99987, 's_tool', 240, 'عدة عمل الدواجن', 0, 0),
(28, 'img/l_tool.png', 99574, 'l_tool', 320, 'عدة عمل الأخشاب', 0, 5000),
(26, 'img/m_tool.png', 99994, 'm_tool', 310, 'عدة عمل معادن', 0, 0),
(26, 'img/f_tool.png', 99994, 'f_tool', 200, 'عدة عمل النفط والغاز', 0, 0),
(26, 'img/v_tool.png', 99987, 'v_tool', 215, 'عدة عمل خضروات', 0, 0),
(26, 'img/brickworkkit.png', 99987, 'brickworkkit', 270, 'عدة عمل أسمنت', 0, 0),
(26, 'img/mnahel_tool.png', 99987, 'mnahel_tool', 220, 'عدة النحال', 0, 0),
(26, 'img/tomot_tool.png', 99987, 'tomot_tool', 200, 'عدة تمور', 0, 0),
(26, 'img/wa_tool.png', 99987, 'wa_tool', 240, 'عدة عمل المياة', 0, 0),
(26, 'img/milk_tool.png', 99987, 'milk_tool', 230, 'عدة حليب', 0, 0),
(28, 'img/t_tool.png', 99989, 't_tool', 190, 'عدة عمل أقمشة', 0, 0),
(26, 'img/fishingrod.png', 99999, 'fishingrod', 170, 'سنارة صيد', 0, 0),
(26, 'img/turtlebait.png', 99999, 'turtlebait', 150, 'طعم سلحفاة', 0, 0),
(28, 'img/fishbait.png', 99997, 'fishbait', 170, 'طعم سمك', 0, 0),
(26, 'img/laptop_h.png', 99999, 'laptop_h', 14999, 'لاب توب', 0, 0),
(26, 'img/speedcamera_detector.png', 99999, 'speedcamera_detector', 70000, 'كاشف رادار', 0, 0),
(26, 'img/drill.png', 99999, 'drill', 19000, 'دريل', 0, 0),
(26, 'img/cupcake.png', 99974, 'cupcake', 80, 'فطيرة', 0, 0),
(26, 'img/croquettes.png', 99999, 'croquettes', 950, 'طعم حيوانات', 0, 0),
(26, 'img/lighter.png', 99999, 'lighter', 199, 'ولاعة', 0, 0),
(28, 'img/cocacola.png', 99988, 'cocacola', 50, 'كوكاكولا', 0, 0),
(28, 'img/chocolate.png', 99999, 'chocolate', 30, 'شوكلاته', 0, 0),
(26, 'img/water.png', 99979, 'water', 145, 'مياه معدنية', 0, 0),
(26, 'img/oxygen_mask.png', 99999, 'oxygen_mask', 1200, 'قناع أكسجين', 0, 0),
(26, 'img/headbag.png', 99999, 'headbag', 400, 'خشيه', 0, 0),
(26, 'img/radio.png', 99996, 'radio', 55000, 'راديو', 0, 0),
(26, 'img/cheps.png', 99994, 'cheps', 70, 'كيس شيبس', 0, 0),
(26, 'img/cigarette.png', 99999, 'cigarette', 100, 'سجائر', 0, 0),
(26, 'img/fixkit.png', 99989, 'fixkit', 670, 'عدة تصليح', 0, 0),
(26, 'img/s_tool.png', 99987, 's_tool', 260, 'عدة عمل الدواجن', 0, 0),
(26, 'img/l_tool.png', 99994, 'l_tool', 320, 'عدة عمل الأخشاب', 0, 0),
(28, 'img/m_tool.png', 99770, 'm_tool', 325, 'عدة عمل معادن', 0, 0),
(28, 'img/f_tool.png', 99984, 'f_tool', 199, 'عدة عمل النفط والغاز', 0, 0),
(28, 'img/v_tool.png', 99999, 'v_tool', 230, 'عدة عمل خضروات', 0, 0),
(28, 'img/brickworkkit.png', 99999, 'brickworkkit', 280, 'عدة عمل أسمنت', 0, 0),
(28, 'img/mnahel_tool.png', 99999, 'mnahel_tool', 230, 'عدة النحال', 0, 0),
(28, 'img/tomot_tool.png', 99999, 'tomot_tool', 210, 'عدة تمور', 0, 0),
(28, 'img/wa_tool.png', 99999, 'wa_tool', 250, 'عدة عمل المياة', 0, 0),
(28, 'img/milk_tool.png', 99999, 'milk_tool', 240, 'عدة حليب', 0, 0),
(26, 'img/t_tool.png', 99999, 't_tool', 190, 'عدة عمل أقمشة', 0, 0),
(28, 'img/fishingrod.png', 99997, 'fishingrod', 180, 'سنارة صيد', 0, 25),
(28, 'img/turtlebait.png', 99999, 'turtlebait', 165, 'طعم سلحفاة', 0, 0),
(26, 'img/fishbait.png', 99999, 'fishbait', 170, 'طعم سمك', 0, 0),
(28, 'img/laptop_h.png', 99998, 'laptop_h', 15000, 'لاب توب', 0, 0),
(28, 'img/speedcamera_detector.png', 99999, 'speedcamera_detector', 50000, 'كاشف رادار', 0, 0),
(28, 'img/drill.png', 99998, 'drill', 25000, 'دريل', 0, 0),
(28, 'img/cupcake.png', 99963, 'cupcake', 99, 'فطيرة', 0, 0),
(1, 'img/croquettes.png', 99999, 'croquettes', 900, 'طعم حيوانات', 0, 0),
(28, 'img/lighter.png', 99999, 'lighter', 220, 'ولاعة', 0, 0),
(26, 'img/cocacola.png', 99994, 'cocacola', 50, 'كوكاكولا', 0, 0),
(26, 'img/chocolate.png', 99994, 'chocolate', 30, 'شوكلاته', 0, 0),
(28, 'img/water.png', 99979, 'water', 130, 'مياه معدنية', 0, 0),
(28, 'img/fixkit.png', 99983, 'fixkit', 999, 'عدة تصليح', 0, 0),
(24, 'img/s_tool.png', 99581, 's_tool', 200, 'عدة عمل الدواجن', 0, 0),
(24, 'img/l_tool.png', 99999, 'l_tool', 300, 'عدة عمل الأخشاب', 0, 0),
(24, 'img/v_tool.png', 99999, 'v_tool', 225, 'عدة عمل خضروات', 0, 0),
(24, 'img/t_tool.png', 99999, 't_tool', 190, 'عدة عمل أقمشة', 0, 0),
(24, 'img/fishingrod.png', 99999, 'fishingrod', 140, 'سنارة صيد', 0, 0),
(22, 'img/turtlebait.png', 99999, 'turtlebait', 180, 'طعم سلحفاة', 0, 0),
(24, 'img/fishbait.png', 99999, 'fishbait', 160, 'طعم سمك', 0, 0),
(24, 'img/cupcake.png', 99979, 'cupcake', 99, 'فطيرة', 0, 0),
(24, 'img/croquettes.png', 99999, 'croquettes', 950, 'طعم حيوانات', 0, 0),
(24, 'img/water.png', 99984, 'water', 145, 'مياه معدنية', 0, 0),
(28, 'img/oxygen_mask.png', 99998, 'oxygen_mask', 1200, 'قناع أكسجين', 0, 0),
(24, 'img/cheps.png', 99989, 'cheps', 60, 'كيس شيبس', 0, 0),
(1, 'img/water.png', 99991, 'water', 135, 'مياه معدنية', 0, 0),
(1, 'img/cigarette.png', 99997, 'cigarette', 124, 'سجائر', 0, 0),
(22, 'img/beer.png', 99998, 'beer', 450, 'خمر', 0, 0),
(1, 'img/grand_cru.png', 99999, 'grand_cru', 1600, 'خمر فاخر', 0, 0),
(2, 'img/water.png', 99999, 'water', 120, 'مياه معدنية', 0, 0),
(2, 'img/cigarette.png', 99999, 'cigarette', 130, 'سجائر', 0, 0),
(2, 'img/beer.png', 99984, 'beer', 500, 'خمر', 0, 0),
(2, 'img/grand_cru.png', 99999, 'grand_cru', 1450, 'خمر فاخر', 0, 0),
(3, 'img/water.png', 99995, 'water', 120, 'مياه معدنية', 0, 0),
(3, 'img/cigarette.png', 99992, 'cigarette', 130, 'سجائر', 0, 0),
(3, 'img/beer.png', 99953, 'beer', 600, 'خمر', 0, 0),
(4, 'img/bulletproof.png', 99991, 'bulletproof', 5000, 'سترة مضادة للرصاص', 0, 0),
(4, 'img/boxbig.png', 99973, 'boxbig', 2600, 'تعبئة طلقات', 0, 0),
(4, 'img/binoculars.png', 99998, 'binoculars', 8000, 'منظار يدوي', 0, 0),
(4, 'img/WEAPON_PISTOL.png', 99994, 'WEAPON_PISTOL', 25000, 'مسدس', 10, 0),
(4, 'img/WEAPON_FLASHLIGHT.png', 99998, 'WEAPON_FLASHLIGHT', 5000, 'كشاف يدوي', 10, 0),
(4, 'img/WEAPON_MACHETE.png', 99995, 'WEAPON_MACHETE', 3000, 'ساطور', 5, 0),
(4, 'img/WEAPON_PUMPSHOTGUN.png', 99996, 'WEAPON_PUMPSHOTGUN', 100000, 'شوزن', 25, 0),
(5, 'img/WEAPON_SWITCHBLADE.png', 99998, 'WEAPON_SWITCHBLADE', 7000, 'سكين', 3, 0),
(4, 'img/WEAPON_BATTLEAXE.png', 99996, 'WEAPON_BATTLEAXE', 10000, 'فأس', 3, 0),
(4, 'img/WEAPON_MICROSMG.png', 99998, 'WEAPON_MICROSMG', 200000, 'مايكرو', 50, 0),
(5, 'img/bulletproof.png', 99994, 'bulletproof', 5000, 'سترة مضادة للرصاص', 0, 0),
(5, 'img/boxbig.png', 99992, 'boxbig', 2600, 'تعبئة طلقات', 0, 0),
(5, 'img/binoculars.png', 99998, 'binoculars', 8000, 'منظار يدوي', 0, 0),
(5, 'img/WEAPON_PISTOL.png', 99995, 'WEAPON_PISTOL', 25000, 'مسدس', 10, 0),
(5, 'img/WEAPON_FLASHLIGHT.png', 99999, 'WEAPON_FLASHLIGHT', 5000, 'كشاف يدوي', 10, 0),
(5, 'img/WEAPON_MACHETE.png', 99999, 'WEAPON_MACHETE', 3000, 'ساطور', 5, 0),
(5, 'img/WEAPON_PUMPSHOTGUN.png', 99999, 'WEAPON_PUMPSHOTGUN', 100000, 'شوزن', 25, 0),
(5, 'img/WEAPON_BATTLEAXE.png', 99999, 'WEAPON_BATTLEAXE', 10000, 'فأس', 3, 0),
(5, 'img/WEAPON_MICROSMG.png', 99999, 'WEAPON_MICROSMG', 200000, 'مايكرو', 50, 0),
(6, 'img/bulletproof.png', 99999, 'bulletproof', 5000, 'سترة مضادة للرصاص', 0, 0),
(6, 'img/boxbig.png', 99999, 'boxbig', 2600, 'تعبئة طلقات', 0, 0),
(6, 'img/binoculars.png', 99999, 'binoculars', 8000, 'منظار يدوي', 0, 0),
(6, 'img/WEAPON_PISTOL.png', 99997, 'WEAPON_PISTOL', 25000, 'مسدس', 10, 0),
(6, 'img/WEAPON_FLASHLIGHT.png', 99999, 'WEAPON_FLASHLIGHT', 5000, 'كشاف يدوي', 10, 0),
(6, 'img/WEAPON_MACHETE.png', 99998, 'WEAPON_MACHETE', 3000, 'ساطور', 5, 0),
(6, 'img/WEAPON_PUMPSHOTGUN.png', 99999, 'WEAPON_PUMPSHOTGUN', 100000, 'شوزن', 25, 0),
(6, 'img/WEAPON_SWITCHBLADE.png', 99999, 'WEAPON_SWITCHBLADE', 7000, 'سكين', 3, 0),
(6, 'img/WEAPON_BATTLEAXE.png', 99999, 'WEAPON_BATTLEAXE', 10000, 'فأس', 3, 0),
(6, 'img/WEAPON_MICROSMG.png', 99999, 'WEAPON_MICROSMG', 200000, 'مايكرو', 50, 0),
(7, 'img/xanax.png', 100015, 'xanax', 950, 'زانكس', 0, 0),
(7, 'img/medikit.png', 99990, 'bandage', 700, 'ضماد جروح', 0, 0),
(7, 'img/medikit.png', 99948, 'medikit', 1900, 'إسعافات أولية', 0, 0),
(8, 'img/xanax.png', 99999, 'xanax', 950, 'زانكس', 0, 0),
(8, 'img/medikit.png', 99999, 'bandage', 700, 'ضماد جروح', 0, 0),
(8, 'img/medikit.png', 99999, 'medikit', 1900, 'إسعافات أولية', 0, 0),
(9, 'img/water.png', 99989, 'water', 150, 'مياه معدنية', 0, 0),
(1, 'img/chocolate.png', 99991, 'chocolate', 30, 'شوكولاتة', 0, 0),
(1, 'img/cocacola.png', 99997, 'cocacola', 50, 'كوكاكولا', 0, 0),
(9, 'img/cupcake.png', 99999, 'cupcake', 100, 'فطيره', 0, 0),
(9, 'img/cheps.png', 99999, 'cheps', 70, 'شيبس', 0, 0),
(10, 'img/water.png', 99999, 'water', 150, 'مياه معدنية', 0, 0),
(10, 'img/chocolate.png', 99999, 'chocolate', 30, 'شوكولاتة', 0, 0),
(10, 'img/cocacola.png', 99999, 'cocacola', 50, 'كوكاكولا', 0, 0),
(10, 'img/cupcake.png', 99999, 'cupcake', 100, 'فطيره', 0, 0),
(10, 'img/cheps.png', 99999, 'cheps', 70, 'شيبس', 0, 0),
(11, 'img/water.png', 99999, 'water', 150, 'مياه معدنية', 0, 0),
(11, 'img/chocolate.png', 99999, 'chocolate', 30, 'شوكولاتة', 0, 0),
(11, 'img/cocacola.png', 99999, 'cocacola', 50, 'كوكاكولا', 0, 0),
(11, 'img/cupcake.png', 99999, 'cupcake', 100, 'فطيره', 0, 0),
(11, 'img/cheps.png', 99999, 'cheps', 70, 'شيبس', 0, 0),
(12, 'img/water.png', 99999, 'water', 150, 'مياه معدنية', 0, 0),
(12, 'img/chocolate.png', 99999, 'chocolate', 30, 'شوكولاتة', 0, 0),
(12, 'img/cocacola.png', 99999, 'cocacola', 50, 'كوكاكولا', 0, 0),
(12, 'img/cupcake.png', 99999, 'cupcake', 100, 'فطيره', 0, 0),
(12, 'img/cheps.png', 99999, 'cheps', 70, 'شيبس', 0, 0),
(13, 'img/chocolate.png', 99999, 'chocolate', 30, 'شوكولاتة', 0, 0),
(13, 'img/cocacola.png', 99999, 'cocacola', 50, 'كوكاكولا', 0, 0),
(13, 'img/cupcake.png', 99999, 'cupcake', 100, 'فطيره', 0, 0),
(13, 'img/cheps.png', 99999, 'cheps', 70, 'شيبس', 0, 0),
(14, 'img/water.png', 99999, 'water', 150, 'مياه معدنية', 0, 0),
(14, 'img/chocolate.png', 99999, 'chocolate', 30, 'شوكولاتة', 0, 0),
(14, 'img/cocacola.png', 99999, 'cocacola', 50, 'كوكاكولا', 0, 0),
(14, 'img/cupcake.png', 99999, 'cupcake', 100, 'فطيره', 0, 0),
(14, 'img/cheps.png', 99999, 'cheps', 70, 'شيبس', 0, 0),
(15, 'img/water.png', 99999, 'water', 150, 'مياه معدنية', 0, 0),
(17, 'img/chocolate.png', 99999, 'chocolate', 30, 'شوكولاتة', 0, 0),
(15, 'img/cocacola.png', 99999, 'cocacola', 50, 'كوكاكولا', 0, 0),
(15, 'img/cupcake.png', 99999, 'cupcake', 100, 'فطيره', 0, 0),
(15, 'img/cheps.png', 99999, 'cheps', 70, 'شيبس', 0, 0),
(15, 'img/water.png', 99999, 'water', 150, 'مياه معدنية', 0, 0),
(15, 'img/chocolate.png', 99999, 'chocolate', 30, 'شوكولاتة', 0, 0),
(16, 'img/cocacola.png', 99999, 'cocacola', 50, 'كوكاكولا', 0, 0),
(16, 'img/cupcake.png', 99999, 'cupcake', 100, 'فطيره', 0, 0),
(16, 'img/cheps.png', 99999, 'cheps', 70, 'شيبس', 0, 0),
(16, 'img/water.png', 99999, 'water', 150, 'مياه معدنية', 0, 0),
(16, 'img/chocolate.png', 99999, 'chocolate', 30, 'شوكولاتة', 0, 0),
(17, 'img/cocacola.png', 99999, 'cocacola', 50, 'كوكاكولا', 0, 0),
(17, 'img/cupcake.png', 99999, 'cupcake', 100, 'فطيره', 0, 0),
(17, 'img/cheps.png', 99999, 'cheps', 70, 'شيبس', 0, 0),
(18, 'img/water.png', 99989, 'water', 150, 'مياه معدنية', 0, 0),
(18, 'img/chocolate.png', 99999, 'chocolate', 30, 'شوكولاتة', 0, 0),
(18, 'img/cocacola.png', 99999, 'cocacola', 50, 'كوكاكولا', 0, 0),
(18, 'img/cupcake.png', 99999, 'cupcake', 100, 'فطيره', 0, 0),
(18, 'img/cheps.png', 99999, 'cheps', 70, 'شيبس', 0, 0),
(19, 'img/water.png', 99999, 'water', 150, 'مياه معدنية', 0, 0),
(19, 'img/chocolate.png', 99999, 'chocolate', 30, 'شوكولاتة', 0, 0),
(19, 'img/cocacola.png', 99998, 'cocacola', 50, 'كوكاكولا', 0, 0),
(19, 'img/cupcake.png', 99999, 'cupcake', 100, 'فطيره', 0, 0),
(19, 'img/cheps.png', 99999, 'cheps', 70, 'شيبس', 0, 0),
(20, 'img/water.png', 99983, 'water', 150, 'مياه معدنية', 0, 0),
(20, 'img/chocolate.png', 99999, 'chocolate', 30, 'شوكولاتة', 0, 0),
(20, 'img/cocacola.png', 99999, 'cocacola', 50, 'كوكاكولا', 0, 0),
(20, 'img/cupcake.png', 99999, 'cupcake', 100, 'فطيره', 0, 0),
(20, 'img/cheps.png', 99999, 'cheps', 70, 'شيبس', 0, 0),
(30, 'img/bergrul.png', 99999, 'bergrul', 250, 'وجبة برجر وسط', 0, 0),
(30, 'img/coshe.png', 99999, 'coshe', 230, 'سوشي', 0, 0),
(30, 'img/pepsi.png', 99995, 'pepsi', 80, 'بيبسي', 0, 0),
(30, 'img/batato.png', 99999, 'batato', 70, 'بطاطس', 0, 0),
(30, 'img/cupcake.png', 99999, 'cupcake', 70, 'فطيرة', 0, 0),
(30, 'img/cocacola.png', 99999, 'cocacola', 0, 'كوكاكولا', 0, 0),
(30, 'img/bread.png', 99999, 'bread', 100, 'برجر', 0, 0),
(30, 'img/water.png', 99994, 'water', 99, 'مياه معدنية', 0, 0),
(31, 'img/bergrkb.png', 99979, 'bergrkb', 350, 'وجبة برجر كبير', 0, 0),
(31, 'img/bergrul.png', 99995, 'bergrul', 250, 'وجبة برجر وسط', 0, 0),
(1, 'img/coshe.png', 99998, 'coshe', 230, 'سوشي', 0, 0),
(31, 'img/pepsi.png', 99992, 'pepsi', 80, 'بيبسي', 0, 0),
(1, 'img/batato.png', 99960, 'batato', 70, 'بطاطس', 0, 0),
(31, 'img/cupcake.png', 99996, 'cupcake', 70, 'فطيرة', 0, 0),
(31, 'img/cocacola.png', 99952, 'cocacola', 0, 'كوكاكولا', 0, 0),
(1, 'img/bread.png', 99971, 'bread', 100, 'برجر', 0, 0),
(31, 'img/water.png', 99992, 'water', 99, 'مياه معدنية', 0, 0),
(30, 'img/bergrkb.png', 99982, 'bergrkb', 350, 'وجبة برجر كبير', 0, 0),
(29, 'img/bergrkb.png', 99898, 'bergrkb', 350, 'وجبة برجر كبير', 0, 0),
(29, 'img/bergrul.png', 99997, 'bergrul', 250, 'وجبة برجر وسط', 0, 0),
(29, 'img/coshe.png', 99998, 'coshe', 230, 'سوشي', 0, 0),
(29, 'img/pepsi.png', 99992, 'pepsi', 80, 'بيبسي', 0, 0),
(32, 'img/batato.png', 99997, 'batato', 70, 'بطاطس', 0, 0),
(29, 'img/cupcake.png', 99997, 'cupcake', 70, 'فطيرة', 0, 0),
(29, 'img/cocacola.png', 99990, 'cocacola', 0, 'كوكاكولا', 0, 0),
(29, 'img/bread.png', 99998, 'bread', 100, 'برجر', 0, 0),
(29, 'img/water.png', 99958, 'water', 99, 'مياه معدنية', 0, 0),
(32, 'img/bergrkb.png', 99997, 'bergrkb', 350, 'وجبة برجر كبير', 0, 0),
(32, 'img/bergrul.png', 100026, 'bergrul', 250, 'وجبة برجر وسط', 0, 0),
(32, 'img/coshe.png', 99993, 'coshe', 230, 'سوشي', 0, 0),
(32, 'img/pepsi.png', 99997, 'pepsi', 80, 'بيبسي', 0, 0),
(29, 'img/batato.png', 99998, 'batato', 70, 'بطاطس', 0, 0),
(32, 'img/cupcake.png', 100022, 'cupcake', 70, 'فطيرة', 0, 0),
(32, 'img/cocacola.png', 99998, 'cocacola', 0, 'كوكاكولا', 0, 0),
(32, 'img/bread.png', 100019, 'bread', 100, 'برجر', 0, 0),
(32, 'img/water.png', 260, 'water', 1, 'مياه معدنية', 0, 0),
(33, 'img/phone.png', 196, 'phone', 3999, 'جوال', 0, 0),
(33, 'img/radio.png', 93, 'radio', 11000, 'راديو', 0, 0),
(33, 'img/water.png', 559, 'water', 70, 'مياه معدنية', 0, 0),
(33, 'img/bread.png', 569, 'bread', 25, 'برجر', 0, 0),
(33, 'img/s_tool.png', 274, 's_tool', 120, 'عدة عمل الدواجن', 0, 0),
(33, 'img/brickworkkit.png', 274, 'brickworkkit', 150, 'عدة عمل أسمنت', 0, 0),
(33, 'img/mnahel_tool.png', 274, 'mnahel_tool', 130, 'عدة النحال', 0, 0),
(33, 'img/tomot_tool.png', 274, 'tomot_tool', 110, 'عدة تمور', 0, 0),
(33, 'img/wa_tool.png', 274, 'wa_tool', 140, 'عدة عمل المياة', 0, 0),
(33, 'img/milk_tool.png', 274, 'milk_tool', 135, 'عدة حليب', 0, 0),
(33, 'img/fixkit.png', 195, 'fixkit', 950, 'عدة تصليح', 0, 0),
(33, 'img/fishingrod.png', 350, 'fishingrod', 650, 'سنارة صيد', 0, 0),
(33, 'img/chocolate.png', 947, 'chocolate', 55, 'شوكلاتة', 0, 0),
(33, 'img/fishbait.png', 925, 'fishbait', 70, 'طعم سمك', 0, 0),
(33, 'img/cupcake.png', 887, 'cupcake', 88, 'فطيرة', 0, 0),
(33, 'img/cocacola.png', 872, 'cocacola', 65, 'كولا', 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `society_moneywash`
--

CREATE TABLE `society_moneywash` (
  `id` int(11) NOT NULL,
  `identifier` varchar(60) NOT NULL,
  `society` varchar(60) NOT NULL,
  `amount` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `support_comments`
--

CREATE TABLE `support_comments` (
  `ID` int(255) NOT NULL,
  `message` varchar(2048) NOT NULL,
  `ticketid` varchar(255) NOT NULL,
  `commentid` varchar(255) NOT NULL,
  `steamid` varchar(255) NOT NULL,
  `time` varchar(255) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `support_tickets`
--

CREATE TABLE `support_tickets` (
  `ID` int(255) NOT NULL,
  `title` varchar(1024) NOT NULL,
  `message` varchar(2048) NOT NULL,
  `ticketid` varchar(255) NOT NULL,
  `steamid` varchar(255) NOT NULL,
  `status` enum('open','in-progress','pending','closed') NOT NULL DEFAULT 'open',
  `time` varchar(255) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `svmowned_shops`
--

CREATE TABLE `svmowned_shops` (
  `identifier` varchar(250) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  `ShopNumber` int(11) DEFAULT 0,
  `money` int(11) DEFAULT 0,
  `ShopValue` int(11) DEFAULT NULL,
  `LastRobbery` int(11) DEFAULT 0,
  `ShopName` varchar(30) DEFAULT NULL,
  `Salvz` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Salvzz` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Salvzzz` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Salvzzzz` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Salvzzzzz` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Salvzzzzzz` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `emps` longtext DEFAULT '{}',
  `buydate` varchar(50) NOT NULL DEFAULT '2122-08-01 19:04:30',
  `enddate` varchar(50) NOT NULL DEFAULT '2222-08-01 19:04:30',
  `ojor` varchar(50) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `svstorem_shipments`
--

CREATE TABLE `svstorem_shipments` (
  `id` int(11) DEFAULT NULL,
  `identifier` varchar(50) NOT NULL,
  `label` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `item` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `price` varchar(50) DEFAULT NULL,
  `count` int(11) NOT NULL,
  `time` int(20) DEFAULT NULL,
  `Command` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tgiann_mdt_arananlar`
--

CREATE TABLE `tgiann_mdt_arananlar` (
  `citizenid` varchar(50) DEFAULT NULL,
  `sebep` longtext DEFAULT NULL,
  `baslangic` varchar(255) DEFAULT NULL,
  `bitis` varchar(255) DEFAULT NULL,
  `isim` varchar(255) DEFAULT NULL,
  `img` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tgiann_mdt_cezalar`
--

CREATE TABLE `tgiann_mdt_cezalar` (
  `id` int(11) NOT NULL,
  `citizenid` varchar(50) DEFAULT NULL,
  `aciklama` longtext DEFAULT NULL,
  `ceza` varchar(255) DEFAULT NULL,
  `polis` mediumtext DEFAULT NULL,
  `zanli` mediumtext DEFAULT NULL,
  `cezalar` longtext DEFAULT NULL,
  `olayid` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tgiann_mdt_olaylar`
--

CREATE TABLE `tgiann_mdt_olaylar` (
  `id` int(11) NOT NULL,
  `aciklama` longtext DEFAULT NULL,
  `polis` mediumtext DEFAULT NULL,
  `zanli` mediumtext DEFAULT NULL,
  `zaman` varchar(50) DEFAULT current_timestamp(),
  `esyalar` mediumtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tinder_accounts`
--

CREATE TABLE `tinder_accounts` (
  `id` int(11) NOT NULL,
  `phone` varchar(50) NOT NULL DEFAULT '0',
  `pp` text NOT NULL,
  `name` varchar(50) NOT NULL DEFAULT '0',
  `gender` varchar(50) NOT NULL,
  `targetGender` varchar(50) NOT NULL DEFAULT '0',
  `hobbies` varchar(50) NOT NULL DEFAULT '0',
  `age` varchar(50) NOT NULL DEFAULT '0',
  `description` varchar(50) NOT NULL DEFAULT '0',
  `password` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tinder_likes`
--

CREATE TABLE `tinder_likes` (
  `id` int(11) NOT NULL,
  `phone` varchar(1024) NOT NULL,
  `likeds` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tinder_messages`
--

CREATE TABLE `tinder_messages` (
  `id` int(11) NOT NULL,
  `phone` varchar(50) NOT NULL,
  `number` varchar(50) NOT NULL,
  `messages` varchar(1024) DEFAULT '{}'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `trucks`
--

CREATE TABLE `trucks` (
  `name` varchar(60) NOT NULL,
  `model` varchar(60) NOT NULL,
  `price` int(11) NOT NULL,
  `category` varchar(60) DEFAULT NULL,
  `level` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `trucks`
--

INSERT INTO `trucks` (`name`, `model`, `price`, `category`, `level`) VALUES
('براد 250 كيلو بينسون', 'benson', 1700000, 'truck250', 45),
('شاحنة معادن كينورث 350 كيلو', 'biff', 1500000, 'miner350', 50),
('مقطورة معادن 250 كيلو', 'botdumptr', 350000, 'trailers250', 50),
('رأس شاحنة - كات 660', 'ct660', 5000000, 'haul', 70),
('شاحنة معادن كات 660', 'ct660dump', 1800000, 'miner350', 60),
('مقطورة معادن 350 كيلو', 'dumptr', 750000, 'mtrailers350', 60),
('براد 250 كيلو مارسيدس', 'mule', 1200000, 'truck250', 45),
('براد 250 كيلو GMC', 'mule2', 1200000, 'truck250', 40),
('رأس شاحنة - بكر', 'packer', 2350000, 'haul', 60),
('رأس شاحنة - فانتوم', 'phantom', 2400000, 'haul', 60),
('براد 250 كيلو كينورث', 'pounder', 1500000, 'truck250', 50),
('شاحنة معادن 250 كيلو ريبل', 'rubble', 1300000, 'miner250', 45),
('رأس شاحنة - سكانيا', 'scaniar730', 2100000, 'haul', 55),
('شاحنة معادن 350 كيلو 1', 'tiptruck', 1730000, 'miner350', 50),
('شاحنة معادن 350 كيلو 2', 'tiptruck2', 1760000, 'miner350', 50),
('باص رحلات سياحية', 'tourbus', 450000, 'trans', 15),
('مقطورة أخشاب 350 كيلو', 'trailerlogs2', 750000, 'mtrailers350', 50),
('مقطورة براد 250 كيلو', 'trailerswb', 350000, 'trailers250', 50),
('مقطورة براد 350 كيلو', 'trailerswb2', 750000, 'mtrailers350', 60),
(' رأس شاحنة - مارسيدس حمولات ثقيلة', 'trans_mbenzarocs', 5000000, 'haul', 75),
('شاحنة مارسيدس 250 كيلو', 'unimog', 2000000, 'truck250', 50),
('رأس شاحنة - فولفو', 'vnl780', 4800200, 'haul', 65),
('رأس شاحنة - W900 كينورث ', 'W900', 6700000, 'haul', 75);

-- --------------------------------------------------------

--
-- Table structure for table `trucks_for_sale`
--

CREATE TABLE `trucks_for_sale` (
  `id` int(11) NOT NULL,
  `seller` varchar(50) NOT NULL,
  `vehicleProps` longtext NOT NULL,
  `price` int(11) NOT NULL DEFAULT 0,
  `info` text NOT NULL DEFAULT 'لاشيء',
  `name` longtext NOT NULL,
  `priceold` int(11) NOT NULL DEFAULT 0,
  `levelold` int(11) NOT NULL DEFAULT 0,
  `category` longtext NOT NULL,
  `modelname` longtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- Table structure for table `truck_categories`
--

CREATE TABLE `truck_categories` (
  `name` varchar(60) NOT NULL,
  `label` varchar(60) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `truck_categories`
--

INSERT INTO `truck_categories` (`name`, `label`) VALUES
('haul', 'رأس مقطورة'),
('miner250', 'شاحنات معادن 250 كيلو'),
('miner350', 'شاحنات معادن 350 كيلو'),
('mtrailers350', 'مقطورات سحب - 350 كيلو'),
('trailers250', 'مقطورات سحب - 250 كيلو'),
('trans', 'باصات'),
('truck250', '250 كيلو');

-- --------------------------------------------------------

--
-- Table structure for table `trunk`
--

CREATE TABLE `trunk` (
  `plate` varchar(50) DEFAULT NULL,
  `data` longtext DEFAULT NULL,
  `owner` bit(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `trunk_inventory`
--

CREATE TABLE `trunk_inventory` (
  `id` int(11) NOT NULL,
  `plate` varchar(8) NOT NULL,
  `data` text NOT NULL,
  `owned` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `trunk_inventory`
--

INSERT INTO `trunk_inventory` (`id`, `plate`, `data`, `owned`) VALUES
(433, 'NLM 010 ', '{}', 0),
(434, '04QQF930', '{}', 0);

-- --------------------------------------------------------

--
-- Table structure for table `twitter_account`
--

CREATE TABLE `twitter_account` (
  `id` varchar(90) NOT NULL,
  `name` varchar(50) NOT NULL,
  `phone` varchar(50) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(50) NOT NULL,
  `avatar` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `twitter_hashtags`
--

CREATE TABLE `twitter_hashtags` (
  `id` int(11) NOT NULL,
  `name` varchar(80) NOT NULL,
  `created` varchar(50) NOT NULL,
  `count` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `twitter_likes`
--

CREATE TABLE `twitter_likes` (
  `id` int(11) NOT NULL,
  `authorId` int(11) DEFAULT NULL,
  `tweetId` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `twitter_mentions`
--

CREATE TABLE `twitter_mentions` (
  `id` int(11) NOT NULL,
  `id_tweet` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `mentioned` text NOT NULL,
  `created` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `twitter_tweets`
--

CREATE TABLE `twitter_tweets` (
  `id` int(5) NOT NULL,
  `username` varchar(80) NOT NULL,
  `message` longtext NOT NULL,
  `hashtags` varchar(50) NOT NULL,
  `mentions` varchar(50) NOT NULL,
  `created` varchar(50) NOT NULL,
  `image` text NOT NULL,
  `likes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `identifier` varchar(40) NOT NULL,
  `pet` varchar(40) DEFAULT NULL,
  `accounts` longtext DEFAULT NULL,
  `group` varchar(50) DEFAULT 'user',
  `inventory` longtext DEFAULT NULL,
  `job` varchar(20) DEFAULT 'unemployed',
  `job_grade` int(11) DEFAULT 0,
  `loadout` longtext DEFAULT NULL,
  `position` varchar(255) DEFAULT '{"x":-269.4,"y":-955.3,"z":31.2,"heading":205.8}',
  `firstname` varchar(16) DEFAULT NULL,
  `lastname` varchar(16) DEFAULT NULL,
  `dateofbirth` varchar(10) DEFAULT NULL,
  `sex` varchar(1) DEFAULT NULL,
  `height` int(11) DEFAULT NULL,
  `skin` longtext DEFAULT NULL,
  `status` longtext DEFAULT NULL,
  `is_dead` tinyint(1) DEFAULT 0,
  `jail` int(11) NOT NULL DEFAULT 0,
  `rp_xp` int(11) NOT NULL DEFAULT 0,
  `rp_rank` int(11) NOT NULL DEFAULT 1,
  `last_property` varchar(255) DEFAULT NULL,
  `aranma` mediumtext DEFAULT '[]',
  `photo` mediumtext DEFAULT NULL,
  `shop` int(11) DEFAULT NULL,
  `last_house` int(11) DEFAULT 0,
  `house` longtext NOT NULL DEFAULT '{"owns":false,"furniture":[],"houseId":0}',
  `bought_furniture` longtext NOT NULL DEFAULT '{}',
  `phone_number` varchar(20) DEFAULT NULL,
  `tattoos` longtext DEFAULT NULL,
  `apps` text DEFAULT NULL,
  `widget` text DEFAULT NULL,
  `bt` text DEFAULT NULL,
  `charinfo` text DEFAULT NULL,
  `metadata` mediumtext DEFAULT NULL,
  `cryptocurrency` longtext DEFAULT NULL,
  `cryptocurrencytransfers` text DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `iban` varchar(50) DEFAULT NULL,
  `twitteraccount` varchar(50) DEFAULT NULL,
  `settings` longtext DEFAULT NULL,
  `calls` longtext DEFAULT NULL,
  `notes` longtext DEFAULT NULL,
  `photos` longtext DEFAULT NULL,
  `darkchatuser` mediumtext DEFAULT NULL,
  `mailaccount` varchar(50) DEFAULT NULL,
  `pincode` int(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`identifier`, `pet`, `accounts`, `group`, `inventory`, `job`, `job_grade`, `loadout`, `position`, `firstname`, `lastname`, `dateofbirth`, `sex`, `height`, `skin`, `status`, `is_dead`, `jail`, `rp_xp`, `rp_rank`, `last_property`, `aranma`, `photo`, `shop`, `last_house`, `house`, `bought_furniture`, `phone_number`, `tattoos`, `apps`, `widget`, `bt`, `charinfo`, `metadata`, `cryptocurrency`, `cryptocurrencytransfers`, `phone`, `iban`, `twitteraccount`, `settings`, `calls`, `notes`, `photos`, `darkchatuser`, `mailaccount`, `pincode`) VALUES
('88f87e1ede7d8340d70efe9a3b6f48939687a4bf', NULL, '{\"bank\":**********,\"black_money\":********,\"money\":***********}', 'superadmin', '{\"beer\":3,\"weed\":1}', 'admin', 4, '{\"WEAPON_PUMPSHOTGUN\":{\"ammo\":209}}', '{\"y\":-1394.0,\"z\":30.6,\"heading\":116.2,\"x\":206.2}', 'عبدالعزيز', 'ابو', '04/23/2000', 'm', 196, '{\"hair_1\":0,\"glasses_1\":0,\"ears_2\":0,\"lipstick_2\":0,\"arms\":0,\"chain_1\":0,\"tshirt_1\":0,\"tshirt_2\":0,\"torso_1\":0,\"eyebrows_4\":0,\"lipstick_3\":0,\"bags_1\":0,\"makeup_3\":0,\"beard_3\":0,\"skin\":0,\"chain_2\":0,\"shoes_2\":0,\"makeup_4\":0,\"bags_2\":0,\"eyebrows_1\":0,\"helmet_2\":0,\"hair_color_2\":0,\"bproof_1\":0,\"decals_1\":0,\"eyebrows_2\":0,\"hair_2\":0,\"makeup_2\":0,\"age_1\":0,\"mask_2\":0,\"shoes_1\":0,\"glasses_2\":0,\"helmet_1\":-1,\"ears_1\":-1,\"beard_4\":0,\"bproof_2\":0,\"decals_2\":0,\"beard_2\":0,\"beard_1\":0,\"pants_1\":0,\"face\":0,\"hair_color_1\":0,\"pants_2\":0,\"mask_1\":0,\"torso_2\":0,\"sex\":0,\"eyebrows_3\":0,\"age_2\":0,\"makeup_1\":0,\"lipstick_4\":0,\"lipstick_1\":0}', '[{\"percent\":0.0,\"val\":0,\"name\":\"drunk\"},{\"percent\":0.0,\"val\":0,\"name\":\"drug\"},{\"percent\":0.0,\"val\":0,\"name\":\"drunk\"},{\"percent\":73.92,\"val\":739200,\"name\":\"hunger\"},{\"percent\":80.44,\"val\":804400,\"name\":\"thirst\"}]', 0, 0, 177437, 30, NULL, '[]', NULL, NULL, 0, '{\"owns\":false,\"furniture\":[],\"houseId\":0}', '{}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('9eef030b5f84df9eefcd3154fdac29df4fa63eb7', NULL, '{\"bank\":**********,\"money\":4293500,\"black_money\":********}', 'admin', '{\"radio\":1}', 'admin', 3, '{\"WEAPON_MACHETE\":{\"ammo\":30},\"WEAPON_PUMPSHOTGUN\":{\"ammo\":5},\"WEAPON_BATTLEAXE\":{\"ammo\":30}}', '{\"y\":-1107.0,\"z\":29.8,\"heading\":309.5,\"x\":22.7}', 'ابو', 'عبدالعزيز', '05/20/2000', 'm', 170, NULL, '[{\"name\":\"drunk\",\"val\":0,\"percent\":0.0},{\"name\":\"drug\",\"val\":0,\"percent\":0.0},{\"name\":\"drunk\",\"val\":0,\"percent\":0.0},{\"name\":\"hunger\",\"val\":465100,\"percent\":46.51},{\"name\":\"thirst\",\"val\":473825,\"percent\":47.3825}]', 0, 0, ********, 999, NULL, '[]', NULL, NULL, 0, '{\"owns\":false,\"furniture\":[],\"houseId\":0}', '{}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_licenses`
--

CREATE TABLE `user_licenses` (
  `id` int(11) NOT NULL,
  `type` varchar(60) NOT NULL,
  `owner` varchar(40) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `user_licenses`
--

INSERT INTO `user_licenses` (`id`, `type`, `owner`) VALUES
(368, 'weapon', '9eef030b5f84df9eefcd3154fdac29df4fa63eb7'),
(369, 'dmv', '9eef030b5f84df9eefcd3154fdac29df4fa63eb7'),
(370, 'drive', '9eef030b5f84df9eefcd3154fdac29df4fa63eb7'),
(371, 'weapon', '370af5c1f03b439803d21d19b79427f9707f46ee'),
(372, 'dmv', '370af5c1f03b439803d21d19b79427f9707f46ee'),
(373, 'drive', '370af5c1f03b439803d21d19b79427f9707f46ee'),
(374, 'drive', '64bf93f4ae55cf7b5cd8f42e413d535088fa5196'),
(375, 'dmv', '64bf93f4ae55cf7b5cd8f42e413d535088fa5196'),
(377, 'weapon', '64bf93f4ae55cf7b5cd8f42e413d535088fa5196');

-- --------------------------------------------------------

--
-- Table structure for table `user_races`
--

CREATE TABLE `user_races` (
  `username` varchar(50) NOT NULL,
  `racetime` varchar(50) NOT NULL,
  `race` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `vehicles`
--

CREATE TABLE `vehicles` (
  `name` varchar(60) NOT NULL,
  `model` varchar(60) NOT NULL,
  `price` int(11) NOT NULL,
  `category` varchar(60) DEFAULT NULL,
  `level` int(11) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `vehicles`
--

INSERT INTO `vehicles` (`name`, `model`, `price`, `category`, `level`) VALUES
('تيوتا تندرا غمارتين 2020', '-420359219', 1650000, 'a', 35),
('جمس 1990', '1259679467', 700000, 'a', 20);

-- --------------------------------------------------------

--
-- Table structure for table `vehicles_for_sale`
--

CREATE TABLE `vehicles_for_sale` (
  `id` int(11) NOT NULL,
  `seller` varchar(50) NOT NULL,
  `vehicleProps` longtext NOT NULL,
  `price` int(11) NOT NULL DEFAULT 0,
  `info` text NOT NULL DEFAULT 'لاشيء',
  `name` longtext NOT NULL,
  `priceold` int(11) NOT NULL DEFAULT 0,
  `levelold` int(11) NOT NULL DEFAULT 0,
  `category` longtext NOT NULL,
  `modelname` longtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `vehicle_categories`
--

CREATE TABLE `vehicle_categories` (
  `name` varchar(60) NOT NULL,
  `label` varchar(60) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `vehicle_categories`
--

INSERT INTO `vehicle_categories` (`name`, `label`) VALUES
('a', 'حمولة 40 كجم'),
('b', 'حمولة 60 كجم'),
('c', 'حمولة 80 كجم'),
('d', 'حمولة 120 كجم'),
('f5mh', 'فخمة'),
('sedans', 'سيدان'),
('sports', 'سبورت'),
('sportsclassics', 'كلاسيك'),
('super', 'سوبر'),
('suvs', 'كبير'),
('zmotorcycles', 'دراجة نارية');

-- --------------------------------------------------------

--
-- Table structure for table `vehicle_sold`
--

CREATE TABLE `vehicle_sold` (
  `client` varchar(50) NOT NULL,
  `model` varchar(50) NOT NULL,
  `plate` varchar(50) NOT NULL,
  `soldby` varchar(50) NOT NULL,
  `date` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `veh_km`
--

CREATE TABLE `veh_km` (
  `carplate` varchar(10) NOT NULL,
  `km` varchar(255) NOT NULL DEFAULT '0',
  `state` int(1) NOT NULL DEFAULT 0,
  `reset` int(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `weashops`
--

CREATE TABLE `weashops` (
  `id` int(11) NOT NULL,
  `zone` varchar(255) NOT NULL,
  `item` varchar(255) NOT NULL,
  `price` int(11) NOT NULL,
  `level` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `weashops`
--

INSERT INTO `weashops` (`id`, `zone`, `item`, `price`, `level`) VALUES
(1, 'GunShop', 'WEAPON_PISTOL', 15000, 25),
(2, 'BlackWeashop', 'WEAPON_PISTOL', 11000, 25),
(3, 'GunShop', 'WEAPON_FLASHLIGHT', 500, 5),
(4, 'BlackWeashop', 'WEAPON_FLASHLIGHT', 350, 5),
(5, 'GunShop', 'WEAPON_MACHETE', 350, 5),
(6, 'BlackWeashop', 'WEAPON_MACHETE', 250, 5),
(7, 'GunShop', 'WEAPON_NIGHTSTICK', 250, 5),
(8, 'BlackWeashop', 'WEAPON_NIGHTSTICK', 150, 5),
(9, 'GunShop', 'WEAPON_BAT', 100, 5),
(10, 'BlackWeashop', 'WEAPON_BAT', 100, 5),
(11, 'BlackWeashop', 'WEAPON_MICROSMG', 35000, 40),
(12, 'BlackWeashop', 'WEAPON_PUMPSHOTGUN', 65000, 36);

-- --------------------------------------------------------

--
-- Table structure for table `whatsapp_accounts`
--

CREATE TABLE `whatsapp_accounts` (
  `id` varchar(100) NOT NULL,
  `name` varchar(50) NOT NULL,
  `phone` varchar(50) NOT NULL,
  `password` varchar(50) NOT NULL,
  `avatar` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `whatsapp_chats`
--

CREATE TABLE `whatsapp_chats` (
  `id` int(11) NOT NULL,
  `phone` varchar(50) NOT NULL,
  `number` varchar(50) NOT NULL,
  `created` varchar(50) NOT NULL,
  `messages` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `whatsapp_groups`
--

CREATE TABLE `whatsapp_groups` (
  `id` int(11) NOT NULL,
  `phone` varchar(50) NOT NULL,
  `number` varchar(50) NOT NULL,
  `type` varchar(50) NOT NULL,
  `name` varchar(50) NOT NULL,
  `image` text NOT NULL,
  `created` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `whatsapp_groups_messages`
--

CREATE TABLE `whatsapp_groups_messages` (
  `id` int(11) NOT NULL,
  `id_group` varchar(50) NOT NULL,
  `owner` varchar(50) NOT NULL,
  `type` varchar(50) NOT NULL,
  `message` text NOT NULL,
  `created` varchar(50) NOT NULL,
  `read` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `whatsapp_groups_users`
--

CREATE TABLE `whatsapp_groups_users` (
  `number_group` varchar(50) NOT NULL,
  `admin` int(11) NOT NULL,
  `phone` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `whatsapp_stories`
--

CREATE TABLE `whatsapp_stories` (
  `phone` varchar(50) NOT NULL,
  `image` text NOT NULL,
  `created` varchar(50) NOT NULL,
  `description` varchar(50) DEFAULT NULL,
  `location` varchar(50) DEFAULT NULL,
  `filter` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `yellowpages_posts`
--

CREATE TABLE `yellowpages_posts` (
  `id` int(11) NOT NULL,
  `owner` text NOT NULL,
  `mesaj` text NOT NULL,
  `isim` text NOT NULL,
  `telno` text NOT NULL,
  `resim` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `yellow_tweets`
--

CREATE TABLE `yellow_tweets` (
  `id` int(11) NOT NULL,
  `phone_number` varchar(50) DEFAULT NULL,
  `firstname` varchar(256) DEFAULT NULL,
  `lastname` varchar(256) DEFAULT NULL,
  `message` varchar(256) NOT NULL,
  `image` varchar(256) DEFAULT NULL,
  `time` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `addon_account`
--
ALTER TABLE `addon_account`
  ADD PRIMARY KEY (`name`);

--
-- Indexes for table `addon_account_data`
--
ALTER TABLE `addon_account_data`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `index_addon_account_data_account_name_owner` (`account_name`,`owner`),
  ADD KEY `index_addon_account_data_account_name` (`account_name`);

--
-- Indexes for table `addon_inventory`
--
ALTER TABLE `addon_inventory`
  ADD PRIMARY KEY (`name`);

--
-- Indexes for table `addon_inventory_items`
--
ALTER TABLE `addon_inventory_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `index_addon_inventory_items_inventory_name_name` (`inventory_name`,`name`),
  ADD KEY `index_addon_inventory_items_inventory_name_name_owner` (`inventory_name`,`name`,`owner`),
  ADD KEY `index_addon_inventory_inventory_name` (`inventory_name`);

--
-- Indexes for table `aircrafts`
--
ALTER TABLE `aircrafts`
  ADD PRIMARY KEY (`model`) USING BTREE;

--
-- Indexes for table `aircrafts_categories`
--
ALTER TABLE `aircrafts_categories`
  ADD PRIMARY KEY (`name`) USING BTREE;

--
-- Indexes for table `aircrafts_for_sale`
--
ALTER TABLE `aircrafts_for_sale`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `bank_transfer`
--
ALTER TABLE `bank_transfer`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `bans`
--
ALTER TABLE `bans`
  ADD PRIMARY KEY (`license`);

--
-- Indexes for table `billing`
--
ALTER TABLE `billing`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `boats`
--
ALTER TABLE `boats`
  ADD PRIMARY KEY (`model`) USING BTREE;

--
-- Indexes for table `boats_categories`
--
ALTER TABLE `boats_categories`
  ADD PRIMARY KEY (`name`) USING BTREE;

--
-- Indexes for table `boats_for_sale`
--
ALTER TABLE `boats_for_sale`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `bought_houses`
--
ALTER TABLE `bought_houses`
  ADD PRIMARY KEY (`houseid`);

--
-- Indexes for table `businesses`
--
ALTER TABLE `businesses`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `bwh_bans`
--
ALTER TABLE `bwh_bans`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `bwh_identifiers`
--
ALTER TABLE `bwh_identifiers`
  ADD PRIMARY KEY (`steam`);

--
-- Indexes for table `bwh_warnings`
--
ALTER TABLE `bwh_warnings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `cardealer_vehicles`
--
ALTER TABLE `cardealer_vehicles`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `codem-hud-data`
--
ALTER TABLE `codem-hud-data`
  ADD UNIQUE KEY `identifier` (`identifier`) USING HASH;

--
-- Indexes for table `communityservice`
--
ALTER TABLE `communityservice`
  ADD PRIMARY KEY (`identifier`);

--
-- Indexes for table `criminal_record`
--
ALTER TABLE `criminal_record`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `daily_free`
--
ALTER TABLE `daily_free`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `darkchat_messages`
--
ALTER TABLE `darkchat_messages`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `id` (`id`) USING BTREE;

--
-- Indexes for table `datastore`
--
ALTER TABLE `datastore`
  ADD PRIMARY KEY (`name`);

--
-- Indexes for table `datastore_data`
--
ALTER TABLE `datastore_data`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `index_datastore_data_name_owner` (`name`,`owner`),
  ADD KEY `index_datastore_data_name` (`name`);

--
-- Indexes for table `doublexpusers`
--
ALTER TABLE `doublexpusers`
  ADD PRIMARY KEY (`identifier`) USING BTREE;

--
-- Indexes for table `fine_types`
--
ALTER TABLE `fine_types`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `gangs`
--
ALTER TABLE `gangs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `gas_station_balance`
--
ALTER TABLE `gas_station_balance`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gas_station_business`
--
ALTER TABLE `gas_station_business`
  ADD PRIMARY KEY (`gas_station_id`) USING BTREE;

--
-- Indexes for table `gas_station_jobs`
--
ALTER TABLE `gas_station_jobs`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gksphone_app_chat`
--
ALTER TABLE `gksphone_app_chat`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `gksphone_bank_transfer`
--
ALTER TABLE `gksphone_bank_transfer`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gksphone_blockednumber`
--
ALTER TABLE `gksphone_blockednumber`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gksphone_calls`
--
ALTER TABLE `gksphone_calls`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `gksphone_ebay`
--
ALTER TABLE `gksphone_ebay`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gksphone_gallery`
--
ALTER TABLE `gksphone_gallery`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gksphone_gps`
--
ALTER TABLE `gksphone_gps`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gksphone_group_message`
--
ALTER TABLE `gksphone_group_message`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `groupid` (`groupid`) USING BTREE;

--
-- Indexes for table `gksphone_instocomment`
--
ALTER TABLE `gksphone_instocomment`
  ADD KEY `id` (`id`) USING BTREE;

--
-- Indexes for table `gksphone_insto_accounts`
--
ALTER TABLE `gksphone_insto_accounts`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD UNIQUE KEY `username` (`username`) USING BTREE;

--
-- Indexes for table `gksphone_insto_instas`
--
ALTER TABLE `gksphone_insto_instas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK_gksphone_insto_instas_gksphone_insto_accounts` (`authorId`);

--
-- Indexes for table `gksphone_insto_likes`
--
ALTER TABLE `gksphone_insto_likes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK_gksphone_insto_likes_gksphone_insto_accounts` (`authorId`),
  ADD KEY `FK_gksphone_insto_likes_gksphone_insto_instas` (`inapId`);

--
-- Indexes for table `gksphone_insto_story`
--
ALTER TABLE `gksphone_insto_story`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `FK_gksphone_insto_story_gksphone_insto_accounts` (`authorId`) USING BTREE;

--
-- Indexes for table `gksphone_job_message`
--
ALTER TABLE `gksphone_job_message`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gksphone_lapraces`
--
ALTER TABLE `gksphone_lapraces`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `gksphone_mails`
--
ALTER TABLE `gksphone_mails`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gksphone_messages`
--
ALTER TABLE `gksphone_messages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `gksphone_messages_group`
--
ALTER TABLE `gksphone_messages_group`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gksphone_news`
--
ALTER TABLE `gksphone_news`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gksphone_settings`
--
ALTER TABLE `gksphone_settings`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gksphone_tinderacc`
--
ALTER TABLE `gksphone_tinderacc`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gksphone_tindermatch`
--
ALTER TABLE `gksphone_tindermatch`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gksphone_tindermessage`
--
ALTER TABLE `gksphone_tindermessage`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gksphone_twitter_accounts`
--
ALTER TABLE `gksphone_twitter_accounts`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD UNIQUE KEY `username` (`username`) USING BTREE;

--
-- Indexes for table `gksphone_twitter_likes`
--
ALTER TABLE `gksphone_twitter_likes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK_gksphone_twitter_likes_gksphone_twitter_accounts` (`authorId`),
  ADD KEY `FK_gksphone_twitter_likes_gksphone_twitter_tweets` (`tweetId`);

--
-- Indexes for table `gksphone_twitter_tweets`
--
ALTER TABLE `gksphone_twitter_tweets`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK_gksphone_twitter_tweets_gksphone_twitter_accounts` (`authorId`);

--
-- Indexes for table `gksphone_users_contacts`
--
ALTER TABLE `gksphone_users_contacts`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gksphone_vehicle_sales`
--
ALTER TABLE `gksphone_vehicle_sales`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `gksphone_yellow`
--
ALTER TABLE `gksphone_yellow`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `h_impounded_vehicles`
--
ALTER TABLE `h_impounded_vehicles`
  ADD PRIMARY KEY (`plate`);

--
-- Indexes for table `instagram_posts`
--
ALTER TABLE `instagram_posts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `instagram_stories`
--
ALTER TABLE `instagram_stories`
  ADD PRIMARY KEY (`owner`) USING BTREE;

--
-- Indexes for table `insto_accounts`
--
ALTER TABLE `insto_accounts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`);

--
-- Indexes for table `insto_instas`
--
ALTER TABLE `insto_instas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK_insto_instas_insto_accounts` (`authorId`);

--
-- Indexes for table `insto_likes`
--
ALTER TABLE `insto_likes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK_insto_likes_insto_accounts` (`authorId`),
  ADD KEY `FK_insto_likes_insto_instas` (`inapId`);

--
-- Indexes for table `items`
--
ALTER TABLE `items`
  ADD PRIMARY KEY (`name`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`name`);

--
-- Indexes for table `job_grades`
--
ALTER TABLE `job_grades`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `licenses`
--
ALTER TABLE `licenses`
  ADD PRIMARY KEY (`type`);

--
-- Indexes for table `owned_foodtrucks`
--
ALTER TABLE `owned_foodtrucks`
  ADD PRIMARY KEY (`plate`) USING BTREE;

--
-- Indexes for table `owned_properties`
--
ALTER TABLE `owned_properties`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `owned_vehicles`
--
ALTER TABLE `owned_vehicles`
  ADD PRIMARY KEY (`plate`);

--
-- Indexes for table `owned_vehicless`
--
ALTER TABLE `owned_vehicless`
  ADD PRIMARY KEY (`plate`);

--
-- Indexes for table `phone_ads`
--
ALTER TABLE `phone_ads`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id` (`id`);

--
-- Indexes for table `phone_alertjobs`
--
ALTER TABLE `phone_alertjobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `job` (`job`);

--
-- Indexes for table `phone_app_chat`
--
ALTER TABLE `phone_app_chat`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `phone_calls`
--
ALTER TABLE `phone_calls`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `phone_chatrooms`
--
ALTER TABLE `phone_chatrooms`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `room_code` (`room_code`);

--
-- Indexes for table `phone_chatroom_messages`
--
ALTER TABLE `phone_chatroom_messages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `phone_ch_reddit`
--
ALTER TABLE `phone_ch_reddit`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `phone_contacts`
--
ALTER TABLE `phone_contacts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id` (`id`);

--
-- Indexes for table `phone_darkgroups`
--
ALTER TABLE `phone_darkgroups`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id` (`id`);

--
-- Indexes for table `phone_groups`
--
ALTER TABLE `phone_groups`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id` (`id`);

--
-- Indexes for table `phone_invoices`
--
ALTER TABLE `phone_invoices`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `phone_mail`
--
ALTER TABLE `phone_mail`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id` (`id`);

--
-- Indexes for table `phone_messages`
--
ALTER TABLE `phone_messages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `phone_notifies`
--
ALTER TABLE `phone_notifies`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `phone_numbers`
--
ALTER TABLE `phone_numbers`
  ADD PRIMARY KEY (`identifier`) USING BTREE;

--
-- Indexes for table `phone_reddit`
--
ALTER TABLE `phone_reddit`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `phone_shops`
--
ALTER TABLE `phone_shops`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `phone_transactions`
--
ALTER TABLE `phone_transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id` (`id`);

--
-- Indexes for table `phone_tweets`
--
ALTER TABLE `phone_tweets`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id` (`id`);

--
-- Indexes for table `phone_users_contacts`
--
ALTER TABLE `phone_users_contacts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `playerhousing`
--
ALTER TABLE `playerhousing`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `player_contacts`
--
ALTER TABLE `player_contacts`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `identifier` (`identifier`) USING BTREE;

--
-- Indexes for table `player_gallery`
--
ALTER TABLE `player_gallery`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `player_mails`
--
ALTER TABLE `player_mails`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `identifier` (`identifier`) USING BTREE;

--
-- Indexes for table `player_notes`
--
ALTER TABLE `player_notes`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `properties`
--
ALTER TABLE `properties`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `qalleagent_brottsregister`
--
ALTER TABLE `qalleagent_brottsregister`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `qaller9abh_brottsregister`
--
ALTER TABLE `qaller9abh_brottsregister`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `qalletebexstore_brottsregister`
--
ALTER TABLE `qalletebexstore_brottsregister`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `qalle_brottsregister`
--
ALTER TABLE `qalle_brottsregister`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `race_tracks`
--
ALTER TABLE `race_tracks`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `rented_vehicles`
--
ALTER TABLE `rented_vehicles`
  ADD PRIMARY KEY (`plate`);

--
-- Indexes for table `salvzbanking_transactions`
--
ALTER TABLE `salvzbanking_transactions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `selling_cars`
--
ALTER TABLE `selling_cars`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `shipments`
--
ALTER TABLE `shipments`
  ADD PRIMARY KEY (`idd`);

--
-- Indexes for table `society_moneywash`
--
ALTER TABLE `society_moneywash`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `svstorem_shipments`
--
ALTER TABLE `svstorem_shipments`
  ADD PRIMARY KEY (`Command`) USING BTREE;

--
-- Indexes for table `tgiann_mdt_cezalar`
--
ALTER TABLE `tgiann_mdt_cezalar`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tgiann_mdt_olaylar`
--
ALTER TABLE `tgiann_mdt_olaylar`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tinder_accounts`
--
ALTER TABLE `tinder_accounts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tinder_likes`
--
ALTER TABLE `tinder_likes`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tinder_messages`
--
ALTER TABLE `tinder_messages`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `trucks`
--
ALTER TABLE `trucks`
  ADD PRIMARY KEY (`model`);

--
-- Indexes for table `trucks_for_sale`
--
ALTER TABLE `trucks_for_sale`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `truck_categories`
--
ALTER TABLE `truck_categories`
  ADD PRIMARY KEY (`name`);

--
-- Indexes for table `trunk_inventory`
--
ALTER TABLE `trunk_inventory`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `plate` (`plate`);

--
-- Indexes for table `twitter_hashtags`
--
ALTER TABLE `twitter_hashtags`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `twitter_likes`
--
ALTER TABLE `twitter_likes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK_twitter_likes_twitter_accounts` (`authorId`),
  ADD KEY `FK_twitter_likes_twitter_tweets` (`tweetId`);

--
-- Indexes for table `twitter_mentions`
--
ALTER TABLE `twitter_mentions`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `twitter_tweets`
--
ALTER TABLE `twitter_tweets`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`identifier`);

--
-- Indexes for table `user_licenses`
--
ALTER TABLE `user_licenses`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `vehicles`
--
ALTER TABLE `vehicles`
  ADD PRIMARY KEY (`model`);

--
-- Indexes for table `vehicles_for_sale`
--
ALTER TABLE `vehicles_for_sale`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `vehicle_categories`
--
ALTER TABLE `vehicle_categories`
  ADD PRIMARY KEY (`name`);

--
-- Indexes for table `vehicle_sold`
--
ALTER TABLE `vehicle_sold`
  ADD PRIMARY KEY (`plate`);

--
-- Indexes for table `veh_km`
--
ALTER TABLE `veh_km`
  ADD PRIMARY KEY (`carplate`),
  ADD UNIQUE KEY `carplate` (`carplate`),
  ADD KEY `carplate_2` (`carplate`);

--
-- Indexes for table `weashops`
--
ALTER TABLE `weashops`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `whatsapp_chats`
--
ALTER TABLE `whatsapp_chats`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `whatsapp_groups`
--
ALTER TABLE `whatsapp_groups`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `whatsapp_groups_messages`
--
ALTER TABLE `whatsapp_groups_messages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `yellowpages_posts`
--
ALTER TABLE `yellowpages_posts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `yellow_tweets`
--
ALTER TABLE `yellow_tweets`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `addon_account_data`
--
ALTER TABLE `addon_account_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=627;

--
-- AUTO_INCREMENT for table `addon_inventory_items`
--
ALTER TABLE `addon_inventory_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `aircrafts_for_sale`
--
ALTER TABLE `aircrafts_for_sale`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=59;

--
-- AUTO_INCREMENT for table `bank_transfer`
--
ALTER TABLE `bank_transfer`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `billing`
--
ALTER TABLE `billing`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=676;

--
-- AUTO_INCREMENT for table `boats_for_sale`
--
ALTER TABLE `boats_for_sale`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=53;

--
-- AUTO_INCREMENT for table `businesses`
--
ALTER TABLE `businesses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `bwh_bans`
--
ALTER TABLE `bwh_bans`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=35;

--
-- AUTO_INCREMENT for table `bwh_warnings`
--
ALTER TABLE `bwh_warnings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `cardealer_vehicles`
--
ALTER TABLE `cardealer_vehicles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `criminal_record`
--
ALTER TABLE `criminal_record`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=57;

--
-- AUTO_INCREMENT for table `daily_free`
--
ALTER TABLE `daily_free`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `darkchat_messages`
--
ALTER TABLE `darkchat_messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `datastore_data`
--
ALTER TABLE `datastore_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1698;

--
-- AUTO_INCREMENT for table `fine_types`
--
ALTER TABLE `fine_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=67;

--
-- AUTO_INCREMENT for table `gangs`
--
ALTER TABLE `gangs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `gas_station_balance`
--
ALTER TABLE `gas_station_balance`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=70;

--
-- AUTO_INCREMENT for table `gas_station_jobs`
--
ALTER TABLE `gas_station_jobs`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `gksphone_app_chat`
--
ALTER TABLE `gksphone_app_chat`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `gksphone_bank_transfer`
--
ALTER TABLE `gksphone_bank_transfer`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=67;

--
-- AUTO_INCREMENT for table `gksphone_blockednumber`
--
ALTER TABLE `gksphone_blockednumber`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `gksphone_calls`
--
ALTER TABLE `gksphone_calls`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=153;

--
-- AUTO_INCREMENT for table `gksphone_ebay`
--
ALTER TABLE `gksphone_ebay`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `gksphone_gallery`
--
ALTER TABLE `gksphone_gallery`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=33;

--
-- AUTO_INCREMENT for table `gksphone_gps`
--
ALTER TABLE `gksphone_gps`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `gksphone_group_message`
--
ALTER TABLE `gksphone_group_message`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `gksphone_instocomment`
--
ALTER TABLE `gksphone_instocomment`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `gksphone_insto_accounts`
--
ALTER TABLE `gksphone_insto_accounts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `gksphone_insto_instas`
--
ALTER TABLE `gksphone_insto_instas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `gksphone_insto_likes`
--
ALTER TABLE `gksphone_insto_likes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `gksphone_insto_story`
--
ALTER TABLE `gksphone_insto_story`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `gksphone_job_message`
--
ALTER TABLE `gksphone_job_message`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=120;

--
-- AUTO_INCREMENT for table `gksphone_lapraces`
--
ALTER TABLE `gksphone_lapraces`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `gksphone_mails`
--
ALTER TABLE `gksphone_mails`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `gksphone_messages`
--
ALTER TABLE `gksphone_messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=146;

--
-- AUTO_INCREMENT for table `gksphone_messages_group`
--
ALTER TABLE `gksphone_messages_group`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `gksphone_news`
--
ALTER TABLE `gksphone_news`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `gksphone_settings`
--
ALTER TABLE `gksphone_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=184;

--
-- AUTO_INCREMENT for table `gksphone_tinderacc`
--
ALTER TABLE `gksphone_tinderacc`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `gksphone_tindermatch`
--
ALTER TABLE `gksphone_tindermatch`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `gksphone_tindermessage`
--
ALTER TABLE `gksphone_tindermessage`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `gksphone_twitter_accounts`
--
ALTER TABLE `gksphone_twitter_accounts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `gksphone_twitter_likes`
--
ALTER TABLE `gksphone_twitter_likes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `gksphone_twitter_tweets`
--
ALTER TABLE `gksphone_twitter_tweets`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `gksphone_users_contacts`
--
ALTER TABLE `gksphone_users_contacts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `gksphone_vehicle_sales`
--
ALTER TABLE `gksphone_vehicle_sales`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `gksphone_yellow`
--
ALTER TABLE `gksphone_yellow`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `instagram_posts`
--
ALTER TABLE `instagram_posts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `insto_accounts`
--
ALTER TABLE `insto_accounts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=57;

--
-- AUTO_INCREMENT for table `insto_instas`
--
ALTER TABLE `insto_instas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=213;

--
-- AUTO_INCREMENT for table `insto_likes`
--
ALTER TABLE `insto_likes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=186;

--
-- AUTO_INCREMENT for table `job_grades`
--
ALTER TABLE `job_grades`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=111;

--
-- AUTO_INCREMENT for table `owned_properties`
--
ALTER TABLE `owned_properties`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=46;

--
-- AUTO_INCREMENT for table `phone_ads`
--
ALTER TABLE `phone_ads`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `phone_alertjobs`
--
ALTER TABLE `phone_alertjobs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `phone_app_chat`
--
ALTER TABLE `phone_app_chat`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `phone_calls`
--
ALTER TABLE `phone_calls`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=186;

--
-- AUTO_INCREMENT for table `phone_chatrooms`
--
ALTER TABLE `phone_chatrooms`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `phone_chatroom_messages`
--
ALTER TABLE `phone_chatroom_messages`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `phone_ch_reddit`
--
ALTER TABLE `phone_ch_reddit`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `phone_contacts`
--
ALTER TABLE `phone_contacts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `phone_darkgroups`
--
ALTER TABLE `phone_darkgroups`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `phone_groups`
--
ALTER TABLE `phone_groups`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `phone_invoices`
--
ALTER TABLE `phone_invoices`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `phone_mail`
--
ALTER TABLE `phone_mail`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `phone_messages`
--
ALTER TABLE `phone_messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `phone_notifies`
--
ALTER TABLE `phone_notifies`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=64;

--
-- AUTO_INCREMENT for table `phone_reddit`
--
ALTER TABLE `phone_reddit`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `phone_shops`
--
ALTER TABLE `phone_shops`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `phone_transactions`
--
ALTER TABLE `phone_transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `phone_tweets`
--
ALTER TABLE `phone_tweets`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `phone_users_contacts`
--
ALTER TABLE `phone_users_contacts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `player_contacts`
--
ALTER TABLE `player_contacts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=38;

--
-- AUTO_INCREMENT for table `player_gallery`
--
ALTER TABLE `player_gallery`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `player_mails`
--
ALTER TABLE `player_mails`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `player_notes`
--
ALTER TABLE `player_notes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `properties`
--
ALTER TABLE `properties`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=73;

--
-- AUTO_INCREMENT for table `qalleagent_brottsregister`
--
ALTER TABLE `qalleagent_brottsregister`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT for table `qaller9abh_brottsregister`
--
ALTER TABLE `qaller9abh_brottsregister`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=44;

--
-- AUTO_INCREMENT for table `qalletebexstore_brottsregister`
--
ALTER TABLE `qalletebexstore_brottsregister`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=234;

--
-- AUTO_INCREMENT for table `qalle_brottsregister`
--
ALTER TABLE `qalle_brottsregister`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=36;

--
-- AUTO_INCREMENT for table `race_tracks`
--
ALTER TABLE `race_tracks`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `salvzbanking_transactions`
--
ALTER TABLE `salvzbanking_transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=166;

--
-- AUTO_INCREMENT for table `selling_cars`
--
ALTER TABLE `selling_cars`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=50;

--
-- AUTO_INCREMENT for table `shipments`
--
ALTER TABLE `shipments`
  MODIFY `idd` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `society_moneywash`
--
ALTER TABLE `society_moneywash`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `svstorem_shipments`
--
ALTER TABLE `svstorem_shipments`
  MODIFY `Command` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2670;

--
-- AUTO_INCREMENT for table `tgiann_mdt_cezalar`
--
ALTER TABLE `tgiann_mdt_cezalar`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1643;

--
-- AUTO_INCREMENT for table `tgiann_mdt_olaylar`
--
ALTER TABLE `tgiann_mdt_olaylar`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1197;

--
-- AUTO_INCREMENT for table `tinder_accounts`
--
ALTER TABLE `tinder_accounts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `tinder_likes`
--
ALTER TABLE `tinder_likes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `tinder_messages`
--
ALTER TABLE `tinder_messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `trucks_for_sale`
--
ALTER TABLE `trucks_for_sale`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=62;

--
-- AUTO_INCREMENT for table `trunk_inventory`
--
ALTER TABLE `trunk_inventory`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=435;

--
-- AUTO_INCREMENT for table `twitter_hashtags`
--
ALTER TABLE `twitter_hashtags`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `twitter_likes`
--
ALTER TABLE `twitter_likes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=138;

--
-- AUTO_INCREMENT for table `twitter_mentions`
--
ALTER TABLE `twitter_mentions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `twitter_tweets`
--
ALTER TABLE `twitter_tweets`
  MODIFY `id` int(5) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT for table `user_licenses`
--
ALTER TABLE `user_licenses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=378;

--
-- AUTO_INCREMENT for table `vehicles_for_sale`
--
ALTER TABLE `vehicles_for_sale`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=141;

--
-- AUTO_INCREMENT for table `weashops`
--
ALTER TABLE `weashops`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=41;

--
-- AUTO_INCREMENT for table `whatsapp_chats`
--
ALTER TABLE `whatsapp_chats`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=43;

--
-- AUTO_INCREMENT for table `whatsapp_groups`
--
ALTER TABLE `whatsapp_groups`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `whatsapp_groups_messages`
--
ALTER TABLE `whatsapp_groups_messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `yellowpages_posts`
--
ALTER TABLE `yellowpages_posts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `yellow_tweets`
--
ALTER TABLE `yellow_tweets`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=895;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `gksphone_group_message`
--
ALTER TABLE `gksphone_group_message`
  ADD CONSTRAINT `FK_phonegroupmessage` FOREIGN KEY (`groupid`) REFERENCES `gksphone_messages_group` (`id`);

--
-- Constraints for table `gksphone_insto_instas`
--
ALTER TABLE `gksphone_insto_instas`
  ADD CONSTRAINT `FK_gksphone_insto_instas_gksphone_insto_accounts` FOREIGN KEY (`authorId`) REFERENCES `gksphone_insto_accounts` (`id`);

--
-- Constraints for table `gksphone_insto_likes`
--
ALTER TABLE `gksphone_insto_likes`
  ADD CONSTRAINT `FK_gksphone_insto_likes_gksphone_insto_accounts` FOREIGN KEY (`authorId`) REFERENCES `gksphone_insto_accounts` (`id`),
  ADD CONSTRAINT `FK_gksphone_insto_likes_gksphone_insto_instas` FOREIGN KEY (`inapId`) REFERENCES `gksphone_insto_instas` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `gksphone_insto_story`
--
ALTER TABLE `gksphone_insto_story`
  ADD CONSTRAINT `FK_gksphone_insto_story_gksphone_insto_accounts` FOREIGN KEY (`authorId`) REFERENCES `gksphone_insto_accounts` (`id`);

--
-- Constraints for table `gksphone_twitter_likes`
--
ALTER TABLE `gksphone_twitter_likes`
  ADD CONSTRAINT `FK_gksphone_twitter_likes_gksphone_twitter_accounts` FOREIGN KEY (`authorId`) REFERENCES `gksphone_twitter_accounts` (`id`),
  ADD CONSTRAINT `FK_gksphone_twitter_likes_gksphone_twitter_tweets` FOREIGN KEY (`tweetId`) REFERENCES `gksphone_twitter_tweets` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `gksphone_twitter_tweets`
--
ALTER TABLE `gksphone_twitter_tweets`
  ADD CONSTRAINT `FK_gksphone_twitter_tweets_gksphone_twitter_accounts` FOREIGN KEY (`authorId`) REFERENCES `gksphone_twitter_accounts` (`id`);

--
-- Constraints for table `insto_instas`
--
ALTER TABLE `insto_instas`
  ADD CONSTRAINT `FK_insto_instas_insto_accounts` FOREIGN KEY (`authorId`) REFERENCES `insto_accounts` (`id`);

--
-- Constraints for table `insto_likes`
--
ALTER TABLE `insto_likes`
  ADD CONSTRAINT `FK_insto_likes_insto_accounts` FOREIGN KEY (`authorId`) REFERENCES `insto_accounts` (`id`),
  ADD CONSTRAINT `FK_insto_likes_insto_instas` FOREIGN KEY (`inapId`) REFERENCES `insto_instas` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
