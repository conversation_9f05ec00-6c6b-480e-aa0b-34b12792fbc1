@font-face {
    font-family: def;
    src: url(../fonts/SquadaOne-Regular.ttf);
    font-display: swap;
}

@font-face {
    font-family: def2;
    src: url(../fonts/Dosis-Medium.ttf);
    font-display: swap;
}

*:focus {
    outline: none;
}

body {
    overflow: hidden;
}

.wrapper {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
}

.container {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    width: 28%;
    height: 22%;
    border-radius: 1.0vh;
    border: 1.2vh solid #373737;

    background-color: #dddddd;
}

.container span {
    position: relative;
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-family: def;
    text-transform: uppercase;
    font-size: 5.0vh;
    margin-top: 1.0%;
    width: 100%;
    height: 15%;
}

.input-text {
    display: flex;
    justify-content: center;
    align-content: center;
    text-align: center;
    font-size: 10vh;
    width: 90%;
    height: 50%;
    border: none;
    background-color: #dddddd;
    margin-bottom: 5%;
    font-family: def2;
    text-transform: uppercase;
}

.btn-container {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 28%;
    height: 8%;

    margin-top: 0.5%;
}

.btn-container button {
    width: 40%;
    height: 70%;

    border: none;
    border-radius: 0.5vh;
    box-shadow:0 0.3vh 0.4vh #00000058, 0 0 20vh #0000001a;

    font-family: def2;
    font-size: 2.5vh;
    font-weight: 700;
    background-color: #DBDBDB;
    transition: 0.2s;
}

.btn-container button:nth-of-type(1):hover {
    background-color: rgb(33, 131, 221);
}

.btn-container button:nth-of-type(2):hover {
    background-color: rgb(233, 29, 56);
}