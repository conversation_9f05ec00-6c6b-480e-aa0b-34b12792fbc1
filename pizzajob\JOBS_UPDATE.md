# تحديث الرتب المسموحة - Jobs Access Update

## التحديثات المطبقة / Applied Updates

### 🎽 تغيير اليد إلى 41
- تم تغيير إعداد اليد من 40 إلى **41** للذكور والإناث

### 🚴‍♂️ الرتب المسموحة لركوب دباب الدلفري

**قبل التحديث:** فقط `pizzajob`
**بعد التحديث:** 
- `pizzajob` (الدلفري)
- `admin` (الإدارة) 
- `mechanic` (الميكانيك)

### 🔧 التغييرات التقنية / Technical Changes

#### 1. إعدادات الكونفيج الجديدة:
```lua
Config.JobRestriction = {
    enabled = true,
    jobName = 'pizzajob', -- للتوصيل فقط
    allowedJobs = {'pizzajob', 'admin', 'mechanic'} -- للركوب
}
```

#### 2. دالة جديدة في الخادم:
- `canRidePizzaBike()` - للتحقق من إمكانية ركوب الدباب
- `hasRequiredJob()` - للتحقق من إمكانية بدء التوصيل

### 📋 الفرق بين الوظائف / Job Functions Difference

#### للتوصيل (Delivery):
- **المطلوب:** رتبة `pizzajob` فقط
- **الوظائف:** بدء التوصيل، تسليم الطلبات، الحصول على المال والخبرة

#### لركوب الدباب (Vehicle Access):
- **المسموح:** `pizzajob`, `admin`, `mechanic`
- **الوظائف:** ركوب دباب الدلفري فقط (بدون تسليم)

### 🎯 كيفية العمل الآن / How It Works Now

#### للإدارة والميكانيك:
1. ✅ يمكنهم ركوب دباب الدلفري
2. ❌ لا يمكنهم بدء التوصيل
3. ❌ لا يمكنهم تسليم الطلبات
4. ❌ لا يظهر لهم البلب أو القوائم

#### لعمال الدلفري:
1. ✅ يمكنهم ركوب دباب الدلفري
2. ✅ يمكنهم بدء التوصيل
3. ✅ يمكنهم تسليم الطلبات
4. ✅ يظهر لهم البلب والقوائم

### 🔒 الأمان والحماية / Security & Protection

#### الرسائل المحدثة:
- **قبل:** "هذه الدراجة مخصصة للدلفري فقط!"
- **بعد:** "هذه الدراجة مخصصة للدلفري والإدارة والميكانيك فقط!"

#### نقاط الحماية:
1. فحص عند محاولة الدخول للمركبة
2. فحص عند القيادة
3. فحص دوري كل 5 ثوان
4. فحص عند التفاعل مع المركبة

### 📁 الملفات المعدلة / Modified Files

- `config.lua` - إضافة الرتب المسموحة وتغيير اليد إلى 41
- `server/main.lua` - إضافة دالة `canRidePizzaBike()` وتحديث الفحوصات
- `JOBS_UPDATE.md` - هذا الملف (جديد)

### 🎮 أمثلة للاستخدام / Usage Examples

#### مدير يريد فحص دباب:
1. يقترب من دباب الدلفري
2. يركب الدباب بدون مشاكل ✅
3. لا يمكنه بدء التوصيل ❌

#### ميكانيك يريد إصلاح دباب:
1. يقترب من دباب الدلفري
2. يركب الدباب للفحص ✅
3. يقوم بالإصلاحات المطلوبة
4. لا يمكنه استخدامه للتوصيل ❌

#### عامل دلفري:
1. يقترب من الطباخ
2. يبدأ التوصيل ✅
3. يحصل على دباب ✅
4. يقوم بالتوصيل ويحصل على المال ✅

### ⚠️ ملاحظات مهمة / Important Notes

1. **الرتب حساسة للأحرف** - تأكد من كتابة `admin` و `mechanic` بالضبط
2. **يجب إعادة تشغيل المورد** لتطبيق التغييرات
3. **الإدارة والميكانيك لا يحصلون على مال** من التوصيل
4. **فقط عمال الدلفري يمكنهم بدء التوصيل**

### 🔄 لتطبيق التحديث / To Apply Update

```bash
# في وحدة التحكم
restart pizzajob
```

### 🧪 للاختبار / For Testing

```lua
-- إعطاء رتبة إدارة (ESX)
/setjob [player_id] admin 0

-- إعطاء رتبة ميكانيك (ESX)  
/setjob [player_id] mechanic 0

-- إعطاء رتبة دلفري (ESX)
/setjob [player_id] pizzajob 0
```

### 📊 ملخص التغييرات / Changes Summary

| العنصر | القيمة السابقة | القيمة الجديدة |
|--------|----------------|-----------------|
| اليد | 40 | **41** |
| الرتب المسموحة | pizzajob فقط | pizzajob, admin, mechanic |
| رسالة المنع | للدلفري فقط | للدلفري والإدارة والميكانيك |

النظام الآن يدعم الرتب الثلاث مع الحفاظ على الأمان! 🎉
