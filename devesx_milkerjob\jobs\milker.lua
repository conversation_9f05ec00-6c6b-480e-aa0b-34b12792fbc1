-- تم انشاء هاذي الوظيفة من قبل      من قبل مجتمع esx العربي       dev_esx  https://discord.gg/TnuKAfUkSV
-- تم انشاء هاذي الوظيفة من قبل      من قبل مجتمع esx العربي       dev_esx  https://discord.gg/TnuKAfUkSV
-- تم انشاء هاذي الوظيفة من قبل      من قبل مجتمع esx العربي       dev_esx  https://discord.gg/TnuKAfUkSV
-- تم انشاء هاذي الوظيفة من قبل      من قبل مجتمع esx العربي       dev_esx  https://discord.gg/TnuKAfUkSV

Config.Jobs.milker = {
    BlipInfos = {
      Sprite = 141,
      Color = 5
    },
    Vehicles = {
      Truck = {
        Spawner = 1,
        Hash = "8977",
        Trailer = "none",
        HasCaution = true
      }
    },
    Zones = {
      CloakRoom = {
        --Pos   = {x = -1071.1319580078, y = -2003.7891845703, z = 15.78551197052},
        Pos   = {x = 2564.0916, y = 4680.5615, z = 34.0768},
        Size  = {x = 1.5, y = 1.5, z = 1.5},
        Color = {r = 225, g = 225, b = 225},
        Marker= 21,
        Blip  = true,
        Name  = _U('s_milker_locker'),
        Type  = "cloakroom",
        Hint  = _U('cloak_change'),
      },
      CloakRooms = {
        --Pos   = {x = -1071.1319580078, y = -2003.7891845703, z = 15.78551197052},
        Pos   = {x = 766.7117,  y = -2481.9109, z = 20.1654},
        Size  = {x = 1.5, y = 1.5, z = 1.5},
        Color = {r = 225, g = 225, b = 225},
        Marker= 21,
        Blip  = true,
        Name  = _U('s_milker_locker'),
        Type  = "cloakroom",
        Hint  = _U('cloak_change'),
      },
  
      Cow = {
        Pos   = {x = 2472.5237, y = 4761.9932, z = 34.3041-0.9},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 1,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_milk'),
            db_name= "milk_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "milk_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },


      -------------------------------------
      Cow2 = {
        Pos   = {x = 2464.2256, y = 4769.5986, z = 34.3654-0.9},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 1,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_milk'),
            db_name= "milk_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "milk_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow3 = {
        Pos   = {x = 2456.2971, y = 4777.4927, z = 34.4914-0.9},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 1,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_milk'),
            db_name= "milk_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "milk_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow4 = {
        Pos   = {x = 2448.6748, y = 4785.7925, z = 34.6292-0.9},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 1,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_milk'),
            db_name= "milk_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "milk_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow5 = {
        Pos   = {x = 2440.5410, y = 4793.6143, z = 34.6567-0.9},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 1,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_milk'),
            db_name= "milk_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "milk_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow6 = {
        Pos   = {x = 2432.6638, y = 4802.5869, z = 34.8160-0.9},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 1,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_milk'),
            db_name= "milk_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "milk_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow7 = {
        Pos   = {x = 2400.4951, y = 4777.4849, z = 34.5311-0.9},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 1,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_milk'),
            db_name= "milk_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "milk_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow8 = {
        Pos   = {x = 2408.8181, y = 4768.3477, z = 34.3016-0.9},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 1,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_milk'),
            db_name= "milk_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "milk_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow9 = {
        Pos   = {x = 2417.3918, y = 4761.7988, z = 34.3043-0.9},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 1,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_milk'),
            db_name= "milk_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "milk_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow10 = {
        Pos   = {x = 2425.0381, y = 4752.8809, z = 34.3041-0.9},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 1,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_milk'),
            db_name= "milk_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "milk_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow11 = {
        Pos   = {x = 2433.3293, y = 4746.1377, z = 34.3040-0.9},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 1,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_milk'),
            db_name= "milk_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "milk_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      Cow12 = {
        Pos   = {x = 2440.4795, y = 4736.7998, z = 34.3002-0.9},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 1,
        Blip  = true,
        Name  = _U('cow_hamada'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_bucket_milk'),
            db_name= "milk_bucket",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "milk_tool",
            requires_name = _U('s_tool_name'),
            drop   = 100
          }
        },
        Hint  = _U('s_getbucket')
      },
      
      -------------------------------
  
      Bottle = {
        Pos   = {x = 2498.6230, y = 4802.5928, z = 34.00400},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 1,
        Blip  = true,
        Name  = _U('s_getbottle'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_milker_name'),
            db_name= "milk_bottle",
            time   = 2000,
            max    = 20,
            add    = 1,
            remove = 1,
            requires = "milk_bucket",
            requires_name = _U('s_bucket_milk'),
            drop   = 100
          }
        },
        Hint  = _U('s_milkononbottle')
      },
  
      Packaging = {
        Pos   = {x = 2572.8323, y = 4692.3740, z = 33.1823},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Color = {r = 225, g = 225, b = 225},
        Marker= 1,
        Blip  = true,
        Name  = _U('s_packagemilk'),
        Type  = "work",
        Item  = {
          {
            name   = _U('s_packagedmilk'),
            db_name= "milk_box",
            time   = 2000,
            max    = 100,
            add    = 5,
            remove = 1,
            requires = "milk_bottle",
            requires_name = _U('s_unpackagedmilk'),
            drop   = 100
          }
        },
        Hint  = _U('s_unpackagedmilk_button')
      },
  
      VehicleSpawner = {
        Pos   = {x = 2547.8403, y = 4669.3257, z = 34.0716},
        Size  = {x = 1.5, y = 1.5, z = 1.5},
        Color = {r = 225, g = 225, b = 225},
        Marker= 39,
        Blip  = false,
        Name  = _U('spawn_veh'),
        Type  = "vehspawner",
        Spawner = 1,
        Hint  = _U('spawn_veh_button'),
        Caution = 2000
      },
  
      VehicleSpawnPoint = {
        Pos   = {x = 2578.1045, y = 4634.2480, z = 33.6394},
        Size  = {x = 3.0, y = 3.0, z = 1.0},
        Marker= -1,
        Blip  = false,
        Name  = _U('service_vh'),
        Type  = "vehspawnpt",
        Spawner = 1,
        Heading = 133.2
      },
  
      VehicleDeletePoint = {
        Pos   = {x = 2553.1577, y = 4669.8765, z = 34.4635},
        Size  = {x = 3.0, y = 3.0, z = 3.0},
        Color = {r = 255, g = 0, b = 0},
        Marker= 39,
        Blip  = false,
        Name  = _U('return_vh'),
        Type  = "vehdelete",
        Hint  = _U('return_vh_button'),
        Spawner = 1,
        Caution = 2000,
        GPS = 0,
        Teleport = 0
      },
  
      Delivery = {
        Pos   = {
            [1] = {x = 861.5342, y = -3026.1326, z = 5.7422},
            [2] = {x = -171.73, y = -2388.47, z = 6.0},
            [3] = {x = -1408.03, y = -2573.41, z = 13.95},
            [4] = {x = 861.5342, y = -3026.1326, z = 5.7422},
            [11] = {x = 861.5342, y = -3026.1326, z = 5.7422},
            [12] = {x = -171.73, y = -2388.47, z = 6.0},
            [13] = {x = -1408.03, y = -2573.41, z = 13.95},
            [14] = {x = 861.5342, y = -3026.1326, z = 5.7422},
        },	
          Size  = {
              [1] = {x = 16.0, y = 16.0, z = 1.0},
              [2] = {x = 7.0, y = 7.0, z = 7.0},
              [3] = {x = 12.0, y = 12.0, z = 1.0},
              [4] = {x = 16.0, y = 16.0, z = 1.0},
              [11] = {x = 16.0, y = 16.0, z = 1.0},
              [12] = {x = 16.0, y = 16.0, z = 1.0},
              [13] = {x = 16.0, y = 16.0, z = 1.0},
              [14] = {x = 16.0, y = 16.0, z = 1.0},
          },	
          Color = {r = 225, g = 225, b = 225},
          Marker= -1,
          Blip  = true,
          Name  = _U('milk_delivery_point'),
          Type  = "delivery",
          Spawner = 1,
          Item  = {
              {
              name   = _U('delivery'),
              time   = 1500,
              remove = 1,
              max    = 100, -- if not present, probably an error at itemQtty >= item.max in esx_jobs_sv.lua
              price  = Config.Prices.milker,
              xp  = Config.Xp.milker,
              requires = "milk_box",
              requires_name = _U('s_packagedmilk'),
              drop   = 100
              }
          },
          Hint  = _U('milk_deliver')
      }
    }
  }
  