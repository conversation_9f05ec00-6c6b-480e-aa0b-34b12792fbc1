Locales['sv'] = {
  -- Cloakroom
  ['cloakroom'] = 'omklädnignsrum',
  ['ems_clothes_civil'] = 'civilkläder',
  ['ems_clothes_ems'] = 'ambulanskläder',
  -- Vehicles
  ['ambulance'] = 'ambulans',
  ['helicopter_prompt'] = 'tryck ~INPUT_CONTEXT~ för att komma åt ~y~helikoptergaraget~s~.',
  ['garage_prompt'] = 'tryck ~INPUT_CONTEXT~ för att komma åt ~y~garaget~s~.',
  ['garage_title'] = 'garage',
  ['garage_stored'] = 'inställt',
  ['garage_notstored'] = 'uttaget',
  ['garage_storing'] = 'vi håller på att ställa in ditt fordon, se till att ingen är i närheten.',
  ['garage_has_stored'] = 'fordonet har ställts in i garaget',
  ['garage_has_notstored'] = 'inget ägt fordon finns i närheten',
  ['garage_notavailable'] = 'ditt fordon är inte inställt i ditt garage.',
  ['garage_blocked'] = 'det finns ingen tillgänglig plats att ställa ut fordonet på!',
  ['garage_empty'] = 'du har inga fordon i ditt garage.',
  ['garage_released'] = 'ditt fordon har tagits ut från garaget.',
  ['garage_store_nearby'] = 'det finns inget fordon i närheten.',
  ['garage_storeditem'] = 'öppna garage',
  ['garage_storeitem'] = 'ställ in fordon',
  ['garage_buyitem'] = 'fordonshandel',
  ['shop_item'] = '%s SEK',
  ['vehicleshop_title'] = 'fordonshandel',
  ['vehicleshop_confirm'] = 'vill du köpa detta fordon?',
  ['vehicleshop_bought'] = 'du köpte ~y~%s~s~ för ~g~%s SEK~s~',
  ['vehicleshop_money'] = 'du har inte råd med detta fordon',
  ['vehicleshop_awaiting_model'] = 'fordonet ~g~LADDAS NED OCH LADDAS IN~s~ var god vänta',
  ['confirm_no'] = 'nej',
  ['confirm_yes'] = 'ja',
  -- Action Menu
  ['revive_inprogress'] = 'en återupplivning har börjats',
  ['revive_complete'] = 'du har återupplivat ~y~%s~s~, bra jobbat!',
  ['revive_complete_award'] = 'du har återupplivat ~y~%s~s~ och tjänat ~g~%s SEK~s~ för dina tjänster, bra jobbat!',
  ['revive_fail_offline'] = 'den spelaren har lämnat servern',
  ['heal_inprogress'] = 'du behandlar patienten!',
  ['heal_complete'] = 'du har behandlat ~y~%s~s~',
  ['no_players'] = 'det finns inga spelare i närheten!',
  ['player_not_unconscious'] = 'spelaren är inte medvetslös!',
  ['player_not_conscious'] = 'spelaren är inte vid medvetande!',
  -- Boss Menu
  ['boss_actions'] = 'chefsmeny',
  -- Misc
  ['invalid_amount'] = '~r~Ogiltig mängd',
  ['actions_prompt'] = 'tryck ~INPUT_CONTEXT~ för att öppna menyn.',
  ['deposit_amount'] = 'mängd att sätta in',
  ['money_withdraw'] = 'mängd att ta ut',
  ['fast_travel'] = 'tryck på ~INPUT_CONTEXT~ för att snabbresa.',
  ['open_pharmacy'] = 'tryck på ~INPUT_CONTEXT~ för att öppna apoteket.',
  ['pharmacy_menu_title'] = 'apotek',
  ['pharmacy_take'] = 'ta <span style="color:blue;">%s</span>',
  ['medikit'] = 'akutväska',
  ['bandage'] = 'bandage',
  ['max_item'] = 'du har redan tillräckligt mycket på dig.',
  -- F6 Menu
  ['ems_menu'] = 'ambulansmeny',
  ['ems_menu_title'] = 'ambulansmeny',
  ['ems_menu_revive'] = 'återuppliva spelare',
  ['ems_menu_putincar'] = 'sätt i fordon',
  ['ems_menu_small'] = 'behandla mindre skador',
  ['ems_menu_big'] = 'behandla svåra skador',
  ['ems_menu_search'] = 'patienten hittades inte',
  -- Phone
  ['alert_ambulance'] = 'ambulanssamtal',
  -- Death
  ['respawn_available_in'] = 'respawn tillgänglig om ~b~%s:%s~s~',
  ['respawn_bleedout_in'] = 'du kommer att förblöda om ~b~%s:%s~s~\n',
  ['respawn_bleedout_prompt'] = 'håll [~b~E~s~] för att respawna',
  ['respawn_bleedout_fine'] = 'håll [~b~E~s~] för att respawna, kostar ~g~%s SEK~s~',
  ['respawn_bleedout_fine_msg'] = 'du betalade ~r~%s SEK~s~ för att respawna.',
  ['distress_send'] = 'tryck ~b~[G]~s~ för att skicka nödsignal',
  ['distress_sent'] = 'nödsignal har skickats till samtliga enheter!',
  ['combatlog_message'] = 'du har force-respawnats på grund av att du lämnat servern medans du var död',
  -- Revive
  ['revive_help'] = 'återuppliva en spelare',
  -- Item
  ['used_medikit'] = 'du har använt ~y~1x~s~ ~b~Akutväska~s~',
  ['used_bandage'] = 'du har använt ~y~1x~s~ ~b~Bandage~s~',
  ['not_enough_medikit'] = 'du har ingen ~b~Akutväska~s~.',
  ['not_enough_bandage'] = 'du har inget ~b~Bandage~s~.',
  ['healed'] = 'dina skador har blivit behandlade.',
  -- Blips
  ['blip_hospital'] = 'sjukhus',
  ['blip_dead'] = 'medvetslös människa',
}
