Config.Jobs.waterr = {
  BlipInfos = {
    Sprite = 751,
    Color = 26
  },
  Vehicles = {
    Truck = {
      Spawner = 1,
      Hash = "jrl48",
      Trailer = "none",
      HasCaution = true
    }
  },
  Zones = {
    CloakRoom = {
      --Pos   = {x = -1071.1319580078, y = -2003.7891845703, z = 15.78551197052},
      Pos   = {x = 1670.8756, y = -63.3470, z = 173.7762},
      Size  = {x = 1.5, y = 1.5, z = 1.5},
      Color = {r = 28, g = 252, b = 255},
      Marker= 21,
      Blip  = true,
      Name  = _U('wa_waterr_locker'),
      Type  = "cloakroom",
      Hint  = _U('cloak_change'),
    },

    CloakRoom2 = {
      --Pos   = {x = -1071.1319580078, y = -2003.7891845703, z = 15.78551197052},
      Pos   = {x = 774.7945, y = -2475.0500, z = 20.2663},
      Size  = {x = 1.5, y = 1.5, z = 1.5},
      Color = {r = 28, g = 252, b = 255},
      Marker= 21,
      Blip  = false,
      Name  = _U('wa_waterr_locker'),
      Type  = "cloakroom",
      Hint  = _U('cloak_change'),
    },

    waterrmloth = {
      Pos   = {x = 1691.1785, y = 42.7681, z =160.8},
      Size  = {x = 3.0, y = 3.0, z = 1.0},
      Color = {r = 28, g = 252, b = 255},
      Marker= 1,
      Blip  = true,
      Name  = _U('wa_mloth1'),
      Type  = "work",
      Item  = {
        {
          name   = _U('wa_mloth3'),
          db_name= "wa_mloth",
          time   = 800,
          max    = 20,
          add    = 1,
          remove = 1,
          requires = "wa_tool",
          requirewa_name = _U('waterr_kit'),
          drop   = 100
        }
      },
      Hint  = _U('wa_waterrmloth')
    },

    waterrghermohlaa = {
      Pos   = {x = 1849.0120, y = 294.7617, z = 161.0},
      Size  = {x = 3.0, y = 3.0, z = 1.0},
      Color = {r = 28, g = 252, b = 255},
      Marker= 1,
      Blip  = true,
      Name  = _U('wa_waterrghermohlaa'),
      Type  = "work",
      Item  = {
        {
          name   = _U('wa_waterrghermohlaa1'),
          db_name= "wa_ghermohla",
          time   = 800,
          max    = 20,
          add    = 1,
          remove = 1,
          requires = "wa_mloth",
          requirewa_name = _U('wa_mloth3'),
          drop   = 100
        }
      },
      Hint  = _U('wa_waterrghermohlaa4')
    },

    waterrmohlaa = {
      Pos   = {x = 1870.7792, y = 272.3534, z = 163.2},
      Size  = {x = 3.0, y = 3.0, z = 1.0},
      Color = {r = 28, g = 252, b = 255},
      Marker= 1,
      Blip  = true,
      Name  = _U('wa_mohlaa2'),
      Type  = "work",
      Item  = {
        {
          name   = _U('wa_mohlaa1'),
          db_name= "wa_mohlaa",
          time   = 800,
          max    = 100,
          add    = 5,
          remove = 1,
          requires = "wa_ghermohla",
          requirewa_name = _U('wa_mohlaa4'),
          drop   = 100
        }
      },
      Hint  = _U('wa_wa_mohlaa_button')
    },

    VehicleSpawner = {
      Pos   = {x = 1663.3512, y = 1.6444, z = 173.7751},
      Size  = {x = 1.5, y = 1.5, z = 1.5},
      Color = {r = 28, g = 252, b = 255},
      Marker= 39,
      Blip  = false,
      Name  = _U('spawn_veh'),
      Type  = "vehspawner",
      Spawner = 1,
      Hint  = _U('spawn_veh_button'),
      Caution = 2000
    },

    VehicleSpawnPoint = {
      Pos   = {x = 1648.3169, y = 13.4993, z = 173.5320},
      Size  = {x = 3.0, y = 3.0, z = 1.0},
      Marker= -1,
      Blip  = false,
      Name  = _U('service_vh'),
      Type  = "vehspawnpt",
      Spawner = 1,
      Heading = 45.2150
    },

    VehicleDeletePoint = {
      Pos   = {x = 1660.2783, y = 47.0882, z = 172.2487},
      Size  = {x = 5.0, y = 5.0, z = 5.0},
      Color = {r = 255, g = 0, b = 0},
      Marker= 39,
      Blip  = false,
      Name  = _U('return_vh'),
      Type  = "vehdelete",
      Hint  = _U('return_vh_button'),
      Spawner = 1,
      Caution = 2000,
      GPS = 0,
      Teleport = 0
    },

    Delivery = {
    Pos   = {
      [1] = {x = 1119.2563, y = -2920.8918, z = 5.9021},
      [2] = {x = -265.1572, y = -2388.5525, z = 6.0003},
      [3] = {x = -1408.03, y = -2573.41, z = 13.95},
      [4] = {x = 1139.43, y = -3190.0, z = 4.9},
      [11] = {x = 1139.43, y = -3190.0, z = 4.9},
      [12] = {x = 1139.43, y = -3190.0, z = 4.9},
      [13] = {x = 1139.43, y = -3190.0, z = 4.9},
      [14] = {x = 1139.43, y = -3190.0, z = 4.9},
    },	
    Size  = {
      [1] = {x = 32.0, y = 32.0, z = 3.0},
      [2] = {x = 32.0, y = 32.0, z = 3.0},
      [3] = {x = 12.0, y = 12.0, z = 1.0},
      [4] = {x = 16.0, y = 16.0, z = 1.0},
      [11] = {x = 16.0, y = 16.0, z = 1.0},
      [12] = {x = 16.0, y = 16.0, z = 1.0},
      [13] = {x = 16.0, y = 16.0, z = 1.0},
      [14] = {x = 16.0, y = 16.0, z = 1.0},
    },		
    Color = {r = 28, g = 252, b = 255},
		Marker= -1,
		Blip  = true,
		Name  = _U('wa_delivery_point'),
		Type  = "delivery",
		Spawner = 1,
		Item  = {
			{
			name   = _U('delivery'),
			time   = 800,
			remove = 1,
			max    = 100, -- if not present, probably an error at itemQtty >= item.max in esx_jobwa_sv.lua
			price  = Config.Prices.waterr,
			xp  = Config.Xp.waterr,
			requires = "wa_mohlaa",
			requirewa_name = _U('wa_packagechicken'),
			drop   = 100
			}
		},
		Hint  = _U('wa_deliver')
    }
  }
}
