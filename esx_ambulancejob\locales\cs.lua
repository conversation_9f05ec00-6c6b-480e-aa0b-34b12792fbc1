Locales['cs'] = {
  -- Cloakroom
  ['cloakroom'] = 'šatna',
  ['ems_clothes_civil'] = 'civilní oblečení',
  ['ems_clothes_ems'] = 'zdravotnické oblečení',
  -- Vehicles
  ['ambulance'] = 'ambulance',
  ['helicopter_prompt'] = 'press ~INPUT_CONTEXT~ to access the ~y~Helicopter Actions~s~.',
  ['garage_prompt'] = 'press ~INPUT_CONTEXT~ to access the ~y~Vehicle Actions~s~.',
  ['garage_title'] = 'vehicle Actions',
  ['garage_stored'] = 'stored',
  ['garage_notstored'] = 'not in garage',
  ['garage_storing'] = 'we\'re attempting to remove the vehicle, make sure no players are around it.',
  ['garage_has_stored'] = 'the vehicle has been stored in your garage',
  ['garage_has_notstored'] = 'no nearby owned vehicles were found',
  ['garage_notavailable'] = 'your vehicle is not stored in the garage.',
  ['garage_blocked'] = 'there\'s no available spawn points!',
  ['garage_empty'] = 'you dont have any vehicles in your garage.',
  ['garage_released'] = 'your vehicle has been released from the garage.',
  ['garage_store_nearby'] = 'there is no nearby vehicles.',
  ['garage_storeditem'] = 'open garage',
  ['garage_storeitem'] = 'store vehicle in garage',
  ['garage_buyitem'] = 'vehicle shop',
  ['shop_item'] = '$%s',
  ['vehicleshop_title'] = 'vehicle Shop',
  ['vehicleshop_confirm'] = 'do you want to buy this vehicle?',
  ['vehicleshop_bought'] = 'you have bought ~y~%s~s~ for ~g~$%s~s~',
  ['vehicleshop_money'] = 'you cannot afford that vehicle',
  ['vehicleshop_awaiting_model'] = 'the vehicle is currently ~g~DOWNLOADING & LOADING~s~ please wait',
  ['confirm_no'] = 'no',
  ['confirm_yes'] = 'yes',
  -- Action Menu
  ['revive_inprogress'] = 'probíhá oživení!',
  ['revive_complete'] = 'oživili jste ~y~%s~s~',
  ['revive_complete_award'] = 'oživili jste ~y~%s~s~ a dostali jste ~g~$%s~s~!',
  ['revive_fail_offline'] = 'that player is no longer online',
  ['heal_inprogress'] = 'uzdravujete!',
  ['heal_complete'] = 'uzdravili jste ~y~%s~s~',
  ['no_players'] = 'v okolí není žádný hráč!',
  ['player_not_unconscious'] = 'hráč není v bezvědomí!',
  ['player_not_conscious'] = 'hráč je v bezvědomí!',
  -- Boss Menu
  ['boss_actions'] = 'akce šéfa',
  -- Misc
  ['invalid_amount'] = '~r~neplatná částka',
  ['actions_prompt'] = 'press ~INPUT_CONTEXT~ access the ~y~Ambulance Actions~s~.',
  ['deposit_amount'] = 'částka vkladu',
  ['money_withdraw'] = 'částka výběru',
  ['fast_travel'] = 'stiskněte ~INPUT_CONTEXT~ k rychlému odcestování.',
  ['open_pharmacy'] = 'stiskněte ~INPUT_CONTEXT~ k otevření lékárny.',
  ['pharmacy_menu_title'] = 'lékárna',
  ['pharmacy_take'] = 'vzít ~y~%s~s~',
  ['medikit'] = 'lékarnička',
  ['bandage'] = 'obvaz',
  ['max_item'] = 'již nesete dostatek věcí.',
  -- F6 Menu
  ['ems_menu'] = 'Zdravotnické menu',
  ['ems_menu_title'] = 'záchranka - Zdravotnické menu',
  ['ems_menu_revive'] = 'oživit hráče',
  ['ems_menu_putincar'] = 'naložit do vozidla',
  ['ems_menu_small'] = 'ošetřit malé zranění',
  ['ems_menu_big'] = 'ošetřit vážné zranění',
  ['ems_menu_search'] = 'pacient nebyl nalezen',
  -- Phone
  ['alert_ambulance'] = 'zdravotnický poplach',
  -- Death
  ['respawn_available_in'] = 'oživení dostupné za ~b~%s minut a %s sekund~s~\n',
  ['respawn_bleedout_in'] = 'vykrvácíte za ~b~%s minut a %s sekund~s~\n',
  ['respawn_bleedout_prompt'] = 'držte [~b~E~s~] pro respawn.',
  ['respawn_now_fine'] = 'držte [~b~E~s~] pro oživení za ~g~$%s~s~',
  ['respawn_bleedout_fine_msg'] = 'zaplatili jste ~r~$%s~s~ za respawn.',
  ['distress_send'] = 'stiskněte [~b~G~s~] pro vyslání tísňového signálu',
  ['distress_sent'] = 'tísňový signál byl vyslán dostupným jednotkám!',
  ['combatlog_message'] = 'byli jste násilně oživeni, protože jste předtím opustili server, když jste byli mrtví.',
  -- Revive
  ['revive_help'] = 'oživit hráče',
  -- Item
  ['used_medikit'] = 'použili jste ~y~1x~s~ lékarničku',
  ['used_bandage'] = 'použili jste ~y~1x~s~ obvaz',
  ['not_enough_medikit'] = 'nemáte ~b~medikit~s~.',
  ['not_enough_bandage'] = 'nemáte~b~bandage~s~.',
  ['healed'] = 'byli jste ošetřeni.',
  -- Blips
  ['blip_hospital'] = 'nemocnice',
  ['blip_dead'] = 'unconscious player',
}
