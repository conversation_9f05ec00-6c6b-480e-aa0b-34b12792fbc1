Config = {}

-- Framework Configuration
Config.Framework = 'ESX' -- Choose from > 'QB' & 'ESX' (Leave as 'QB' if you are using QBX)

-- Target System Configuration (تم تعطيله واستبداله بنظام الضغط على E)
Config.Target = 'none' -- تم تغييره إلى نظام الضغط على E بدلاً من نظام العين

-- Config for Delivering Pizza Progress Bar
Config.DeliveryTime = 5000  -- Time in milliseconds for the progress bar to complete

-- XP Configuration
Config.XP = {
    min = 20, -- Minimum XP for very close deliveries
    max = 80, -- Maximum XP for very far deliveries
    baseAmount = 30, -- Base XP amount
    perMeterRate = 0.015 -- Additional XP per meter of distance
}
-- Legacy support (will be calculated automatically now)
Config.impoundxp = 20

-- Config for Payment on Delivery
Config.Pay = {
    min = 200, -- Minimum amount for each delivery (for very close deliveries)
    max = 1000, -- Maximum amount for each delivery (for very far deliveries)
    baseAmount = 400, -- Base payment amount
    perMeterRate = 0.3 -- Additional money per meter of distance
}

-- Distance-Based Rewards Configuration
Config.DistanceRewards = {
    enabled = true, -- Enable/disable distance-based rewards
    showDistance = true, -- Show distance in notifications
    debugMode = false -- Show debug information in console
}

-- XP Notification Configuration
Config.XPNotification = {
    enabled = true, -- Enable/disable XP notifications
    title = "مكافأة الخبرة", -- Notification title
    message = "حصلت على %d نقطة خبرة من الدلفري!", -- Message format (%d will be replaced with XP amount)
    distanceMessage = "حصلت على %d نقطة خبرة! (مسافة: %.0f متر)", -- Message with distance
    duration = 5000 -- Notification duration in milliseconds
}

-- Pizza Job Uniform Configuration
Config.Uniform = {
    enabled = true, -- Enable/disable automatic uniform giving
    giveOnLogin = false, -- Give uniform when player logs in (disabled - now given when starting delivery)

    -- Male uniform components
    male = {
        torso = { drawable = 4, texture = 0 }, -- Torso 4
        arms = { drawable = 41, texture = 0 }, -- Arms/Hands 41 (تم تغييرها)
        legs = { drawable = 4, texture = 0 }, -- Pants 4
        shoes = { drawable = 10, texture = 0 }, -- Shoes 10
        hat = { drawable = 10, texture = 2 } -- Hat 10, texture 2
    },

    -- Female uniform components
    female = {
        torso = { drawable = 4, texture = 0 }, -- Torso 4
        arms = { drawable = 41, texture = 0 }, -- Arms/Hands 41 (تم تغييرها)
        legs = { drawable = 4, texture = 0 }, -- Pants 4
        shoes = { drawable = 10, texture = 0 }, -- Shoes 10
        hat = { drawable = 10, texture = 2 } -- Hat 10, texture 2
    },

    -- Notification settings
    notification = {
        enabled = true,
        title = "زي العمل",
        message = "تم إعطاؤك زي الدلفري!",
        duration = 4000
    }
}

-- Job Restriction Configuration
Config.JobRestriction = {
    enabled = true, -- Set to true to restrict to job holders only
    jobName = 'pizzajob', -- Job name required to access the pizza delivery
    allowedJobs = {'pizzajob', 'admin', 'mechanic'} -- الرتب المسموحة لركوب دباب الدلفري
}

-- Delivery Bot Configuration
Config.DeliveryBot = {
    model = "a_m_m_business_01", -- Bot model at delivery locations
    label = "تسليم الطلب", -- Target label for delivery
    distance = 3.5 -- زيادة المسافة من 2.0 إلى 3.5 متر
}

-- Delivery Area Circle Configuration
Config.DeliveryCircle = {
    enabled = true, -- Enable/disable yellow circle around delivery location
    radius = 50.0, -- Radius of the circle in meters
    color = {r = 255, g = 255, b = 0, a = 100}, -- Yellow color with transparency
    thickness = 2.0, -- Thickness of the circle border
    showOnMap = true, -- Show circle on map (as radius blip)
    showIn3D = true, -- Show circle in 3D world (as ground marker)

    -- Text UI settings
    showDistanceUI = true, -- Show distance to delivery area
    showInsideUI = true, -- Show UI when inside delivery area

    -- Notification settings
    showNotification = true, -- Show notification when delivery area is created
    notificationDuration = 5000 -- Duration of notification in milliseconds
}

-- Vehicle Return Location
Config.VehicleReturn = {
    location = vector3(-1190.0, -890.0, 13.8),
    label = "تسليم الدباب",
    distance = 3.0
}

-- Ped Configuration
Config.Ped = {
    location = vector4(-1194.93, -894.138, 13.886, 338.2341),
    model = "s_m_y_chef_01",
}

-- Blip Configuration (No separate location, uses ped location)
Config.Blip = {
    sprite = 488,
    color = 1,
    size = 0.8,
    label = "الدلفري",
}

-- Target Settings for the Ped
Config.TargetSettings = {
    label = "تحدث مع الطباخ",
    distance = 3.0, -- زيادة المسافة من 1.5 إلى 3.0 متر
    size = 1.0,
}

-- Vehicle Insurance Configuration
Config.VehicleInsurance = {
    enabled = true, -- Enable/disable vehicle insurance fee
    amount = 2000, -- Insurance amount to charge when starting delivery
    refundOnReturn = false, -- Set to true if you want to refund insurance when returning vehicle

    -- Notification messages
    messages = {
        notEnoughMoney = "تحتاج إلى $%d كتأمين للدراجة",
        charged = "تم خصم $%d كتأمين للدراجة",
        refunded = "تم استرداد $%d من تأمين الدراجة"
    }
}

-- Delivery Settings
Config.Deliveries = {
    vehicle = 'enduro',
    spawnLocation = vector4(-1174.19, -873.137, 13.629, 120.0),

    -- Delivery Locations
    locations = {
        [1] = {
            location = vector3(206.4080, -86.0276, 69.3822),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [2] = {
            location = vector3(329.3652, -225.2551, 54.2218),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [3] = {
            location = vector3(1303.2076, -527.3721, 71.4606),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [4] = {
            location = vector3(1328.6552, -536.0223, 72.4408),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [5] = {
            location = vector3(1348.2975, -546.7674, 73.8913),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [6] = {
            location = vector3(1373.2633, -555.7613, 74.6857),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [7] = {
            location = vector3(1388.9089, -569.5203, 74.4957),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [8] = {
            location = vector3(1386.2338, -593.4677, 74.4839),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [9] = {
            location = vector3(1367.2441, -606.6927, 74.7109),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [10] = {
            location = vector3(1341.3439, -597.2584, 74.7009),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [11] = {
            location = vector3(1323.4661, -583.1940, 73.2447),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [12] = {
            location = vector3(1301.0762, -574.2172, 71.7322),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [13] = {
            location = vector3(331.4731, 465.2437, 151.2560),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [14] = {
            location = vector3(315.6923, 502.0507, 153.1797),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [15] = {
            location = vector3(324.9917, 537.1765, 153.8885),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [16] = {
            location = vector3(224.0782, 513.4530, 140.9175),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [17] = {
            location = vector3(107.0312, 466.7024, 147.5619),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [18] = {
            location = vector3(79.9463, 486.2843, 148.2014),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [19] = {
            location = vector3(57.5254, 449.5797, 147.0729),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        },
        [20] = {
            location = vector3(43.0662, 468.8459, 148.0959),
            heading = 180.0  -- مواجه للجنوب (مواجه للاعب)
        }
        -- Add more locations here
    }
}
