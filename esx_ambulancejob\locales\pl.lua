Locales['pl'] = {
  -- Cloakroom
  ['cloakroom'] = 'szatnia',
  ['ems_clothes_civil'] = 'ubrania cywilne',
  ['ems_clothes_ems'] = 'ubrania EMS',
  -- Vehicles
  ['ambulance'] = 'ambulance',
  ['helicopter_prompt'] = 'press ~INPUT_CONTEXT~ to access the ~y~Helicopter Actions~s~.',
  ['garage_prompt'] = 'press ~INPUT_CONTEXT~ to access the ~y~Vehicle Actions~s~.',
  ['garage_title'] = 'vehicle Actions',
  ['garage_stored'] = 'stored',
  ['garage_notstored'] = 'not in garage',
  ['garage_storing'] = 'we\'re attempting to remove the vehicle, make sure no players are around it.',
  ['garage_has_stored'] = 'the vehicle has been stored in your garage',
  ['garage_has_notstored'] = 'no nearby owned vehicles were found',
  ['garage_notavailable'] = 'your vehicle is not stored in the garage.',
  ['garage_blocked'] = 'there\'s no available spawn points!',
  ['garage_empty'] = 'you dont have any vehicles in your garage.',
  ['garage_released'] = 'your vehicle has been released from the garage.',
  ['garage_store_nearby'] = 'there is no nearby vehicles.',
  ['garage_storeditem'] = 'open garage',
  ['garage_storeitem'] = 'store vehicle in garage',
  ['garage_buyitem'] = 'vehicle shop',
  ['shop_item'] = '$%s',
  ['vehicleshop_title'] = 'vehicle Shop',
  ['vehicleshop_confirm'] = 'do you want to buy this vehicle?',
  ['vehicleshop_bought'] = 'you have bought ~y~%s~s~ for ~g~$%s~s~',
  ['vehicleshop_money'] = 'you cannot afford that vehicle',
  ['vehicleshop_awaiting_model'] = 'the vehicle is currently ~g~DOWNLOADING & LOADING~s~ please wait',
  ['confirm_no'] = 'no',
  ['confirm_yes'] = 'yes',
  -- Action Menu
  ['revive_inprogress'] = 'trwa wskrzeszenie',
  ['revive_complete'] = 'zostałeś wskrzeszony ~y~%s~s~',
  ['revive_complete_award'] = 'zostałeś wskrzeszony ~y~%s~s~ i zarobiono ~g~$%s~s~!',
  ['revive_fail_offline'] = 'that player is no longer online',
  ['heal_inprogress'] = 'leczysz!',
  ['heal_complete'] = 'zostałeś uleczony ~y~%s~s~',
  ['no_players'] = 'brak graczy w pobliżu',
  ['player_not_unconscious'] = 'ten gracz nie jest nieprzytomny!',
  ['player_not_conscious'] = 'ten gracz nie jest świadomy!',
  -- Boss Menu
  ['boss_actions'] = 'akcje szefa',
  -- Misc
  ['invalid_amount'] = '~r~Nieprawidłowa kwota',
  ['actions_prompt'] = 'press ~INPUT_CONTEXT~ access the ~y~Ambulance Actions~s~.',
  ['deposit_amount'] = 'kwota depozytu',
  ['money_withdraw'] = 'wycofana kwota',
  ['fast_travel'] = 'naciśnij na ~INPUT_CONTEXT~ do szybkiej podróży.',
  ['open_pharmacy'] = 'naciśnij na ~INPUT_CONTEXT~ aby otworzyć aptekę',
  ['pharmacy_menu_title'] = 'apteka',
  ['pharmacy_take'] = 'weź <span style="color:blue;">%s</span>',
  ['medikit'] = 'apteczka',
  ['bandage'] = 'bandaż',
  ['max_item'] = 'już masz na sobie dość.',
  -- F6 Menu
  ['ems_menu'] = 'menu EMS',
  ['ems_menu_title'] = 'eMS Menu - Ambulans',
  ['ems_menu_revive'] = 'ożyw gracza',
  ['ems_menu_putincar'] = 'umieścić w pojeździe',
  ['ems_menu_small'] = 'ulecz małe rany',
  ['ems_menu_big'] = 'ulecz poważne obrażenia',
  ['ems_menu_search'] = 'nie znaleziono pacjenta',
  -- Phone
  ['alert_ambulance'] = 'wezwij ambulans',
  -- Death
  ['respawn_available_in'] = 'respawn available in ~b~%s minutes %s seconds~s~',
  ['respawn_bleedout_in'] = 'you will bleed out in ~b~%s minutes %s seconds~s~\n',
  ['respawn_bleedout_prompt'] = 'hold [~b~E~s~] to respawn',
  ['respawn_bleedout_fine'] = 'hold [~b~E~s~] to respawn for ~g~$%s~s~',
  ['respawn_bleedout_fine_msg'] = 'you paid ~r~$%s~s~ to respawn.',
  ['distress_send'] = 'press [~b~G~s~] to send distress signal',
  ['distress_sent'] = 'distress signal has been sent to available units!',
  ['combatlog_message'] = 'you have been force-respawned because you\'ve previously left the server when dead.',
  -- Revive
  ['revive_help'] = 'ożyw gracza',
  -- Item
  ['used_medikit'] = 'użyłeś 1x Apteczki',
  ['used_bandage'] = 'użyłeś 1x Bandaża',
  ['not_enough_medikit'] = 'nie posiadasz ~b~apteczki~s~.',
  ['not_enough_bandage'] = 'nie posiadasz ~b~bandaża~s~.',
  ['healed'] = 'zostałeś potraktowany',
  -- Blips
  ['blip_hospital'] = 'szpital',
  ['blip_dead'] = 'unconscious player',
}
