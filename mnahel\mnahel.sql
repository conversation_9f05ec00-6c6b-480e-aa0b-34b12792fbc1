-- إنشاء وظيفة المناحل (بدون عمود xp)
INSERT INTO `jobs` (`name`, `label`, `whitelisted`) VALUES
	('mnahel', 'مناحل العسل الذهبي', 0)
ON DUPLICATE KEY UPDATE `label` = 'مناحل العسل الذهبي';

-- إنشاء رتبة الوظيفة (رتبة واحدة فقط)
INSERT INTO `job_grades` (`job_name`, `grade`, `name`, `label`, `salary`, `skin_male`, `skin_female`) VALUES
	('mnahel', 0, 'beekeeper', 'نحال', 400, '{"tshirt_2":0,"ears_1":5,"glasses_1":0,"torso_2":8,"ears_2":3,"glasses_2":0,"shoes_2":0,"pants_1":90,"shoes_1":24,"bags_1":0,"helmet_2":1,"pants_2":8,"torso_1":0,"tshirt_1":15,"arms":41,"bags_2":0,"helmet_1":13}', '{"tshirt_2":1,"ears_1":5,"glasses_1":0,"torso_2":1,"ears_2":3,"glasses_2":0,"shoes_2":0,"pants_1":93,"shoes_1":52,"bags_1":0,"helmet_2":1,"pants_2":0,"torso_1":0,"tshirt_1":6,"arms":46,"bags_2":0,"helmet_1":2}')
ON DUPLICATE KEY UPDATE `label` = 'نحال', `salary` = 400;

-- إنشاء عناصر المناحل (مع تجنب التكرار)
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
	('mnahel_toolbox', 'صندوق عدة النحال', 20, 0, 1),
	('mnahel_bottle', 'عسل طبيعي', 3, 0, 1),
	('mnahel_box', 'علبة عسل جاهزة للبيع', 2, 0, 1),
	('mnahel_bucket', 'عسل خام', 4, 0, 1),
	('mnahel_tool', 'عدة النحال', 4, 0, 1)
ON DUPLICATE KEY UPDATE
	`label` = VALUES(`label`),
	`weight` = VALUES(`weight`);
