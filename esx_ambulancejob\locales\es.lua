-- Traducción by https://github.com/ItsManueh

Locales['es'] = {
  -- Cloakroom
  ['cloakroom'] = 'Guardarropa',
  ['ems_clothes_civil'] = 'Camiseta',
  ['ems_clothes_ems'] = 'Equipo de ambulancia',
  -- Vehicles
  ['ambulance'] = 'Ambulancia',
  ['helicopter_prompt'] = 'Presiona ~INPUT_CONTEXT~ para acceder al ~y~Acciones de helicópteros~s~.',
  ['garage_prompt'] = 'Presiona ~INPUT_CONTEXT~ para acceder al ~y~Acciones de vehículos~s~.',
  ['garage_title'] = 'Acciones de vehículos',
  ['garage_stored'] = 'Almacenado',
  ['garage_notstored'] = 'No en el garaje',
  ['garage_storing'] = 'Estamos intentando retirar el vehículo, asegúrese de que no haya jugadores a su alrededor.',
  ['garage_has_stored'] = 'El vehículo se ha guardado en su garaje',
  ['garage_has_notstored'] = 'No se encontraron vehículos de propiedad cercana',
  ['garage_notavailable'] = 'Su vehículo no está guardado en el garaje.',
  ['garage_blocked'] = '¡No hay puntos de generación disponibles!',
  ['garage_empty'] = 'No tiene ningún vehículo en su garaje.',
  ['garage_released'] = 'Su vehículo ha sido sacado del garaje.',
  ['garage_store_nearby'] = 'No hay vehículos cercanos.',
  ['garage_storeditem'] = 'Garaje abierto',
  ['garage_storeitem'] = 'Almacenar vehículo en garaje',
  ['garage_buyitem'] = 'Tienda de vehículos',
  ['shop_item'] = '$%s',
  ['vehicleshop_title'] = 'Tienda de vehículos',
  ['vehicleshop_confirm'] = '¿Quieres comprar este vehículo?',
  ['vehicleshop_bought'] = 'Usted ha comprado ~y~%s~s~ para ~g~$%s~s~',
  ['vehicleshop_money'] = 'No puedes pagar ese vehículo',
  ['vehicleshop_awaiting_model'] = 'El vehículo está actualmente ~g~DESCARGANDO & CARGANDO~s~ por favor espere.',
  ['confirm_no'] = 'No',
  ['confirm_yes'] = 'Si',
  -- Action Menu
  ['revive_inprogress'] = 'Reanimación en curso',
  ['revive_complete'] = 'Has sido reanimado ~y~%s~s~',
  ['revive_complete_award'] = 'Has sido reanimado ~y~%s~s~, ~g~$%s~s~',
  ['revive_fail_offline'] = 'Ese jugador ya no está en línea',
  ['heal_inprogress'] = '!Te estas curando!',
  ['heal_complete'] = 'Te has curado ~y~%s~s~',
  ['no_players'] = 'Ningún jugador cerca',
  ['player_not_unconscious'] = 'n\'Estás inconsciente',
  ['player_not_conscious'] = '¡Ese jugador no está consciente!',
  -- Boss Menu
  ['boss_actions'] = 'Acciones del jefe',
  -- Misc
  ['invalid_amount'] = '~r~Cantidad no válida',
  ['actions_prompt'] = 'Presiona ~INPUT_CONTEXT~ acceder al ~y~Acciones de ambulancia~s~.',
  ['deposit_amount'] = 'Cantidad de fianza depositada',
  ['money_withdraw'] = 'Cantidad de fianza retirada',
  ['fast_travel'] = 'Presiona ~INPUT_CONTEXT~ para viajar rápido.',
  ['open_pharmacy'] = 'Presiona ~INPUT_CONTEXT~ para abrir la farmacia.',
  ['pharmacy_menu_title'] = 'Farmacia',
  ['pharmacy_take'] = 'tomar <span style="color:blue;">%s</span>',
  ['medikit'] = 'Kit médico',
  ['bandage'] = 'Vendas',
  ['max_item'] = 'Ya has ocupado el máximo número de items disponibles.',
  -- F6 Menu
  ['ems_menu'] = 'ayuda ciudadana',
  ['ems_menu_title'] = 'ambulancia - Ayuda Ciudadana',
  ['ems_menu_revive'] = 'reanimar',
  ['ems_menu_putincar'] = 'meter en el vehículo',
  ['ems_menu_small'] = 'Curar pequeñas heridas',
  ['ems_menu_big'] = 'Tratar lesiones graves',
  ['ems_menu_search'] = 'Paciente no encontrado',
  -- Phone
  ['alert_ambulance'] = 'Alerta de ambulancia',
  -- Death
  ['respawn_available_in'] = 'Reaparición disponible en ~b~%s minutes %s seconds~s~',
  ['respawn_bleedout_in'] = 'te desangrarás en ~b~%s minutes %s seconds~s~\n',
  ['respawn_bleedout_prompt'] = 'Mantén [~b~E~s~] reaparecer',
  ['respawn_bleedout_fine'] = 'Mantén [~b~E~s~] para reaparecer ~g~$%s~s~',
  ['respawn_bleedout_fine_msg'] = 'Tu pagaste ~r~$%s~s~ para reaparecer.',
  ['distress_send'] = 'Presiona [~b~G~s~] para enviar señal de socorro',
  ['distress_sent'] = 'Se ha enviado una señal de socorro a las unidades disponibles.',
  ['combatlog_message'] = 'Ha sido reaparecido a la fuerza porque anteriormente abandonó el servidor cuando estaba muerto.',
  -- Revive
  ['revive_help'] = 'Revivir un jugador',
  -- Item
  ['used_medikit'] = 'Has usado ~y~1x~s~ Kit médico',
  ['used_bandage'] = 'Has usado ~y~1x~s~ Vendas',
  ['not_enough_medikit'] = 'Usted no tiene ~b~Kit médico~s~.',
  ['not_enough_bandage'] = 'Usted no tiene ~b~Vendas~s~.',
  ['healed'] = 'Has sido tratado.',
  -- Blips
  ['blip_hospital'] = 'Hospital',
  ['blip_dead'] = 'Jugador inconsciente',
}
