Locales['fr'] = {
  -- Cloakroom
  ['cloakroom'] = 'vestiaire',
  ['ems_clothes_civil'] = 'tenue Civil',
  ['ems_clothes_ems'] = 'tenue Ambulancier',
  -- Vehicles
  ['ambulance'] = 'ambulance',
  ['helicopter_prompt'] = 'appuyez sur ~INPUT_CONTEXT~ pour accéder aux ~y~Actions de l\'hélicoptère~s~.',
  ['garage_prompt'] = 'appuyez sur ~INPUT_CONTEXT~ pour accéder aux ~y~Actions des véhciules~s~.',
  ['garage_title'] = 'actions véhicules',
  ['garage_stored'] = 'rangé',
  ['garage_notstored'] = 'en dehors du garage',
  ['garage_storing'] = 'tentative de suppression du véhicule, assurez-vous que personne ne soit autour.',
  ['garage_has_stored'] = 'le véhicule a bien été rangé dans le garage',
  ['garage_has_notstored'] = 'aucun véhicule dans le garage',
  ['garage_notavailable'] = 'votre véhicule n\'est pas rangé dans le garage.',
  ['garage_blocked'] = 'la sortie du garage est obstruée!',
  ['garage_empty'] = 'vous n\'avez aucun véhicule dans le garage.',
  ['garage_released'] = 'votre véhicule a été sorti.',
  ['garage_store_nearby'] = 'aucun véhicule a proximité.',
  ['garage_storeditem'] = 'ouvrir le garage',
  ['garage_storeitem'] = 'ranger le véhicule',
  ['garage_buyitem'] = 'magasin véhicule',
  ['shop_item'] = '$%s',
  ['vehicleshop_title'] = 'magasin véhicule',
  ['vehicleshop_confirm'] = 'voulez-vous acheter ce véhicule?',
  ['vehicleshop_bought'] = 'vous avez acheté ~y~%s~s~ pour ~g~$%s~s~',
  ['vehicleshop_money'] = 'vous ne pouvez pas acheter ce véhicule',
  ['vehicleshop_awaiting_model'] = 'le véhicule est actuellement en ~g~PRÉPARATION~s~ veuillez patienter',
  ['confirm_no'] = 'non',
  ['confirm_yes'] = 'oui',
  -- Action Menu
  ['revive_inprogress'] = 'réanimation en cours',
  ['revive_complete'] = 'vous avez réanimé ~y~%s~s~',
  ['revive_complete_award'] = 'vous avez réanimé ~y~%s~s~, ~g~$%s~s~',
  ['revive_fail_offline'] = 'ce joueur n\'est plus en ligne',
  ['heal_inprogress'] = 'vous soignez...',
  ['heal_complete'] = 'vous avez soigné ~y~%s~s~',
  ['no_players'] = 'aucun joueur à proximité',
  ['player_not_unconscious'] = 'n\'est pas inconscient',
  ['player_not_conscious'] = 'Cette personne est inconsciente!',
  -- Boss Menu
  ['boss_actions'] = 'action Patron',
  -- Misc
  ['invalid_amount'] = '~r~montant invalide~s~',
  ['actions_prompt'] = 'appuyez sur ~INPUT_CONTEXT~ accédez aux ~y~Actions d\'Ambulance~s~.',
  ['deposit_amount'] = 'montant du dépôt',
  ['money_withdraw'] = 'montant du retrait',
  ['fast_travel'] = 'appuyez sur ~INPUT_CONTEXT~ pour vous déplacer rapidement.',
  ['open_pharmacy'] = 'appuyez sur ~INPUT_CONTEXT~ pour ouvrir la pharmacie.',
  ['pharmacy_menu_title'] = 'Pharmacie',
  ['pharmacy_take'] = 'prendre <span style="color:blue;">%s</span>',
  ['medikit'] = 'kit de soin',
  ['bandage'] = 'bandage',
  ['max_item'] = 'vous en portez déjà assez sur vous.',
  -- F6 Menu
  ['ems_menu'] = 'interaction citoyen',
  ['ems_menu_title'] = 'ambulance - Interactions Citoyen',
  ['ems_menu_revive'] = 'réanimer',
  ['ems_menu_putincar'] = 'mettre dans véhicule',
  ['ems_menu_small'] = 'soigner petites blessures',
  ['ems_menu_big'] = 'soigner blessures graves',
  ['ems_menu_search'] = 'patient introuvable',
  -- Phone
  ['alert_ambulance'] = 'alerte Ambulance',
  -- Death
  ['respawn_available_in'] = 'réanimation possible dans ~b~%s minutes %s secondes~s~',
  ['respawn_bleedout_in'] = 'vous allez souffrir d\'une hémorragie dans ~b~%s minutes %s secondes~s~\n',
  ['respawn_bleedout_prompt'] = 'maintenez [~b~E~s~] pour être réanimé',
  ['respawn_bleedout_fine'] = 'maintenez [~b~E~s~] pour être réanimé pour ~g~$%s~s~',
  ['respawn_bleedout_fine_msg'] = 'vous avez payé ~r~$%s~s~ pour être réanimer.',
  ['distress_send'] = 'appuyez sur [~b~G~s~] pour envoyer un signal de détresse',
  ['distress_sent'] = 'un signal a été envoyé à toutes les unités disponibles!',
  ['combatlog_message'] = 'vous avez été réanimé de force car vous avez quitté le serveur.',
  -- Revive
  ['revive_help'] = 'relancer un joueur',
  -- Item
  ['used_medikit'] = 'vous avez utilisé 1x kit de soin',
  ['used_bandage'] = 'vous avez utilisé 1x bandage',
  ['not_enough_medikit'] = 'vous n\'avez pas de ~b~kit de soin~s~.',
  ['not_enough_bandage'] = 'vous n\'avez pas de ~b~bandage~s~.',
  ['healed'] = 'vous avez été soigné.',
  -- Blips
  ['blip_hospital'] = 'hôpital',
  ['blip_dead'] = 'joueur inconscient',
}
