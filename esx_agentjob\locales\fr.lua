Locales['fr'] = {
  -- Cloakroom
  ['cloakroom'] = 'vestiaire',
  ['citizen_wear'] = 'tenue Civil',
  ['police_wear'] = 'tenue Policier',
  ['gilet_wear'] = 'gilet orange',
  ['bullet_wear'] = 'gilet pare-balles',
  ['no_outfit'] = 'il n\'y a pas d\'uniforme à votre taille...',
  ['open_cloackroom'] = 'appuyez sur ~INPUT_CONTEXT~ pour vous changer',
  -- Armory
  ['remove_object'] = 'prendre Objet',
  ['deposit_object'] = 'déposer objet',
  ['get_weapon'] = 'prendre Arme',
  ['put_weapon'] = 'déposer Arme',
  ['buy_weapons'] = 'acheter Armes',
  ['armory'] = 'armurerie',
  ['open_armory'] = 'appuyez sur ~INPUT_CONTEXT~ pour accéder à l\'armurerie',
  ['armory_owned'] = 'possédé',
  ['armory_free'] = 'gratuit',
  ['armory_item'] = '$%s',
  ['armory_weapontitle'] = 'armurerie - Acheter une arme',
  ['armory_componenttitle'] = 'armurerie - Accessoires d\'armes',
  ['armory_bought'] = 'vous achetez un ~y~%s~s~ pour ~g~$%s~s~',
  ['armory_money'] = 'vous ne pouvez pas acheter cette arme',
  ['armory_hascomponent'] = 'vous avez cet accessoire équipé!',
  ['get_weapon_menu'] = 'armurerie - Retirer arme',
  ['put_weapon_menu'] = 'armurerie - Stocker arme',
  -- Vehicles
  ['vehicle_menu'] = 'véhicule',
  ['vehicle_blocked'] = 'tous les points de spawn sont bloqués!',
  ['garage_prompt'] = 'appuyez sur ~INPUT_CONTEXT~ pour accéder aux ~y~Actions Véhicule~s~.',
  ['garage_title'] = 'actions Véhicules',
  ['garage_stored'] = 'rangé',
  ['garage_notstored'] = 'sorti(e)',
  ['garage_storing'] = 'tentative de suppression du véhicule, assurez-vous que personne ne soit autour.',
  ['garage_has_stored'] = 'le véhicule a bien été rangé dans le garage',
  ['garage_has_notstored'] = 'aucun véhicule dans le garage',
  ['garage_notavailable'] = 'votre véhicule n\'est pas rangé dans le garage.',
  ['garage_blocked'] = 'la sortie du garage est obstruée!',
  ['garage_empty'] = 'vous n\'avez aucun véhicule dans le garage.',
  ['garage_released'] = 'votre véhicule a été sorti.',
  ['garage_store_nearby'] = 'aucun véhicule a proximité.',
  ['garage_storeditem'] = 'ouvrir le garage',
  ['garage_storeitem'] = 'ranger le véhicule',
  ['garage_buyitem'] = 'magasin véhicule',
  ['garage_notauthorized'] = 'vous n\'êtes pas autorisé à acheter ce type de véhicules.',
  ['helicopter_prompt'] = 'appuyez sur ~INPUT_CONTEXT~ pour accéder aux ~y~Actions de l\'hélicoptère~s~.',
  ['shop_item'] = '$%s',
  ['vehicleshop_title'] = 'magasin véhicule',
  ['vehicleshop_confirm'] = 'voulez-vous acheter ce véhicule?',
  ['vehicleshop_bought'] = 'vous avez acheté ~y~%s~s~ pour ~r~$%s~s~',
  ['vehicleshop_money'] = 'vous ne pouvez pas acheter ce véhicule',
  ['vehicleshop_awaiting_model'] = 'le véhicule est actuellement en ~g~PRÉPARATION~s~ veuillez patienter',
  ['confirm_no'] = 'non',
  ['confirm_yes'] = 'oui',
  -- Service
  ['service_max'] = 'vous ne pouvez pas entrer en service, officiers en service: %s/%s',
  ['service_not'] = 'vous n\'êtes pas en service! Vous devez d\'abord enfiler votre tenue.',
  ['service_anonunce'] = 'prise de service',
  ['service_in'] = 'vous êtes en service, bon courage!',
  ['service_in_announce'] = 'l\'officier ~y~%s~s~ est entré en service!',
  ['service_out'] = 'vous avez terminé votre service.',
  ['service_out_announce'] = 'l\'officier ~y~%s~s~ a quitté son service.',
  -- Action Menu
  ['citizen_interaction'] = 'interaction citoyen',
  ['vehicle_interaction'] = 'interaction véhicule',
  ['object_spawner'] = 'placer objets',

  ['id_card'] = 'carte d\'identité',
  ['search'] = 'fouiller',
  ['handcuff'] = 'menotter / Démenotter',
  ['drag'] = 'escorter',
  ['put_in_vehicle'] = 'mettre dans véhicule',
  ['out_the_vehicle'] = 'sortir du véhicule',
  ['fine'] = 'Amende',
  ['unpaid_bills'] = 'gérer les amendes impayées',
  ['license_check'] = 'gérer les licences',
  ['license_revoke'] = 'révoquer la licence',
  ['license_revoked'] = 'votre ~b~%s~s~ a été ~y~révoqué~s~!',
  ['licence_you_revoked'] = 'vous avez révoqué un ~b~%s~s~ qui appartenait à ~y~%s~s~',
  ['no_players_nearby'] = 'aucun joueur à proximité',
  ['being_searched'] = 'vous êtes ~y~recherché(e)~s~ par la ~b~Police~s~',
  -- Vehicle interaction
  ['vehicle_info'] = 'infos véhicule',
  ['pick_lock'] = 'crocheter véhicule',
  ['vehicle_unlocked'] = 'véhicule ~g~déverouillé~s~',
  ['no_vehicles_nearby'] = 'aucun véhicule à proximité',
  ['impound'] = 'véhicule en fourrière',
  ['impound_prompt'] = 'appuyez sur ~INPUT_CONTEXT~ pour annuler la ~y~saisie du véhicule~s~',
  ['impound_canceled'] = 'vous avez annulé la saisie',
  ['impound_canceled_moved'] = 'la saisie a été annulée parce que le véhicule a déménagé',
  ['impound_successful'] = 'vous avez saisi le véhicule',
  ['search_database'] = 'vehicle information',
  ['search_database_title'] = 'informations sur le véhicule - recherche avec numéro d\'enregistrement',
  ['search_database_error_invalid'] = 'Ce n\'est ~r~pas~s~ un ~y~numéro d\'enregistrement valide~s~',
  -- Traffic interaction
  ['traffic_interaction'] = 'interaction routière',
  ['cone'] = 'plot',
  ['barrier'] = 'barrière',
  ['spikestrips'] = 'herse',
  ['box'] = 'caisse',
  ['cash'] = 'caisse',
  -- ID Card Menu
  ['name'] = 'nom: %s',
  ['job'] = 'métier: %s',
  ['sex'] = 'sexe: %s',
  ['dob'] = 'DDN: %s',
  ['height'] = 'taille: %s',
  ['bac'] = 'BAC: %s',
  ['unknown'] = 'inconnu',
  ['male'] = 'homme',
  ['female'] = 'femme',
  -- Body Search Menu
  ['guns_label'] = '--- Armes ---',
  ['inventory_label'] = '--- Inventaire ---',
  ['license_label'] = ' --- Licenses ---',
  ['confiscate'] = 'confisquer %s',
  ['confiscate_weapon'] = 'confisqué %s avec %s balles',
  ['confiscate_inv'] = 'confisquer %sx %s',
  ['confiscate_dirty'] = 'confisquer argent sale: <span style="color:red;">€%s</span>',
  ['you_confiscated'] = 'vous avez confisqué ~y~%sx~s~ ~b~%s~s~ à ~b~%s~s~',
  ['got_confiscated'] = '~y~%sx~s~ ~b~%s~s~ ont été confisqués par ~y~%s~s~',
  ['you_confiscated_account'] = 'vous avez confisqué ~g~$%s~s~ (%s) à ~b~%s~s~',
  ['got_confiscated_account'] = '~g~$%s~s~ (%s) ont été confisqués par ~y~%s~s~',
  ['you_confiscated_weapon'] = 'vous avez confisqué ~b~%s~s~ à ~b~%s~s~ avec ~o~%s~s~ balles',
  ['got_confiscated_weapon'] = 'votre ~b~%s~s~ avec ~o~%s~s~ balles a été confisqué par ~y~%s~s~',
  ['traffic_offense'] = 'code de la route',
  ['minor_offense'] = 'délit mineur',
  ['average_offense'] = 'délit moyen',
  ['major_offense'] = 'délit grave',
  ['fine_total'] = 'amende: %s',
  -- Vehicle Info Menu
  ['plate'] = 'n°: %s',
  ['owner_unknown'] = 'propriétaire: Inconnu',
  ['owner'] = 'propriétaire: %s',
  -- Boss Menu
  ['open_bossmenu'] = 'appuyez sur ~INPUT_CONTEXT~ pour ouvrir le menu',
  ['quantity_invalid'] = 'quantité invalide',
  ['have_withdrawn'] = 'vous avez retiré ~y~%sx~s~ ~b~%s~s~',
  ['have_deposited'] = 'vous avez déposé ~y~%sx~s~ ~b~%s~s~',
  ['quantity'] = 'quantité',
  ['inventory'] = 'inventaire',
  ['police_stock'] = 'coffre de la police',
  -- Misc
  ['remove_prop'] = 'appuyez sur ~INPUT_CONTEXT~ pour enlever l\'objet',
  ['map_blip'] = 'Commissariat',
  ['unrestrained_timer'] = 'vous sentez que vos menottes deviennent fragiles.',
  -- Notifications
  ['alert_police'] = 'alerte police',
  ['phone_police'] = 'police',
}
